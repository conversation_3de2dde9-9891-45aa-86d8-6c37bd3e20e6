<h1 align="center">林风婚恋交友商业版 v1.10.0 一体化说明</h1>

项目包含：后端服务（Java Spring Boot 多模块）、管理后台前端（Vue3 + Element Plus）、移动端前端（UniApp，支持 H5/小程序/APP）。本 README 作为统一的使用手册与结构说明。

## 一、功能总览
- 嘉宾：推荐、打招呼、历史回看、我喜欢/喜欢我
- 动态：发布、评论、点赞、举报、话题
- 认证：实名认证、学历、工作
- 聊天（IM）：好友、会话、聊天、礼物
- 配置：全局与业务配置
- 用户：注册登录、会员
- 花瓣：虚拟币充值、解锁
- 漂流瓶：捡瓶子、会话
- 签到任务：新手/日常任务与奖励
- 线下活动：发布、报名、退款、公告、回顾
- 账户提现：提现、花瓣兑换
- 网关：接口加解密（可选）

## 二、技术栈
- 后端：Spring Boot 2.2.x、MyBatis-Plus、Spring Security、RabbitMQ、Netty、WebSocket、MySQL、Redis、JWT、Druid、Quartz、Swagger、Lombok、Hutool、MinIO、WxJava
- 管理后台：Vue 3、Element Plus、Axios、Vue Router、Vuex、ECharts
- 移动端：UniApp（Vue3）、uview-ui、mp-html、录音/加密等三方组件

## 三、目录与模块
```
linfeng-love-backend/           # 后端根目录（Maven 多模块）
  ├─ linfeng-love-app          # 移动端 API（默认端口 8080，Netty WS 8081）
  ├─ linfeng-love-sys          # 管理后台 API（默认端口 8082）
  ├─ linfeng-love-gateway      # 网关（数据加密，可选）
  ├─ linfeng-love-service      # 业务服务/DAO 与缓存
  ├─ linfeng-love-common       # 公共模块
  ├─ linfeng-love-push         # 推送模块（MQ/WebSocket）
  ├─ linfeng-love-push-client  # 推送客户端（MQ 生产者）
  ├─ linfeng-love-transport    # 三方服务封装
  └─ linfeng-love-parent       # 依赖管理（Spring Boot 2.2.4，JDK 1.8）

linfeng-love-web/               # 管理后台前端（Vue3 + Element Plus）
  └─ config/index.js           # 后端接口地址（DEV/PROD）

linfeng-love-uniapps/           # 移动端前端（UniApp）
  ├─ pages.json                # 路由/Tab 配置
  ├─ manifest.json             # 平台与打包配置
  └─ utils/config.js           # 接口域名、AES、WebSocket 配置

sql/                            # 数据库 SQL（全量与增量）
shell/                          # 便捷启动/关闭脚本（Linux 服务器）
```

## 四、环境要求
- JDK 1.8
- Maven 3.6+
- MySQL 8.x（默认本地，无密码示例）
- Redis（默认 127.0.0.1:6379，无密码示例）
- Node.js ≥ 16（管理后台前端）
- HBuilderX（推荐用于 UniApp 运行/打包）

## 五、快速启动（本地）
### 1）准备数据库
1. 新建数据库（建议统一用一个库名，例如 `linfeng-love`）。
2. 导入 `sql/linfeng-love-all.sql`。
3. 如需增量升级，参考 `sql/sql说明.txt`。

注意：当前后端默认配置中，两个模块的数据库名称不同：
- `linfeng-love-app` → `application-dev.yml` 使用 `linfeng-love-local`
- `linfeng-love-sys` → `application-dev.yml` 使用 `linfeng-love`

建议做法（二选一）：
- 方案A：把 `linfeng-love-app/src/main/resources/application-dev.yml` 的 `url` 数据库名改成 `linfeng-love`；
- 方案B：额外建立名为 `linfeng-love-local` 的数据库并导入同样的结构数据。

### 2）准备中间件
- Redis：保持默认 127.0.0.1:6379 无密码或按需修改 `application.yml`/`application-dev.yml`。
- RabbitMQ：本地默认注释关闭，后续需要 MQ 再开启并填写连接。
- MinIO/对象存储：默认未强依赖，生产需配置。

### 3）启动后端
在 `linfeng-love-backend/` 目录执行：
```
mvn -U -DskipTests=true clean package
```
分别启动核心服务：
```
# 移动端 API（HTTP 8080，Netty WS 8081）
java -jar linfeng-love-app/target/*.jar

# 管理后台 API（HTTP 8082）
java -jar linfeng-love-sys/target/*.jar
```
或用 IDE 运行对应模块的启动类。Linux 服务器可参考 `shell/后台管理/*`、`shell/用户端/*` 中的脚本使用方式。

### 4）启动管理后台前端（Vue3）
```
cd linfeng-love-web
npm install
# 按需修改接口地址：linfeng-love-web/config/index.js（默认 DEV_API=http://localhost:8082）
npm run dev
```

### 5）启动移动端前端（UniApp）
- 打开 `linfeng-love-uniapps` 至 HBuilderX；
- 修改 `utils/config.js`：
  - `baseUrl`：后端移动端 API 根地址（默认 `127.0.0.1:8080`）；
  - `websocketUrl`：`ws://127.0.0.1:8081/ws`；
  - `aesKey`/`aesIv`：需与后端保持一致（接口加解密）；
- 运行到浏览器（H5）或小程序/APP 模拟器。

## 六、关键配置与端口
- 管理后台 API：`http://localhost:8082`（`linfeng-love-sys/application.yml`）
- 移动端 API：`http://localhost:8080`（`linfeng-love-app/application.yml`）
- WebSocket（IM）：`ws://localhost:8081/ws`（`linfeng-love-app/application.yml` → `netty.port: 8081`）
- 管理后台前端 API 地址：`linfeng-love-web/config/index.js`
- 移动端前端地址与加密：`linfeng-love-uniapps/utils/config.js`

## 七、账号与登录
- 管理后台默认管理员：用户名 `admin`（常见默认），密码 `123456`（见 `注意事项.txt`）。若用户名有变更，以实际数据库 `sys_user` 为准。

## 八、常见问题（FAQ）
- 无法连接数据库：确认 `application-dev.yml` 的库名、账号、端口与 MySQL 版本匹配；确保已导入全量 SQL。
- 接口报加解密错误：检查移动端 `utils/config.js` 的 `aesKey`、`aesIv` 与后端配置是否一致；若本地调试想关闭，可将前端 `aesOpen=false`，并在后端关闭对应加密校验（如走直连 API 而非网关）。
- WebSocket 连接失败：确认已启动 `linfeng-love-app`，且 8081 端口未被占用；H5 需使用 `ws://` 开发协议。
- 管理后台接口 404：检查 `linfeng-love-web/config/index.js` 的 `DEV_API` 是否指向 `8082`，并已启动 `linfeng-love-sys`。

## 九、升级与 SQL
- 首次搭建：导入 `sql/linfeng-love-all.sql`
- 版本升级：按 `sql/sql说明.txt` 中的说明，按历史版本顺序依次导入增量 SQL

## 十、合规与说明
- 技术文档与演示站点参考 `注意事项.txt` 与后端/前端各自 README；
- 严禁分享、转卖源码；上线后将项目地址与订单号发送至 `<EMAIL>` 登记（见 `注意事项.txt`）。

---
如需我为你一键本地化配置（统一库名、更新配置文件、可选关闭前端加密）与首次运行脚本，请告知你的 MySQL 账号/密码与是否需要 RabbitMQ/MinIO/Redis 密码，我可直接改好并给出启动验证清单。


