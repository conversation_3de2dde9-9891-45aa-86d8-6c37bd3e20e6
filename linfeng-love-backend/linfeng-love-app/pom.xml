<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>io.linfeng</groupId>
		<artifactId>linfeng-love-parent</artifactId>
		<version>1.0.0</version>
		<relativePath   />
	</parent>


	<groupId>io.linfeng</groupId>
	<artifactId>linfeng-love-app</artifactId>
	<name>linfeng-love-app</name>
	<version>1.0.0</version>
	<packaging>jar</packaging>
	
	<description></description>
	
	<dependencies>
		<dependency>
			<groupId>io.linfeng</groupId>
			<artifactId>linfeng-love-common</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>io.linfeng</groupId>
			<artifactId>linfeng-love-service</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>io.linfeng</groupId>
			<artifactId>linfeng-love-push</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>io.linfeng</groupId>
			<artifactId>linfeng-love-push-client</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>io.linfeng</groupId>
			<artifactId>linfeng-love-transport</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>io.linfeng</groupId>
			<artifactId>linfeng-love-gateway</artifactId>
			<version>1.0.0</version>
		</dependency>
	</dependencies>

	<build>
		<finalName>${project.artifactId}</finalName>
		<extensions>
			<extension>
				<groupId>org.apache.maven.wagon</groupId>
				<artifactId>wagon-ssh</artifactId>
				<version>2.8</version>
			</extension>
		</extensions>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<fork>true</fork>
				</configuration>
			</plugin>
			<!-- 跳过单元测试 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<configuration>
					<!-- 过滤后缀不需要转码的文件后缀名.crt-->
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>ttf</nonFilteredFileExtension>
					</nonFilteredFileExtensions>
				</configuration>
			</plugin>
		</plugins>
	</build>
	
</project>
