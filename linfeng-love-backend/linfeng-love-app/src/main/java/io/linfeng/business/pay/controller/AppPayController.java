/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.pay.controller;

import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import io.linfeng.business.pay.request.PayCancelRequest;
import io.linfeng.business.pay.response.PayResultResponse;
import io.linfeng.business.pay.service.AppPayService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;


/**
 * 支付Api
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
@RestController
@RequestMapping("app/pay")
@Api(tags = "支付Api")
public class AppPayController {

    private final AppPayService appPayService;

    public AppPayController(AppPayService appPayService) {
        this.appPayService = appPayService;
    }

    /**
     * 支付回调通知
     * @param xmlData 支付回调报文
     * @return 支付处理结果
     */
    @ApiOperation(value = "支付回调通知处理")
    @PostMapping("/callBack")
    public String parseOrderNotifyResult(@RequestBody String xmlData) {
        appPayService.callBack(xmlData);
        return WxPayNotifyResponse.success("成功");
    }

    /**
     * 查询支付结果
     * @param user 登录用户
     * @param orderNo 订单号
     * @return 支付结果
     */
    @Login
    @ApiOperation(value = "支付结果查询")
    @GetMapping("/result")
    public Result<PayResultResponse> queryPayResult(@LoginUser UserEntity user, String orderNo) {
        PayResultResponse response = appPayService.queryPayResult(user, orderNo);
        return new Result<PayResultResponse>().ok(response);
    }

    @ApiOperation(value = "退款回调通知处理")
    @PostMapping("/refund/callBack")
    public String parseRefundNotifyResult(@RequestBody String xmlData) {
        appPayService.refundCallBack(xmlData);
        return WxPayNotifyResponse.success("成功");
    }

    @Login
    @ApiOperation(value = "支付取消")
    @PostMapping("/cancel")
    public R cancel(@LoginUser UserEntity user, @RequestBody PayCancelRequest request) {
        appPayService.cancelPay(user, request.getOrderNo());
        return R.ok();
    }

}
