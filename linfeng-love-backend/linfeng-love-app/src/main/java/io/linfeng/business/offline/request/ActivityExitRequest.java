package io.linfeng.business.offline.request;

import io.linfeng.business.pay.request.PayRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="退出活动请求对象")
public class ActivityExitRequest extends PayRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * activityId
	 */
	@ApiModelProperty(value = "activityId")
	private Integer activityId;

}
