package io.linfeng.business.topic.service.impl;

import io.linfeng.business.topic.response.TopicResponse;
import io.linfeng.business.topic.service.AppTopicService;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.config.dto.response.TopicResponseDTO;
import io.linfeng.love.moment.service.MomentTopicService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("appTopicService")
public class AppTopicServiceImpl implements AppTopicService {

    private final MomentTopicService momentTopicService;

    public AppTopicServiceImpl(MomentTopicService momentTopicService) {
        this.momentTopicService = momentTopicService;
    }

    @Override
    public List<TopicResponse> getTopicList() {
        List<TopicResponseDTO> topicResponseDTOList = momentTopicService.getTopicList();
        return ObjectMapperUtil.convert(topicResponseDTOList, TopicResponse.class);
    }

    @Override
    public TopicResponse getTopicById(Integer topicId) {
        List<TopicResponse> topicResponseList = getTopicList();
        TopicResponse topicResponse = new TopicResponse();
        for(TopicResponse topic : topicResponseList){
            if(topic.getTopicId() == topicId){
                topicResponse = topic;
                break;
            }
        }
        return topicResponse;
    }

    @Override
    public List<TopicResponse> getHotList() {
        List<TopicResponseDTO> topicResponseDTOList = momentTopicService.getHotTopicList();
        return ObjectMapperUtil.convert(topicResponseDTOList, TopicResponse.class);
    }

}