/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.user.controller;


import io.linfeng.business.user.request.UserPreferencesRequest;
import io.linfeng.business.user.response.UserPreferencesResponse;
import io.linfeng.business.user.service.AppUserPreferencesService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.common.utils.ValidatorUtils;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 用户偏好Api
 */
@RestController
@RequestMapping("/app/preferences")
@Api(tags = "用户偏好Api")
public class AppUserPreferencesController {

    private final AppUserPreferencesService appUserPreferencesService;

    public AppUserPreferencesController(AppUserPreferencesService appUserPreferencesService) {
        this.appUserPreferencesService = appUserPreferencesService;
    }

    /**
     * 获取用户偏好详情
     * @param user 登录用户
     * @return 用户偏好详情
     */
    @Login
    @GetMapping ("/detail")
    @ApiOperation("用户偏好详情")
    public Result<UserPreferencesResponse> detail(@ApiIgnore @LoginUser UserEntity user){
        UserPreferencesResponse userPreferences = appUserPreferencesService.getUserPreferences(user);
        return new Result<UserPreferencesResponse>().ok(userPreferences);
    }

    /**
     * 用户偏好修改
     * @param user 登录用户
     * @param request 用户偏好信息
     * @return 修改结果
     */
    @Login
    @PostMapping ("/edit")
    @ApiOperation("用户偏好修改")
    public R edit(@ApiIgnore @LoginUser UserEntity user, @RequestBody UserPreferencesRequest request){
        ValidatorUtils.validateEntity(request);
        appUserPreferencesService.updateAppUserPreferences(user, request);
        return R.ok("修改成功");
    }

}
