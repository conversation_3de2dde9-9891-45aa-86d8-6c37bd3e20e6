package io.linfeng.business.offline.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="活动查询对象")
public class ActivityQueryRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 页数
	 */
	@ApiModelProperty(value = "页数")
	private Integer pageNum;
	/**
	 * 页码
	 */
	@ApiModelProperty(value = "页码")
	private Integer pageSize;
	/**
	 * 城市
	 */
	@ApiModelProperty(value = "城市")
	private String city;
	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private Integer queryType;

}
