package io.linfeng.business.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.user.request.AccountRecordQueryRequest;
import io.linfeng.business.user.response.UserAccountRecordResponse;
import io.linfeng.business.user.response.UserAccountResponse;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.enums.AccountRecordSubType;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 用户账户业务服务
 *
 * <AUTHOR>
 * @date 2023-09-06 16:22:14
 */
public interface AppUserAccountService {


    /**
     * 资金入账
     * @param uid 用户id
     * @param amount 入账金额
     * @param subType 入账类型
     * @param remark 备注
     */
    void entry(Integer uid, BigDecimal amount, AccountRecordSubType subType, String remark);

    /**
     * 查询用户账户信息
     * @param user 登录用户
     * @return 用户账户信息
     */
    UserAccountResponse getUserAccount(UserEntity user);

    /**
     * 获取用户账户记录列表（分页）
     * @param user 登录用户
     * @param request 请求对象
     * @return 用户账户记录列表
     */
    IPage<UserAccountRecordResponse> getUserAccountRecordPage(UserEntity user, AccountRecordQueryRequest request);
}

