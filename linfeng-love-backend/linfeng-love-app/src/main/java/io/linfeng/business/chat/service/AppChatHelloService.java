/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.chat.service;


import io.linfeng.business.chat.response.ChatHelloResponse;
import io.linfeng.business.chat.request.ChatHelloRequest;
import io.linfeng.love.user.entity.UserEntity;

import java.util.List;

/**
 * 嘉宾业务服务
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
public interface AppChatHelloService {

    /**
     * 嘉宾打招呼
     * @param user 登录用户
     * @param request 打招呼请求
     */
    void hello(UserEntity user, ChatHelloRequest request);

    /**
     * 嘉宾打招呼列表
     * @param user 登录用户
     * @return 打招呼列表
     */
    List<ChatHelloResponse> helloApplyList(UserEntity user);

    /**
     * 删除打招呼记录
     * @param user 登录用户
     * @param request 请求
     */
    void deleteHelloApply(UserEntity user, ChatHelloRequest request);

    /**
     * 嘉宾打招呼回应
     * @param user 登录用户
     * @param request 打招呼回应请求
     */
    void helloReply(UserEntity user, ChatHelloRequest request);
}

