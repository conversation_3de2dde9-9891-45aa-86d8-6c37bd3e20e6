package io.linfeng.business.vip.service.impl;

import cn.hutool.core.util.ObjectUtil;
import io.linfeng.business.vip.service.AppVipService;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.love.config.entity.VipOptionEntity;
import io.linfeng.love.config.service.VipOptionService;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.service.UserService;
import org.apache.ibatis.logging.Log;
import org.apache.ibatis.logging.LogFactory;
import org.springframework.stereotype.Service;


@Service("appVipService")
public class AppVipServiceImpl implements AppVipService {

    private Log log = LogFactory.getLog(getClass());

    private final VipOptionService vipOptionService;

    private final UserService userService;

    public AppVipServiceImpl(VipOptionService vipOptionService, UserService userService) {
        this.vipOptionService = vipOptionService;
        this.userService = userService;
    }

    @Override
    public void vipRecharge(Integer uid, Integer optionId) {
        UserEntity user = userService.getUserByUid(uid);

        VipOptionEntity vipOption = vipOptionService.getById(optionId);

        if(user.getVip().equals(Constant.VIP_USER) && ObjectUtil.isNotNull(user.getVipExpireTime())){
            //续费用户
            String toStr = DateUtil.dateToStr(user.getVipExpireTime(), "yyyy-MM-dd HH:mm:ss");
            user.setVipExpireTime(DateUtil.addDay(toStr,vipOption.getValidDays()));
        }else{
            user.setVip(Constant.VIP_USER);
            user.setVipExpireTime(DateUtil.addDay(DateUtil.nowDateTimeStr(),vipOption.getValidDays()));
        }
        userService.updateAndDeleteCache(user);
    }

}