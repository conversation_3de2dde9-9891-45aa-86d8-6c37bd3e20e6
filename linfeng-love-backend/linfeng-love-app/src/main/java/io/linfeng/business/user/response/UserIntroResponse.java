package io.linfeng.business.user.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="用户介绍响应实体")
public class UserIntroResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 配置id
	 */
	@ApiModelProperty(value = "配置id")
	private Integer introId;
	/**
	 * 配置编码
	 */
	@ApiModelProperty(value = "图标地址")
	private String icon;
	/**
	 * 配置编码
	 */
	@ApiModelProperty(value = "配置标题")
	private String title;

	/**
	 * 提示信息
	 */
	@ApiModelProperty(value = "提示信息")
	private String placeholder;

	/**
	 * 填写内容
	 */
	@ApiModelProperty(value = "填写内容")
	private String content;

}
