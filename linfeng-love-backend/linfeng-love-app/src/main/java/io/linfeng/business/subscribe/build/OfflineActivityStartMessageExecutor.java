package io.linfeng.business.subscribe.build;

import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import com.alibaba.fastjson.JSON;
import io.linfeng.business.subscribe.core.AbstractSubscribeMessageExecutor;
import io.linfeng.business.subscribe.dto.WxMaSubscribeMessageDTO;
import io.linfeng.love.offline.entity.OfflineActivityEntity;
import io.linfeng.love.offline.entity.OfflineActivityEntity;
import io.linfeng.love.offline.service.OfflineActivityService;
import io.linfeng.love.offline.service.OfflineActivityService;
import io.linfeng.love.user.entity.UserEntity;
import me.chanjar.weixin.mp.bean.subscribe.WxMpSubscribeMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("newOfflineActivityMessageBuildService")
public class OfflineActivityStartMessageExecutor extends AbstractSubscribeMessageExecutor {

    @Autowired
    private OfflineActivityService offlineActivityService;

    @Override
    public WxMaSubscribeMessageDTO buildWxMaSubscribeMessage(UserEntity user, String tmplId, String linkId) {
        WxMaSubscribeMessageDTO wxMaSubscribeMessageDTO = new WxMaSubscribeMessageDTO();
        WxMaSubscribeMessage wxMaSubscribeMessage = new WxMaSubscribeMessage();
        OfflineActivityEntity offlineActivity = offlineActivityService.getById(Integer.parseInt(linkId));
        wxMaSubscribeMessage.setTemplateId(tmplId);
        wxMaSubscribeMessage.setToUser(user.getOpenid());
        wxMaSubscribeMessage.setPage(getPageUrl(offlineActivity.getId().toString()));
        List<WxMaSubscribeMessage.MsgData> msgDataList = new ArrayList<>();
        WxMaSubscribeMessage.MsgData msgData1 = new WxMaSubscribeMessage.MsgData();
        msgData1.setName("thing4");
        msgData1.setValue(offlineActivity.getActivityName());
        WxMaSubscribeMessage.MsgData msgData2 = new WxMaSubscribeMessage.MsgData();
        msgData2.setName("date5");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        msgData2.setValue(sdf.format(offlineActivity.getStartTime()));
        WxMaSubscribeMessage.MsgData msgData3 = new WxMaSubscribeMessage.MsgData();
        msgData3.setName("thing6");
        msgDataList.add(msgData1);
        msgDataList.add(msgData2);
        msgDataList.add(msgData3);
        msgData3.setValue(offlineActivity.getAddressTitle());
        wxMaSubscribeMessage.setData(msgDataList);

        saveSubscribeMessage(user, tmplId, linkId, JSON.toJSONString(wxMaSubscribeMessage));

        wxMaSubscribeMessageDTO.setImmediately(false);
        wxMaSubscribeMessageDTO.setWxMaSubscribeMessage(wxMaSubscribeMessage);
        return wxMaSubscribeMessageDTO;
    }

    @Override
    public WxMpSubscribeMessage buildWxMpSubscribeMessage(UserEntity user, String tmplId, String linkId) {
        WxMpSubscribeMessage wxMpSubscribeMessage = new WxMpSubscribeMessage();
        OfflineActivityEntity offlineActivity = offlineActivityService.getById(Integer.parseInt(linkId));
        wxMpSubscribeMessage.setToUser(user.getMpOpenid());
        wxMpSubscribeMessage.setTemplateId(tmplId);
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("thing4", offlineActivity.getActivityName());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        dataMap.put("date5", sdf.format(offlineActivity.getStartTime()));
        dataMap.put("thing6", offlineActivity.getAddressTitle());
        wxMpSubscribeMessage.setDataMap(dataMap);
        return wxMpSubscribeMessage;
    }

    @Override
    public String getPageUrl(String linkId) {
        return "/pages/offline/activity/official-detail?activityId=" + linkId;
    }

    @Override
    public String getMessageType() {
        return "offlineActivityStart";
    }
}