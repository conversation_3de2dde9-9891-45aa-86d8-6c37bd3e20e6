package io.linfeng.business.offline.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.business.offline.response.NoticeResponse;
import io.linfeng.business.offline.service.AppOfflineNoticeService;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.offline.entity.OfflineNoticeEntity;
import io.linfeng.love.offline.service.OfflineNoticeService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("appOfflineNoticeService")
@AllArgsConstructor
public class AppOfflineNoticeServiceImpl implements AppOfflineNoticeService {

    private final OfflineNoticeService offlineNoticeService;

    @Override
    public List<NoticeResponse> getNoticelist() {
        LambdaQueryWrapper<OfflineNoticeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(OfflineNoticeEntity::getSort);
        List<OfflineNoticeEntity> list = offlineNoticeService.list(wrapper);
        return ObjectMapperUtil.convert(list, NoticeResponse.class);
    }

    @Override
    public NoticeResponse getOfflineNoticeDetail(Integer id) {
        OfflineNoticeEntity notice = offlineNoticeService.getById(id);
        return ObjectMapperUtil.convert(notice, NoticeResponse.class);
    }
}