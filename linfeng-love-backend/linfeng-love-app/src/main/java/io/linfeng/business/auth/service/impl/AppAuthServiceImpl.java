package io.linfeng.business.auth.service.impl;


import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.linfeng.business.auth.request.SmsLoginRequest;
import io.linfeng.business.auth.request.WxLoginRequest;
import io.linfeng.business.auth.response.LoginResponse;
import io.linfeng.business.auth.service.AppAuthService;
import io.linfeng.business.sms.service.AppSmsService;
import io.linfeng.business.auth.request.BindWxPhoneRequest;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.JwtUtil;
import io.linfeng.love.common.enums.CommonStatus;
import io.linfeng.love.executor.MissionEventExecutor;
import io.linfeng.love.user.entity.UserAccountEntity;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.enums.RealNameStatus;
import io.linfeng.love.user.service.UserAccountService;
import io.linfeng.love.user.service.UserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

@Service("appAuthService")
@AllArgsConstructor
@Slf4j
public class AppAuthServiceImpl implements AppAuthService {

    private final WxMaService wxMaService;

    private final WxMpService wxMpService;

    private final UserService userService;

    private final AppSmsService appSmsService;

    private final JwtUtil jwtUtil;

    private final MissionEventExecutor missionEventExecutor;

    private final UserAccountService userAccountService;

    @Override
    public LoginResponse miniWxLogin(WxLoginRequest request) {
        LoginResponse response = new LoginResponse();
        String token = null;
        String openid = null;
        try{
            openid = wxMaService.getUserService().getSessionInfo(request.getCode()).getOpenid();
        }catch (Exception e){
            log.error("微信小程序登录获取openId获取失败", e);
            throw new LinfengException("微信小程序登录获取openId获取失败", e);
        }

        LambdaQueryWrapper<UserEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(UserEntity::getOpenid, openid);
        UserEntity userEntity = userService.getOne(lambdaQueryWrapper);
        if(userEntity == null){
            userEntity = new UserEntity();
            userEntity.setOpenid(openid);
            userEntity.setOid(IdUtil.fastSimpleUUID());
            userEntity.setRealNameStatus(RealNameStatus.REGISTER.getValue());
            userEntity.setStatus(CommonStatus.DEFAULT.getValue());
            userEntity.setCreateTime(DateUtil.nowDateTime());
            userService.save(userEntity);
            createUserAccount(userEntity);
            //新用户注册成功领取新手任务
            missionEventExecutor.receiveNewUserMission(userEntity);

        }

        token = jwtUtil.generateToken(userEntity.getUid());
        response.setToken(token);
        return response;
    }

    @Override
    public void bindMpOpenid(UserEntity user, WxLoginRequest request) {
        String mpOpenid = null;
        try{
            mpOpenid = wxMpService.getOAuth2Service().getAccessToken(request.getCode()).getOpenId();
        }catch (Exception e){
            log.error("微信公众号获取openId获取失败", e);
            throw new LinfengException("微信公众号获取openId获取失败");
        }
        user.setMpOpenid(mpOpenid);
        user.setUpdateTime(DateUtil.nowDateTime());
        userService.updateAndDeleteCache(user);
    }

    @Override
    public LoginResponse smsLogin(SmsLoginRequest request) {

        appSmsService.verifySmsCode(request.getMobileNo(), request.getCode());

        LoginResponse response = new LoginResponse();
        String token = null;

        LambdaQueryWrapper<UserEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(UserEntity::getMobile, request.getMobileNo());
        UserEntity userEntity = userService.getOne(lambdaQueryWrapper);
        if(userEntity == null){
            userEntity = new UserEntity();
            userEntity.setMobile(request.getMobileNo());
            userEntity.setOid(IdUtil.fastSimpleUUID());
            userEntity.setRealNameStatus(RealNameStatus.REGISTER.getValue());
            userEntity.setStatus(CommonStatus.DEFAULT.getValue());
            userEntity.setCreateTime(DateUtil.nowDateTime());
            userService.save(userEntity);
            createUserAccount(userEntity);
            //新用户注册领取新手任务
            missionEventExecutor.receiveNewUserMission(userEntity);
        }

        token = jwtUtil.generateToken(userEntity.getUid());
        response.setToken(token);
        return response;
    }

    @Override
    @Transactional
    public LoginResponse bindWxPhone(UserEntity loginUser, BindWxPhoneRequest request) {
        LoginResponse response = new LoginResponse();
        String token = null;
        UserEntity user = userService.getById(loginUser.getUid());
        String sessionKey = null;
        String phone = null;
        try{
            sessionKey = wxMaService.getUserService().getSessionInfo(request.getCode()).getSessionKey();
            phone = wxMaService.getUserService().getPhoneNoInfo(sessionKey, request.getEncryptedData(), request.getIv()).getPhoneNumber();
        }catch (Exception e){
            log.error("openid获取失败", e);
            throw new LinfengException("openid获取失败");
        }

        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserEntity::getMobile, phone);
        UserEntity oldUser = userService.getOne(wrapper);
        //如果其他终端已经注册过就把数据同步过来
        if(oldUser == null){
            user.setMobile(phone);
            user.setUpdateTime(DateUtil.nowDateTime());
            userService.updateAndDeleteCache((user));
            return response;
        }

        oldUser.setOpenid(user.getOpenid());
        oldUser.setUpdateTime(DateUtil.nowDateTime());
        userService.removeById(user.getUid());
        userService.updateAndDeleteCache(oldUser);
        token = jwtUtil.generateToken(oldUser.getUid());
        response.setToken(token);
        return response;

    }

    private void createUserAccount(UserEntity userEntity){
        //创建账户
        UserAccountEntity userAccount = new UserAccountEntity();
        userAccount.setUid(userEntity.getUid());
        userAccount.setTotalAmount(new BigDecimal(0));
        userAccount.setCashedAmount(new BigDecimal(0));
        userAccount.setPetalAmount(new BigDecimal(0));
        userAccount.setFreezeAmount(new BigDecimal(0));
        userAccount.setUnCashedAmount(new BigDecimal(0));
        userAccount.setDayCashedCount(0);
        userAccount.setWeekCashedCount(0);
        userAccount.setCreateTime(DateUtil.nowDateTime());
        userAccountService.save(userAccount);
    }

}