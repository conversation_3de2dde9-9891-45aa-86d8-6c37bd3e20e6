/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.bottle.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.bottle.request.*;
import io.linfeng.business.bottle.response.*;
import io.linfeng.business.bottle.service.AppBottleService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * 漂流瓶Api
 */
@RestController
@RequestMapping("/app/bottle")
@Api(tags = "漂流瓶Api")
public class AppBottleController {

    private final AppBottleService appBottleService;

    public AppBottleController(AppBottleService appBottleService) {
        this.appBottleService = appBottleService;
    }

    /**
     * 漂流列表
     * @param user 登录用户
     * @return 我扔出的漂流瓶列表
     */
    @Login
    @GetMapping("/list")
    @ApiOperation("漂流瓶列表")
    public Result<List<UserBottleDetailResponse>> list(@ApiIgnore @LoginUser UserEntity user){
        List<UserBottleDetailResponse> userBottleList = appBottleService.getUserBottleDetailList(user);
        return new Result<List<UserBottleDetailResponse>>().ok(userBottleList);
    }

    /**
     * 剩余次数
     * @param user 登录用户
     * @return 剩余次数
     */
    @Login
    @PostMapping("/residue")
    @ApiOperation("剩余次数")
    public Result<UserBottleResponse> detail(@ApiIgnore @LoginUser UserEntity user){
        UserBottleResponse userBottleDetail = appBottleService.getUserBottle(user);
        return new Result<UserBottleResponse>().ok(userBottleDetail);
    }


    /**
     * 扔漂流瓶
     * @param user 登录用户
     * @param request 请求对象
     * @return 结果
     */
    @Login
    @PostMapping("/throw")
    @ApiOperation("扔一个")
    public R throwBottle(@ApiIgnore @LoginUser UserEntity user, @RequestBody ThrowBottleRequest request){
        appBottleService.throwBottle(user, request);
        return R.ok();
    }

    /**
     * 回应捡到的漂流瓶
     * @param user 登录用户
     * @param request 回应请求
     * @return 回应结果
     */
    @Login
    @PostMapping("/reply")
    @ApiOperation("回应")
    public R reply(@ApiIgnore @LoginUser UserEntity user, @RequestBody ReplyBottleRequest request){
        appBottleService.reply(user, request);
        return R.ok();
    }

    /**
     * 捞一个漂流瓶
     * @param user 登录用户
     * @return 捞的结果
     */
    @Login
    @PostMapping("/pick")
    @ApiOperation("捞一个")
    public Result<BottleResponse> pick(@ApiIgnore @LoginUser UserEntity user){
        BottleResponse bottleResponse = appBottleService.pick(user);
        return new Result<BottleResponse>().ok(bottleResponse);
    }

    /**
     * 漂流瓶会话列表
     * @param user 登录用户
     * @return 漂流瓶会话列表
     */
    @Login
    @GetMapping("/session/list")
    @ApiOperation("漂流瓶会话列表")
    public Result<List<BottleSessionResponse>> sessionList(@ApiIgnore @LoginUser UserEntity user){
        List<BottleSessionResponse> chatSessionList = appBottleService.getBottleSessionList(user);
        return new Result<List<BottleSessionResponse>>().ok(chatSessionList);
    }

    /**
     * 漂流瓶会话详情
     * @param user 登录用户
     * @param bottleId 瓶子id
     * @return 漂流瓶会话详情
     */
    @Login
    @GetMapping("/session/detail")
    @ApiOperation("漂流瓶会话详情")
    public Result<BottleSessionResponse> sessionDetail(@ApiIgnore @LoginUser UserEntity user, Integer bottleId){
        BottleSessionResponse chatSession = appBottleService.getBottleSession(user, bottleId);
        return new Result<BottleSessionResponse>().ok(chatSession);
    }

    /**
     * 删除漂流瓶会话
     * @param user 登录用户
     * @param request 删除请求信息
     * @return 删除结果
     */
    @Login
    @PostMapping("/session/delete")
    @ApiOperation("删除漂流瓶会话")
    public R delete(@ApiIgnore @LoginUser UserEntity user, @RequestBody BottleSessionRequest request){
        appBottleService.deleteSession(user, request.getBottleId());
        return R.ok();
    }

    /**
     * 查询聊天记录
     * @param user 登录用户
     * @param request 查询请求
     * @return 聊天记录列表
     */
    @Login
    @GetMapping("/chat/list")
    @ApiOperation("查询聊天记录")
    public Result<IPage<BottleMessageResponse>> list(@ApiIgnore @LoginUser UserEntity user, BottleMessageRequest request){
        IPage<BottleMessageResponse> chatMessageList = appBottleService.getMessageList(user, request);
        return new Result<IPage<BottleMessageResponse>>().ok(chatMessageList);
    }

    /**
     * 发送聊天消息
     * @param user 登录用户
     * @param request 聊天请求
     * @return 发送结果
     */
    @Login
    @PostMapping("/chat/send")
    @ApiOperation("发消息")
    public Result<BottleMessageResponse> send(@ApiIgnore @LoginUser UserEntity user, @RequestBody BottleMessageRequest request){
        BottleMessageResponse chatMessageResponse = appBottleService.sendMessage(user, request);
        return new Result<BottleMessageResponse>().ok(chatMessageResponse);
    }

    /**
     * 消息已读
     * @param user 登录用户
     * @param request 请求
     * @return 结果
     */
    @Login
    @PostMapping("/chat/read")
    @ApiOperation("消息已读")
    public R read(@ApiIgnore @LoginUser UserEntity user, @RequestBody BottleMessageRequest request){
        appBottleService.readMessage(user, request);
        return R.ok();
    }

}
