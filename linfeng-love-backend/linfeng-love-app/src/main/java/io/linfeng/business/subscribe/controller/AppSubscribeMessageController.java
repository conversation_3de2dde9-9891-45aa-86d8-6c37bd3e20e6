/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.subscribe.controller;

import io.linfeng.business.subscribe.request.SubscribeMessageRequest;
import io.linfeng.business.subscribe.service.AppSubscribeMessageService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * APP消息api
 *
 */
@RestController
@RequestMapping("app/subscribeMessage")
@Api(tags = "APP消息api")
public class AppSubscribeMessageController {

	private final AppSubscribeMessageService appMessageService;

	public AppSubscribeMessageController(AppSubscribeMessageService appMessageService) {
		this.appMessageService = appMessageService;
	}

	/**
	 * 新增消息订阅通知
	 * @param user 登录用户
	 * @return 新增结果
	 */
	@Login
	@PostMapping("/add")
	@ApiOperation("新增消息订阅通知")
	public R receive(@ApiIgnore @LoginUser UserEntity user, @RequestBody SubscribeMessageRequest request){
		appMessageService.executeSubscribeMessageTask(user, request);
		return R.ok();
	}

}
