package io.linfeng.business.bottle.service.impl;


import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.linfeng.business.bottle.request.BottleMessageRequest;
import io.linfeng.business.bottle.request.ReplyBottleRequest;
import io.linfeng.business.bottle.request.ThrowBottleRequest;
import io.linfeng.business.bottle.response.*;
import io.linfeng.business.bottle.service.AppBottleService;
import io.linfeng.business.censor.service.AppCensorService;
import io.linfeng.business.user.service.AppUserMessageService;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.bottle.dto.response.BottleMessageResponseDTO;
import io.linfeng.love.bottle.dto.response.BottleResponseDTO;
import io.linfeng.love.bottle.dto.response.BottleSessionResponseDTO;
import io.linfeng.love.bottle.entity.BottleMessageEntity;
import io.linfeng.love.bottle.entity.BottleSessionEntity;
import io.linfeng.love.bottle.entity.UserBottleDetailEntity;
import io.linfeng.love.bottle.entity.UserBottleEntity;
import io.linfeng.love.bottle.enums.BottleStatus;
import io.linfeng.love.bottle.service.BottleMessageService;
import io.linfeng.love.bottle.service.BottleSessionService;
import io.linfeng.love.bottle.service.UserBottleDetailService;
import io.linfeng.love.bottle.service.UserBottleService;
import io.linfeng.love.chat.enums.MessageType;
import io.linfeng.love.config.service.ConfigBusinessService;
import io.linfeng.love.config.service.DictItemService;
import io.linfeng.love.petal.entity.PetalUnlockEntity;
import io.linfeng.love.petal.service.PetalUnlockService;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.entity.UserMessageEntity;
import io.linfeng.love.user.enums.MessageSource;
import io.linfeng.love.user.service.UserMessageService;
import io.linfeng.love.user.service.UserService;
import io.linfeng.push.client.dto.*;
import io.linfeng.push.client.enums.PushMessageType;
import io.linfeng.push.client.producer.MessageProducer;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service("appBottleService")
public class AppBottleServiceImpl implements AppBottleService {

    private final UserBottleService userBottleService;

    private final UserBottleDetailService userBottleDetailService;

    private final BottleSessionService bottleSessionService;

    private final BottleMessageService bottleMessageService;

    private final ConfigBusinessService configBusinessService;

    private final DictItemService dictItemService;

    private final UserService userService;

    private final MessageProducer messageProducer;

    private final AppUserMessageService appUserMessageService;

    private final UserMessageService userMessageService;

    private final PetalUnlockService petalUnlockService;

    private final AppCensorService appCensorService;

    public AppBottleServiceImpl(UserBottleService userBottleService, UserBottleDetailService userBottleDetailService, BottleSessionService bottleSessionService, BottleMessageService bottleMessageService, ConfigBusinessService configBusinessService, DictItemService dictItemService, UserService userService, MessageProducer messageProducer, AppUserMessageService appUserMessageService, UserMessageService userMessageService, PetalUnlockService petalUnlockService, AppCensorService appCensorService) {
        this.userBottleService = userBottleService;
        this.userBottleDetailService = userBottleDetailService;
        this.bottleSessionService = bottleSessionService;
        this.bottleMessageService = bottleMessageService;
        this.configBusinessService = configBusinessService;
        this.dictItemService = dictItemService;
        this.userService = userService;
        this.messageProducer = messageProducer;
        this.appUserMessageService = appUserMessageService;
        this.userMessageService = userMessageService;
        this.petalUnlockService = petalUnlockService;
        this.appCensorService = appCensorService;
    }


    @Override
    public List<UserBottleDetailResponse> getUserBottleDetailList(UserEntity user) {
        LambdaQueryWrapper<UserBottleDetailEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserBottleDetailEntity::getUid, user.getUid());
        wrapper.orderByDesc(UserBottleDetailEntity::getThrowTime);
        List<UserBottleDetailEntity> userBottleDetailEntityList = userBottleDetailService.list(wrapper);
        List<UserBottleDetailResponse> responseList = new ArrayList<>();
        userBottleDetailEntityList.forEach(userBottleDetail -> {
            UserBottleDetailResponse response = ObjectMapperUtil.convert(userBottleDetail, UserBottleDetailResponse.class);
            response.setThrowTime(DateUtil.dateToStr(userBottleDetail.getThrowTime(), DateUtil.DATE_FORMAT));
            response.setReplyTime(DateUtil.dateToStr(userBottleDetail.getReplyTime(), DateUtil.DATE_FORMAT));
            responseList.add(response);
        });
        return responseList;
    }

    @Override
    @Transactional
    public void throwBottle(UserEntity user, ThrowBottleRequest request) {
        if(request.getBottleId() != null){
            //捡到的扔回去
            UserBottleDetailEntity userBottleDetail = userBottleDetailService.getById(request.getBottleId());
            userBottleDetail.setStatus(BottleStatus.DRIFT.getValue());
            userBottleDetail.setUpdateTime(DateUtil.nowDateTime());
            userBottleDetailService.updateById(userBottleDetail);
            return;
        }

        //文本审核
        appCensorService.censorText(request.getContent());

        //自己扔的
        UserBottleDetailEntity userBottleDetail = new UserBottleDetailEntity();
        userBottleDetail.setUid(user.getUid());
        userBottleDetail.setContent(request.getContent());
        userBottleDetail.setPickNum(0);
        userBottleDetail.setStatus(BottleStatus.DRIFT.getValue());
        userBottleDetail.setThrowTime(DateUtil.nowDateTime());
        userBottleDetail.setCreateTime(DateUtil.nowDateTime());
        userBottleDetailService.save(userBottleDetail);

        //校验奖励次数是否达到上限
        LambdaQueryWrapper<UserBottleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserBottleEntity::getUid, user.getUid());
        UserBottleEntity userBottle = userBottleService.getOne(wrapper);
        userBottle.setThrowTimes(userBottle.getThrowTimes() + 1);
        if(userBottle.getThrowTimes() <= Integer.parseInt(configBusinessService.getValue(Constant.THROW_BOTTLE_PRIZE_TIMES))){
            userBottle.setResiduePickTimes(userBottle.getResiduePickTimes() + Integer.parseInt(configBusinessService.getValue(Constant.THROW_BOTTLE_PRIZE_NUM)));
        }
        userBottle.setUpdateTime(DateUtil.nowDateTime());
        userBottleService.updateById(userBottle);

    }

    @Override
    @Transactional
    public void reply(UserEntity user, ReplyBottleRequest request) {
        //更新漂流瓶信息
        UserBottleDetailEntity userBottleDetail = userBottleDetailService.getById(request.getBottleId());
        userBottleDetail.setReplyUid(user.getUid());
        userBottleDetail.setReplyContent(request.getContent());
        userBottleDetail.setReplyTime(DateUtil.nowDateTime());
        userBottleDetail.setStatus(BottleStatus.REPLY.getValue());
        userBottleDetail.setUpdateTime(DateUtil.nowDateTime());
        userBottleDetailService.updateById(userBottleDetail);

        //创建漂流瓶会话
        createBottleSession(userBottleDetail);
        //创建聊天消息
        createBottleMessage(userBottleDetail);

    }

    private void createBottleMessage(UserBottleDetailEntity userBottleDetail) {
        BottleMessageEntity friendBottleMessage = new BottleMessageEntity();
        friendBottleMessage.setMessageId(IdUtil.fastSimpleUUID());
        friendBottleMessage.setBottleId(userBottleDetail.getId());
        friendBottleMessage.setContent(userBottleDetail.getContent());
        friendBottleMessage.setSenderUid(userBottleDetail.getUid());
        friendBottleMessage.setReceiverUid(userBottleDetail.getReplyUid());
        friendBottleMessage.setMessageType(MessageType.TEXT.getValue());
        friendBottleMessage.setSendTime(userBottleDetail.getThrowTime());
        friendBottleMessage.setCreateTime(DateUtil.nowDateTime());
        bottleMessageService.save(friendBottleMessage);

        BottleMessageEntity myBottleMessage = new BottleMessageEntity();
        myBottleMessage.setMessageId(IdUtil.fastSimpleUUID());
        myBottleMessage.setBottleId(userBottleDetail.getId());
        myBottleMessage.setContent(userBottleDetail.getReplyContent());
        myBottleMessage.setSenderUid(userBottleDetail.getReplyUid());
        myBottleMessage.setReceiverUid(userBottleDetail.getUid());
        myBottleMessage.setMessageType(MessageType.TEXT.getValue());
        myBottleMessage.setSendTime(userBottleDetail.getReplyTime());
        myBottleMessage.setCreateTime(DateUtil.nowDateTime());
        bottleMessageService.save(myBottleMessage);

        //发送公共会话消息（聊天页面会话）
        appUserMessageService.sendBottleMessage(myBottleMessage);
    }

    private void createBottleSession(UserBottleDetailEntity userBottleDetail) {
        //保存我的会话
        BottleSessionEntity myBottleSession = new BottleSessionEntity();
        myBottleSession.setBottleId(userBottleDetail.getId());
        myBottleSession.setUid(userBottleDetail.getReplyUid());
        myBottleSession.setFriendUid(userBottleDetail.getUid());
        myBottleSession.setUnRead(0);
        myBottleSession.setLastMessageUid(userBottleDetail.getReplyUid());
        myBottleSession.setLastMessageType(MessageType.TEXT.getValue());
        myBottleSession.setLastMessageContent(userBottleDetail.getReplyContent());
        myBottleSession.setLastMessageTime(userBottleDetail.getReplyTime());
        myBottleSession.setCreateTime(DateUtil.nowDateTime());
        bottleSessionService.save(myBottleSession);

        //保存对方的会话
        BottleSessionEntity friendBottleSession = new BottleSessionEntity();
        friendBottleSession.setBottleId(userBottleDetail.getId());
        friendBottleSession.setUid(userBottleDetail.getUid());
        friendBottleSession.setFriendUid(userBottleDetail.getReplyUid());
        friendBottleSession.setUnRead(1);
        friendBottleSession.setLastMessageUid(userBottleDetail.getUid());
        friendBottleSession.setLastMessageType(MessageType.TEXT.getValue());
        friendBottleSession.setLastMessageContent(userBottleDetail.getReplyContent());
        friendBottleSession.setLastMessageTime(userBottleDetail.getReplyTime());
        friendBottleSession.setCreateTime(DateUtil.nowDateTime());
        bottleSessionService.save(friendBottleSession);

        //推送会话消息（漂流瓶会话）
        pushSessionMessage(myBottleSession);
        pushSessionMessage(friendBottleSession);

    }

    @Override
    @Transactional
    public BottleResponse pick(UserEntity user) {
        LambdaQueryWrapper<UserBottleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserBottleEntity::getUid, user.getUid());
        UserBottleEntity userBottle = userBottleService.getOne(wrapper);
        if(userBottle.getResiduePickTimes() == 0){
            throw new LinfengException("今日打捞次数已用完");
        }
        userBottle.setResiduePickTimes(userBottle.getResiduePickTimes() - 1);
        userBottleService.updateById(userBottle);

        BottleResponseDTO bottleResponseDTO = userBottleDetailService.pickBottle(user.getUid());
        if(bottleResponseDTO == null){
            throw new LinfengException("啥也没捞到");
        }

        UserBottleDetailEntity userBottleDetail = userBottleDetailService.getById(bottleResponseDTO.getBottleId());
        userBottleDetail.setPickNum(userBottleDetail.getPickNum() + 1);
        userBottleDetail.setStatus(BottleStatus.PICKED.getValue());
        userBottleDetail.setLastPickTime(DateUtil.nowDateTime());
        userBottleDetail.setUpdateTime(DateUtil.nowDateTime());
        userBottleDetailService.updateById(userBottleDetail);

        BottleResponse response = ObjectMapperUtil.convert(bottleResponseDTO, BottleResponse.class);
        LambdaQueryWrapper<PetalUnlockEntity> petalUnlockWrapper =new LambdaQueryWrapper<>();
        petalUnlockWrapper.eq(PetalUnlockEntity::getUid,user.getUid());
        petalUnlockWrapper.eq(PetalUnlockEntity::getGuestUid,bottleResponseDTO.getUid());
        PetalUnlockEntity petalUnlockEntity = petalUnlockService.getOne(petalUnlockWrapper);
        if(petalUnlockEntity != null || user.getVip() == Constant.VIP_USER){
           response.setLockFlag(0);
        }else{
            response.setLockFlag(1);
        }
        response.setBirthdayYear(bottleResponseDTO.getBirthday().substring(2,4));
        response.setJobText(dictItemService.getItemName(Constant.DICT_JOB, bottleResponseDTO.getJob()));
        return response;
    }

    @Override
    public List<BottleSessionResponse> getBottleSessionList(UserEntity user) {
        List<BottleSessionResponseDTO> responseDTOList = bottleSessionService.getAllSessionList(user.getUid());
        List<BottleSessionResponse> responseList = ObjectMapperUtil.convert(responseDTOList, BottleSessionResponse.class);

        return responseList;
    }

    @Override
    public void deleteSession(UserEntity user, Integer bottleId) {
        LambdaQueryWrapper<BottleSessionEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BottleSessionEntity::getBottleId, bottleId);
        wrapper.eq(BottleSessionEntity::getUid, user.getUid());
        bottleSessionService.remove(wrapper);
    }

    @Override
    public IPage<BottleMessageResponse> getMessageList(UserEntity user, BottleMessageRequest request) {
        Page<BottleMessageResponseDTO> page = new Page<>(request.getPageNum(), request.getPageSize());

        IPage<BottleMessageResponseDTO> bottleMessagePage = bottleMessageService.selectMessagePage(page, request.getBottleId());

        return ObjectMapperUtil.convert(bottleMessagePage, BottleMessageResponse.class);
    }

    @Override
    public BottleMessageResponse sendMessage(UserEntity user, BottleMessageRequest request) {
        UserEntity friendUser = userService.getUserByOid(request.getFriendOid());
        LambdaQueryWrapper<BottleSessionEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BottleSessionEntity::getBottleId, request.getBottleId());
        wrapper.eq(BottleSessionEntity::getUid, friendUser.getUid());
        BottleSessionEntity friendBottleSession = bottleSessionService.getOne(wrapper);
        if(friendBottleSession == null){
            throw new LinfengException("对方已结束漂流瓶会话");
        }

        //更新朋友会话
        friendBottleSession.setUnRead(friendBottleSession.getUnRead() + 1);
        friendBottleSession.setLastMessageUid(user.getUid());
        friendBottleSession.setLastMessageType(request.getMessageType());
        friendBottleSession.setLastMessageContent(request.getContent());
        friendBottleSession.setLastMessageTime(DateUtil.nowDateTime());
        friendBottleSession.setUpdateTime(DateUtil.nowDateTime());
        bottleSessionService.updateById(friendBottleSession);

        //推送会话消息
        pushSessionMessage(friendBottleSession);

        //更新我的会话
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BottleSessionEntity::getBottleId, request.getBottleId());
        wrapper.eq(BottleSessionEntity::getUid, user.getUid());
        BottleSessionEntity myBottleSession = bottleSessionService.getOne(wrapper);
        myBottleSession.setLastMessageUid(user.getUid());
        myBottleSession.setLastMessageType(request.getMessageType());
        myBottleSession.setLastMessageContent(request.getContent());
        myBottleSession.setLastMessageTime(DateUtil.nowDateTime());
        myBottleSession.setUpdateTime(DateUtil.nowDateTime());
        bottleSessionService.updateById(myBottleSession);

        //保存聊天记录
        BottleMessageEntity bottleMessage = new BottleMessageEntity();
        bottleMessage.setMessageId(IdUtil.fastSimpleUUID());
        bottleMessage.setCreateTime(DateUtil.nowDateTime());
        bottleMessage.setBottleId(request.getBottleId());
        bottleMessage.setSenderUid(user.getUid());
        bottleMessage.setReceiverUid(friendUser.getUid());
        bottleMessage.setMessageType(request.getMessageType());
        bottleMessage.setContent(request.getContent());
        bottleMessage.setDuration(request.getDuration());
        bottleMessage.setSendTime(DateUtil.nowDateTime());
        bottleMessageService.save(bottleMessage);

        //发送公共会话消息（聊天页面会话）
        appUserMessageService.sendBottleMessage(bottleMessage);

        //推送聊天消息
        PushMessage pushMessage = new PushMessage();
        pushMessage.setSenderUid(user.getUid());
        pushMessage.setReceiverUid(friendUser.getUid());
        pushMessage.setType(PushMessageType.BOTTLE_CHAT.getValue());
        BottleChatData bottleChatData = ObjectMapperUtil.convert(bottleMessage, BottleChatData.class);
        bottleChatData.setSenderOid(user.getOid());
        bottleChatData.setReceiverOid(friendUser.getOid());
        bottleChatData.setSendTime(DateUtil.dateToStr(bottleMessage.getSendTime(), DateUtil.DATE_FORMAT));
        pushMessage.setData(bottleChatData);
        messageProducer.sendMessage(Constant.DIRECT_EXCHANGE_NAME, Constant.PUSH_ROUTING_NAME, pushMessage);

        BottleMessageResponse response = ObjectMapperUtil.convert(bottleMessage, BottleMessageResponse.class);
        response.setSenderOid(user.getOid());
        response.setReceiverOid(friendUser.getOid());
        response.setSendTime(DateUtil.dateToStr(bottleMessage.getSendTime(), DateUtil.DATE_FORMAT));

        return response;
    }

    @Override
    @Transactional
    public void readMessage(UserEntity user, BottleMessageRequest request) {
        LambdaQueryWrapper<BottleSessionEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(BottleSessionEntity::getBottleId, request.getBottleId());
        lambdaQueryWrapper.eq(BottleSessionEntity::getUid, user.getUid());
        BottleSessionEntity bottleSession = bottleSessionService.getOne(lambdaQueryWrapper);

        LambdaQueryWrapper<UserMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserMessageEntity::getUid, user.getUid());
        wrapper.eq(UserMessageEntity::getMessageSource, MessageSource.BOTTLE.getValue());
        UserMessageEntity userMessageEntity = userMessageService.getOne(wrapper);
        if(userMessageEntity != null){
            userMessageEntity.setUnRead(userMessageEntity.getUnRead() - bottleSession.getUnRead());
            userMessageEntity.setUpdateTime(DateUtil.nowDateTime());
            userMessageService.updateById(userMessageEntity);
        }

        bottleSession.setUnRead(0);
        bottleSession.setUpdateTime(DateUtil.nowDateTime());
        bottleSessionService.update(bottleSession, lambdaQueryWrapper);

    }

    @Override
    public UserBottleResponse getUserBottle(UserEntity user) {
        UserBottleResponse response = new UserBottleResponse();
        LambdaQueryWrapper<UserBottleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserBottleEntity::getUid, user.getUid());
        UserBottleEntity userBottle = userBottleService.getOne(wrapper);
        if(userBottle == null){
            userBottle = new UserBottleEntity();
            userBottle.setUid(user.getUid());
            userBottle.setResiduePickTimes(Integer.parseInt(configBusinessService.getValue(Constant.PICK_BOTTLE_FREE_TIMES)));
            userBottle.setThrowTimes(0);
            userBottle.setRefreshTime(DateUtil.nowDateTime());
            userBottle.setCreateTime(DateUtil.nowDateTime());
            userBottleService.save(userBottle);
            response.setResiduePickTimes(Integer.parseInt(configBusinessService.getValue(Constant.PICK_BOTTLE_FREE_TIMES)));
            return response;
        }

        //次日刷新打捞次数/扔次数
        if(userBottle.getRefreshTime().compareTo(DateUtil.getToDay()) == -1){
            userBottle.setResiduePickTimes(Integer.parseInt(configBusinessService.getValue(Constant.PICK_BOTTLE_FREE_TIMES)));
            userBottle.setThrowTimes(0);
            userBottle.setRefreshTime(DateUtil.nowDateTime());
            userBottle.setUpdateTime(DateUtil.nowDateTime());
            userBottleService.updateById(userBottle);
        }

        response.setResiduePickTimes(userBottle.getResiduePickTimes());
        return response;
    }

    @Override
    public BottleSessionResponse getBottleSession(UserEntity user, Integer bottleId) {
        BottleSessionResponseDTO responseDTO = bottleSessionService.getBottleSession(user.getUid(), bottleId);
        return ObjectMapperUtil.convert(responseDTO, BottleSessionResponse.class);
    }

    private void pushSessionMessage(BottleSessionEntity friendBottleSession){
        //推送会话消息
        UserEntity senderUserInfo = userService.getUserByUid(friendBottleSession.getFriendUid());
        UserEntity receiverUserInfo = userService.getUserByUid(friendBottleSession.getUid());
        PushMessage pushMessage = new PushMessage();
        pushMessage.setSenderUid(friendBottleSession.getFriendUid());
        pushMessage.setReceiverUid(friendBottleSession.getUid());
        pushMessage.setType(PushMessageType.BOTTLE_SESSION.getValue());
        BottleSessionData bottleSessionData = ObjectMapperUtil.convert(friendBottleSession, BottleSessionData.class);
        bottleSessionData.setFriendOid(senderUserInfo.getOid());
        bottleSessionData.setFriendAvatar(senderUserInfo.getAvatar());
        bottleSessionData.setFriendUserName(senderUserInfo.getUserName());
        bottleSessionData.setLastMessageTime(DateUtil.dateToStr(friendBottleSession.getLastMessageTime(), DateUtil.DATE_FORMAT));

        LambdaQueryWrapper<PetalUnlockEntity> petalUnlockWrapper =new LambdaQueryWrapper<>();
        petalUnlockWrapper.eq(PetalUnlockEntity::getUid,receiverUserInfo.getUid());
        petalUnlockWrapper.eq(PetalUnlockEntity::getGuestUid,senderUserInfo.getUid());
        PetalUnlockEntity petalUnlockEntity = petalUnlockService.getOne(petalUnlockWrapper);
        if(petalUnlockEntity != null || receiverUserInfo.getVip() == Constant.VIP_USER){
            bottleSessionData.setLockFlag(0);
        }else{
            bottleSessionData.setLockFlag(1);
        }

        if(friendBottleSession.getUid() == friendBottleSession.getLastMessageUid()){
            bottleSessionData.setLastMessageOid(receiverUserInfo.getOid());
        }else{
            bottleSessionData.setLastMessageOid(senderUserInfo.getOid());
        }
        pushMessage.setData(bottleSessionData);

        //推送朋友会话mq消息
        messageProducer.sendMessage(Constant.DIRECT_EXCHANGE_NAME, Constant.PUSH_ROUTING_NAME, pushMessage);
    }

}