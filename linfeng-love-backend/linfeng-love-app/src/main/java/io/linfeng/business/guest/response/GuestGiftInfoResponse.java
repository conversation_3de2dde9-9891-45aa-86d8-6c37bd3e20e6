package io.linfeng.business.guest.response;

import io.linfeng.business.user.response.UserGiftResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(description="嘉宾礼物信息响应对象")
public class GuestGiftInfoResponse implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 用户名
	 */
	@ApiModelProperty(value = "用户名")
	private String userName;
	/**
	 * 头像
	 */
	@ApiModelProperty(value = "头像")
	private String avatar;
	/**
	 * 点亮数量
	 */
	@ApiModelProperty(value = "点亮数量")
	private Integer lightNumber;
	/**
	 * 礼物列表
	 */
	@ApiModelProperty(value = "礼物列表")
	private List<UserGiftResponse> userGiftList;

}
