package io.linfeng.business.offline.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="删除搭子请求对象")
public class PartnerDeleteRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 搭子活动ID
	 */
	@ApiModelProperty(value = "搭子活动ID")
	private Integer partnerId;

}
