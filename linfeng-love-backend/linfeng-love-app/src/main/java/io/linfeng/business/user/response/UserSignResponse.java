package io.linfeng.business.user.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="用户签到响应实体")
public class UserSignResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 常规奖励
	 */
	@ApiModelProperty(value = "常规奖励")
	private Integer prize;
	/**
	 * 额外奖励
	 */
	@ApiModelProperty(value = "额外奖励")
	private Integer extraPrize;
	/**
	 * 天数
	 */
	@ApiModelProperty(value = "天数")
	private Integer day;
	/**
	 * 签到标识
	 */
	@ApiModelProperty(value = "签到标识")
	private boolean flag;


}
