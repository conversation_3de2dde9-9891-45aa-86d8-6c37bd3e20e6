package io.linfeng.business.guest.response;

import io.linfeng.business.user.response.UserGiftResponse;
import io.linfeng.business.user.response.UserIntroResponse;
import io.linfeng.business.user.response.UserMediaResponse;
import io.linfeng.business.user.response.UserTagResponse;
import io.linfeng.business.moment.response.MomentMediaResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(description="嘉宾信息响应对象")
public class GuestResponse implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 嘉宾oid
	 */
	@ApiModelProperty(value = "嘉宾oid")
	private String oid;
	/**
	 * 用户名
	 */
	@ApiModelProperty(value = "用户名")
	private String userName;
	/**
	 * 头像
	 */
	@ApiModelProperty(value = "头像")
	private String avatar;
	/**
	 * 性别
	 */
	@ApiModelProperty(value = "性别")
	private Integer gender;
	/**
	 * 性别(字典翻译)
	 */
	@ApiModelProperty(value = "性别(字典翻译)")
	private String genderText;
	/**
	 * 出生年份
	 */
	@ApiModelProperty(value = "出生年份")
	private String birthdayYear;
	/**
	 * 年龄
	 */
	@ApiModelProperty(value = "年龄")
	private Integer age;
	/**
	 * 星座
	 */
	@ApiModelProperty(value = "星座")
	private String constellation;
	/**
	 * 身高
	 */
	@ApiModelProperty(value = "身高")
	private Integer stature;
	/**
	 * 体重
	 */
	@ApiModelProperty(value = "体重")
	private Integer weight;
	/**
	 * 家乡省份
	 */
	@ApiModelProperty(value = "家乡省份")
	private String homeProvince;
	/**
	 * 家乡城市
	 */
	@ApiModelProperty(value = "家乡城市")
	private String homeCity;
	/**
	 * 居住省份
	 */
	@ApiModelProperty(value = "居住省份")
	private String livingProvince;
	/**
	 * 居住城市
	 */
	@ApiModelProperty(value = "居住城市")
	private String livingCity;
	/**
	 * 婚姻状态(字典翻译)
	 */
	@ApiModelProperty(value = "婚姻状态(字典翻译)")
	private String marriageText;
	/**
	 * 毕业院校
	 */
	@ApiModelProperty(value = "毕业院校")
	private String school;
	/**
	 * 最高学历(字典翻译)
	 */
	@ApiModelProperty(value = "最高学历(字典翻译)")
	private String educationText;
	/**
	 * 行业/职业(字典翻译)
	 */
	@ApiModelProperty(value = "行业/职业(字典翻译)")
	private String jobText;
	/**
	 * 年薪(字典翻译)
	 */
	@ApiModelProperty(value = "年薪(字典翻译)")
	private String salaryText;
	/**
	 * 个性签名
	 */
	@ApiModelProperty(value = "个性签名")
	private String signature;
	/**
	 * 实名状态
	 */
	@ApiModelProperty(value = "实名状态")
	private Integer realNameStatus;
	/**
	 * 学历认证状态
	 */
	@ApiModelProperty(value = "学历认证状态")
	private Integer educationStatus;
	/**
	 * 工作认证状态
	 */
	@ApiModelProperty(value = "工作认证状态")
	private Integer jobStatus;
	/**
	 * 关注标识
	 */
	@ApiModelProperty(value = "关注标识")
	private Integer concernFlag;
	/**
	 * 配对状态
	 */
	@ApiModelProperty(value = "配对状态")
	private Integer coupleStatus;
	/**
	 * 朋友状态
	 */
	@ApiModelProperty(value = "朋友状态")
	private Integer friendStatus;
	/**
	 * 用户介绍
	 */
	@ApiModelProperty(value = "用户介绍")
	private List<UserIntroResponse> introList;

	/**
	 * 用户附件
	 */
	@ApiModelProperty(value = "用户附件")
	private List<UserMediaResponse> userMediaList;

	/**
	 * 动态数量
	 */
	@ApiModelProperty(value = "动态数量")
	private Integer momentListLength;
	/**
	 * 账号类型 1普通用户 2官方账号
	 */
	@ApiModelProperty(value = "账号类型")
	private Integer type;

	/**
	 * 动态附件
	 */
	@ApiModelProperty(value = "动态附件")
	private List<MomentMediaResponse> momentMediaList;

	/**
	 * 用户标签
	 */
	@ApiModelProperty(value = "用户标签")
	private List<UserTagResponse> userTagList;

	/**
	 * 礼物列表
	 */
	@ApiModelProperty(value = "礼物列表")
	private List<UserGiftResponse> userGiftList;
	/**
	 * 礼物点亮数量
	 */
	@ApiModelProperty(value = "礼物点亮数量")
	private Integer giftLightNumber;
	/**
	 * 语音持续时间
	 */
	@ApiModelProperty(value = "语音持续时间")
	private Integer voiceDuration;
	/**
	 * 语音地址
	 */
	@ApiModelProperty(value = "语音地址")
	private String voiceUrl;
	/**
	 * 背景图片
	 */
	@ApiModelProperty(value = "背景图片")
	private String backImage;

}
