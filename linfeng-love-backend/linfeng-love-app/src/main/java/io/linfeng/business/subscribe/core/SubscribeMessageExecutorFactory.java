package io.linfeng.business.subscribe.core;

import io.linfeng.business.subscribe.build.*;
import io.linfeng.common.exception.LinfengException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 
 * 订阅消息执行服务工厂
 * 
 * <AUTHOR>
 *
 */
@Component
@AllArgsConstructor
public class SubscribeMessageExecutorFactory {

	private OfflineActivityStartMessageExecutor offlineActivityStartMessageExecutor;

	private OfflinePartnerStartMessageExecutor offlinePartnerStartMessageExecutor;

	private HelloReplyMessageExecutor helloReplyMessageExecutor;

	private AccusationMessageExecutor accusationMessageExecutor;

	private EducationMessageExecutor educationMessageExecutor;

	private JobMessageExecutor jobMessageExecutor;
	
	public SubscribeMessageExecutor createSubscribeMessageBuildService(String type){
		if(type.equals("offlineActivityStart")){
			return offlineActivityStartMessageExecutor;
		}
		if(type.equals("offlinePartnerStart")){
			return offlinePartnerStartMessageExecutor;
		}
		if(type.equals("helloReply")){
			return helloReplyMessageExecutor;
		}
		if(type.equals("accusation")){
			return accusationMessageExecutor;
		}
		if(type.equals("education")){
			return educationMessageExecutor;
		}
		if(type.equals("job")){
			return jobMessageExecutor;
		}
		throw  new LinfengException("未找到消息执行主体");
	}

}
