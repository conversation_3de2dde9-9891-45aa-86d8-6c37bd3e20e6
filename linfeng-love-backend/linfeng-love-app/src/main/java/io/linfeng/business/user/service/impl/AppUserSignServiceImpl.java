package io.linfeng.business.user.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.business.user.response.UserSignResponse;
import io.linfeng.business.user.service.AppUserSignService;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.love.common.enums.PrizeOrigin;
import io.linfeng.love.common.enums.PrizeType;
import io.linfeng.love.config.entity.ConfigSignEntity;
import io.linfeng.love.config.service.ConfigBusinessService;
import io.linfeng.love.config.service.ConfigSignService;
import io.linfeng.love.executor.PrizeEventExecutor;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.entity.UserSignEntity;
import io.linfeng.love.user.service.UserSignService;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("appUserSignService")
public class AppUserSignServiceImpl implements AppUserSignService {

    private final UserSignService userSignService;

    private final ConfigSignService configSignService;

    private final PrizeEventExecutor prizeEventExecutor;

    private final ConfigBusinessService configBusinessService;

    public AppUserSignServiceImpl(UserSignService userSignService, ConfigSignService configSignService, PrizeEventExecutor prizeEventExecutor, ConfigBusinessService configBusinessService) {
        this.userSignService = userSignService;
        this.configSignService = configSignService;
        this.prizeEventExecutor = prizeEventExecutor;
        this.configBusinessService = configBusinessService;
    }

    @Override
    public Map<String, Object> getUserSignDetail(UserEntity user) {
        Map<String, Object> result = new HashMap<>();
        List<ConfigSignEntity> configSignList = configSignService.getConfigSignList();
        LambdaQueryWrapper<UserSignEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserSignEntity::getUid, user.getUid());
        UserSignEntity userSignEntity = userSignService.getOne(wrapper);

        boolean toDaySign = userSignEntity != null && userSignEntity.getLastSignTime().compareTo(DateUtil.getToDay()) == 1;
        Integer continuousDay;
        //最后一次签到时间在昨天之前则视为签到中断
        //连续签到7天并且今日未签到的重置签到
        if(userSignEntity == null || DateUtil.daysBetween(userSignEntity.getLastSignTime(), DateUtil.getToDay()) >= 1
        ||(userSignEntity.getSignDays() == configSignList.size() && !toDaySign)){
            continuousDay = 0;
        }else{
            continuousDay = userSignEntity.getSignDays();
        }

        List<UserSignResponse> userSignResponseList = new ArrayList<>();
        for(int i=0; i<configSignList.size(); i++){
            UserSignResponse userSignResponse = new UserSignResponse();
            if(user.getVip() == Constant.VIP_USER){
                int multiple = Integer.parseInt(configBusinessService.getValue(Constant.VIP_PETAL));
                userSignResponse.setPrize(configSignList.get(i).getPrize() * multiple);
                userSignResponse.setExtraPrize(configSignList.get(i).getExtraPrize() * multiple);
            }else{
                userSignResponse.setPrize(configSignList.get(i).getPrize());
                userSignResponse.setExtraPrize(configSignList.get(i).getExtraPrize());
            }
            userSignResponse.setDay(configSignList.get(i).getDay());
            userSignResponse.setFlag(continuousDay >= configSignList.get(i).getDay());
            userSignResponseList.add(userSignResponse);

        }
        result.put("toDaySign", toDaySign);
        result.put("continuousDay", continuousDay);
        result.put("userSignList", userSignResponseList);

        return result;
    }

    @Override
    public void signSubmit(UserEntity user) {
        List<ConfigSignEntity> configSignList = configSignService.getConfigSignList();
        LambdaQueryWrapper<UserSignEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserSignEntity::getUid, user.getUid());
        UserSignEntity userSignEntity = userSignService.getOne(wrapper);
        Integer prizeValue = 0;
        Integer extraPrizeValue = 0;
        if(userSignEntity == null){
            userSignEntity = new UserSignEntity();
            userSignEntity.setUid(user.getUid());
            userSignEntity.setSignDays(1);
            userSignEntity.setLastSignTime(DateUtil.nowDateTime());
            userSignEntity.setCreateTime(DateUtil.nowDateTime());
            userSignService.save(userSignEntity);
            prizeValue = configSignList.get(0).getPrize();
            extraPrizeValue = configSignList.get(0).getExtraPrize();
        }else{
            if(userSignEntity.getLastSignTime().compareTo(DateUtil.nowDateTime()) == 1){
                throw new LinfengException("今日已签到");
            }
            if(userSignEntity.getSignDays() == configSignList.size()
            || DateUtil.daysBetween(userSignEntity.getLastSignTime(), DateUtil.getToDay()) >= 1){
                userSignEntity.setSignDays(1);
            }else{
                userSignEntity.setSignDays(userSignEntity.getSignDays() + 1);
            }
            userSignEntity.setLastSignTime(DateUtil.nowDateTime());
            userSignEntity.setUpdateTime(DateUtil.nowDateTime());
            userSignService.updateById(userSignEntity);

            for (ConfigSignEntity configSign : configSignList){
                if (configSign.getDay() == userSignEntity.getSignDays()){
                    prizeValue = configSign.getPrize();
                    extraPrizeValue = configSign.getExtraPrize();
                    break;
                }
            }
        }

        if(user.getVip() == Constant.VIP_USER){
            int multiple = Integer.parseInt(configBusinessService.getValue(Constant.VIP_PETAL));
            prizeValue = prizeValue * multiple;
            extraPrizeValue = extraPrizeValue * multiple;
        }

        prizeEventExecutor.awardPrize(user, PrizeType.PETAL_LIMIT.getValue(), prizeValue + extraPrizeValue, PrizeOrigin.SIGN.getValue());
    }


}