/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.chat.service;


import io.linfeng.business.chat.response.ChatSessionResponse;
import io.linfeng.love.chat.entity.ChatSessionEntity;
import io.linfeng.love.user.entity.UserEntity;

import java.util.List;

/**
 * 聊天会话服务
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
public interface AppChatSessionService {

    /**
     * 查询所有会话
     * @param user 用户
     * @return 聊天会话列表
     */
    List<ChatSessionResponse> getAllSessionList(UserEntity user);

    /**
     * 创建或更新会话
     * @param newChatSession 会话
     * @return 会话id
     */
    String createOrUpdateSession(ChatSessionEntity newChatSession);

    /**
     * 查询会话详情
     * @param uid 用户id
     * @param friendOid 朋友oid
     * @return 会话详情
     */
    ChatSessionResponse getSessionResponse(Integer uid, String friendOid);

    /**
     * 删除会话
     * @param user 用户
     * @param sessionId 会话id
     */
    void deleteSession(UserEntity user, String sessionId);
}


