/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.question.service;


import io.linfeng.business.question.response.QuestionGroupResponse;
import io.linfeng.business.question.response.QuestionResponse;
import io.linfeng.business.topic.response.TopicResponse;

import java.util.List;

/**
 * 话题业务服务
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
public interface AppQuestionService {

    /**
     * 查询问题列表
     * @return 问题列表
     */
    List<QuestionGroupResponse> getQuestionList();

    /**
     * 查询问题详情
     * @param id 问题id
     * @return 问题详情
     */
    QuestionResponse getQuestionById(Integer id);
}

