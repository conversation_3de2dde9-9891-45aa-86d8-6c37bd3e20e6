/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.user.controller;


import io.linfeng.business.user.response.UserGiftResponse;
import io.linfeng.business.user.service.AppUserGiftService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * 用户礼物Api
 */
@RestController
@RequestMapping("/app/gift")
@Api(tags = "用户礼物Api")
public class AppUserGiftController {

    private final AppUserGiftService appUserGiftService;

    public AppUserGiftController(AppUserGiftService appUserGiftService) {
        this.appUserGiftService = appUserGiftService;
    }


    /**
     * 用户礼物列表
     * @param user 登录用户
     * @return 用户礼物列表
     */
    @Login
    @GetMapping ("/list")
    @ApiOperation("用户礼物列表")
    public Result<List<UserGiftResponse>> list(@ApiIgnore @LoginUser UserEntity user){
        List<UserGiftResponse> userGiftList = appUserGiftService.getUserGiftList(user);
        return new Result<List<UserGiftResponse>>().ok(userGiftList);
    }

}
