package io.linfeng.business.user.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description="用户互动消息详情响应实体")
public class UserMessageInteractResponse {
    /**
     * 用户oid
     */
    @ApiModelProperty(value = "嘉宾oid")
    private String guestOid;
    /**
     * 用户名
     */
    @ApiModelProperty(value = "嘉宾名称")
    private String guestUserName;
    /**
     * 用户头像
     */
    @ApiModelProperty(value = "嘉宾头想")
    private String guestAvatar;
    /**
     * 内容
     */
    @ApiModelProperty(value = "消息类型")
    private String content;
    /**
     * 类型
     */
    @ApiModelProperty(value = "消息类型")
    private String dataType;
    /**
     * 跳转图片
     */
    @ApiModelProperty(value = "消息类型")
    private String linkImage;
    /**
     * 跳转链接
     */
    @ApiModelProperty(value = "消息类型")
    private String linkUrl;
    /**
     * 发送时间
     */
    @ApiModelProperty(value = "消息类型")
    private String sendTime;

}
