/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  *********/**********
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.user.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.moment.request.MomentQueryRequest;
import io.linfeng.business.user.request.AccountRecordQueryRequest;
import io.linfeng.business.user.response.UserAccountRecordResponse;
import io.linfeng.business.user.response.UserAccountResponse;
import io.linfeng.business.user.service.AppUserAccountService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * 用户账户Api
 */
@RestController
@RequestMapping("/app/account")
@Api(tags = "用户账户Api")
public class AppUserAccountController {

    private final AppUserAccountService appUserAccountService;

    public AppUserAccountController(AppUserAccountService appUserAccountService) {
        this.appUserAccountService = appUserAccountService;
    }


    /**
     * 用户账户信息
     * @param user 登录用户
     * @return 用户账户信息
     */
    @Login
    @GetMapping ()
    @ApiOperation("用户账户信息")
    public Result<UserAccountResponse> info(@ApiIgnore @LoginUser UserEntity user){
        UserAccountResponse userAccountList = appUserAccountService.getUserAccount(user);
        return new Result<UserAccountResponse>().ok(userAccountList);
    }

    /**
     * 用户账户明细列表（分页）
     * @param user 登录用户
     * @return 用户账户明细列表
     */
    @Login
    @GetMapping ("/record/list")
    @ApiOperation("用户账户明细列表")
    public Result<IPage<UserAccountRecordResponse>> recordList(@ApiIgnore @LoginUser UserEntity user, AccountRecordQueryRequest request){
        IPage<UserAccountRecordResponse> userAccountRecordPage = appUserAccountService.getUserAccountRecordPage(user, request);
        return new Result<IPage<UserAccountRecordResponse>>().ok(userAccountRecordPage);
    }


}
