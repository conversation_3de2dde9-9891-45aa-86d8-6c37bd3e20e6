package io.linfeng.business.mission.response;

import io.linfeng.love.mission.dto.response.MissionDetailResponseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(description="动态响应对象")
public class MissionResponse implements Serializable {

	/**
	 * 任务id
	 */
	@ApiModelProperty(value="任务id")
	private String missionId;
	/**
	 * 任务名称
	 */
	@ApiModelProperty(value="任务名称")
	private String missionName;
	/**
	 * 任务描述
	 */
	@ApiModelProperty(value="任务描述")
	private String description;
	/**
	 * 任务类型
	 */
	@ApiModelProperty(value="任务类型")
	private Integer type;
	/**
	 * 图标
	 */
	@ApiModelProperty(value="图标")
	private String icon;
	/**
	 * 引导跳转路径
	 */
	@ApiModelProperty(value="引导跳转路径")
	private String guidePath;
	/**
	 * 是否导航页
	 */
	@ApiModelProperty(value="是否导航页")
	private Integer guideTabBar;
	/**
	 * 引导文字
	 */
	@ApiModelProperty(value="引导文字")
	private String guideText;
	/**
	 * 状态
	 */
	@ApiModelProperty(value="状态")
	private Integer status;
	/**
	 * 当前阶段值
	 */
	@ApiModelProperty(value="当前阶段值")
	private Integer currentValue;
	/**
	 * 下一个目标值(基础任务和最大目标值一致)
	 */
	@ApiModelProperty(value="下一个目标值")
	private Integer nextTargetValue;
	/**
	 * 下一个奖品(基础任务和最大奖品一致)
	 */
	@ApiModelProperty(value="下一个奖品")
	private String nextPrizeName;
	/**
	 * 下一个奖品奖励值(基础任务和最大奖品奖励值一致)
	 */
	@ApiModelProperty(value="下一个奖品奖励值")
	private Integer nextPrizeValue;
	/**
	 * 最大目标值
	 */
	@ApiModelProperty(value="最大目标值")
	private Integer maxTargetValue;
	/**
	 * 最大奖品
	 */
	@ApiModelProperty(value="最大奖品")
	private String maxPrizeName;
	/**
	 * 最大奖品奖励值
	 */
	@ApiModelProperty(value="最大奖品奖励值")
	private Integer maxPrizeValue;
	/**
	 * 详情列表（成长任务才有值）
	 */
	@ApiModelProperty(value="详情列表")
	private List<MissionDetailResponse> detailList;

}
