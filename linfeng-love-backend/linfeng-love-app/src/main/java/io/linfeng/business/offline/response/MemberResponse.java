package io.linfeng.business.offline.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
@ApiModel(description="搭子成员响应对象")
public class MemberResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 成员oid
	 */
	@ApiModelProperty(value = "成员oid")
	private String oid;
	/**
	 * 手机号
	 */
	@ApiModelProperty(value = "手机号")
	private String mobile;
	/**
	 * 头像
	 */
	@ApiModelProperty(value = "头像")
	private String avatar;
	/**
	 * 昵称
	 */
	@ApiModelProperty(value = "昵称")
	private String userName;
}
