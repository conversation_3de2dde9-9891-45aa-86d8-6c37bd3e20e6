package io.linfeng.business.user.service;

import io.linfeng.business.user.request.UserPreferencesRequest;
import io.linfeng.business.user.response.UserPreferencesResponse;
import io.linfeng.love.user.entity.UserEntity;

/**
 * 用户偏好信息业务服务
 *
 * <AUTHOR>
 * @date 2023-09-06 16:22:14
 */
public interface AppUserPreferencesService {

    /**
     * 获取用户偏好详情
     * @param user 登录用户
     * @return 用户偏好详情
     */
    UserPreferencesResponse getUserPreferences(UserEntity user);

    /**
     * 用户偏好修改
     * @param user 登录用户
     * @param request 用户偏好信息
     * @return 修改结果
     */
    void updateAppUserPreferences(UserEntity user, UserPreferencesRequest request);
}

