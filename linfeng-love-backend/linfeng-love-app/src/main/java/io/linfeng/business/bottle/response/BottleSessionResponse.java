package io.linfeng.business.bottle.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="漂流瓶会话")
public class BottleSessionResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 漂流瓶id
	 */
	@ApiModelProperty(value = "漂流瓶id")
	private Integer bottleId;

	/**
	 * 朋友oid
	 */
	@ApiModelProperty(value = "朋友oid")
	private String friendOid;

	/**
	 * 好友头像
	 */
	@ApiModelProperty(value = "好友头像")
	private String friendAvatar;

	/**
	 * 朋友用户名
	 */
	@ApiModelProperty(value = "朋友用户名")
	private String friendUserName;
	/**
	 * 最后一条消息Oid
	 */
	@ApiModelProperty(value = "最后一条消息Oid")
	private String lastMessageOid;
	/**
	 * 最后一条消息类型
	 */
	@ApiModelProperty(value = "最后一条消息类型")
	private Integer lastMessageType;
	/**
	 * 最后一条消息内容
	 */
	@ApiModelProperty(value = "最后一条消息内容")
	private String lastMessageContent;
	/**
	 * 最后一条消息时间
	 */
	@ApiModelProperty(value = "最后一条消息时间")
	private String lastMessageTime;
	/**
	 * 未读消息数量
	 */
	@ApiModelProperty(value = "未读消息数量")
	private Integer unRead;

	/**
	 * 锁定标识
	 */
	@ApiModelProperty(value = "锁定标识")
	private Integer lockFlag;


}
