/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.accusation.controller;

import io.linfeng.business.accusation.request.AccusationRequest;
import io.linfeng.business.accusation.response.AccusationResponse;
import io.linfeng.business.accusation.service.AppAccusationService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


/**
 * 用户举报Api
 *
 * <AUTHOR> CX
 * @date 2023-09-28 14:26:17
 */
@RestController
@RequestMapping("app/accusation")
@Api(tags = "用户举报Api")
public class AppAccusationController {

    private final AppAccusationService appAccusationService;

    public AppAccusationController(AppAccusationService appAccusationService) {
        this.appAccusationService = appAccusationService;
    }

    /**
     * 举报提交
     * @param user 登录用户
     * @param request 举报请求
     * @return 结果
     */
    @Login
    @ApiOperation(value = "举报提交")
    @PostMapping("/submit")
    public R submit(@ApiIgnore @LoginUser UserEntity user, @RequestBody AccusationRequest request) {
        Integer accusationId = appAccusationService.submit(user, request);
        return R.ok().put("accusationId", accusationId);
    }

    /**
     * 举报列表
     * @param user 登录用户
     * @param status 查询状态
     * @return 举报列表
     */
    @Login
    @GetMapping("/list")
    @ApiOperation("举报列表")
    public Result<List<AccusationResponse>> list(@ApiIgnore @LoginUser UserEntity user, Integer status){
        List<AccusationResponse> accusationList = appAccusationService.getAccusationList(user, status);
        return new Result<List<AccusationResponse>>().ok(accusationList);
    }

    /**
     * 举报详情
     * @param user 登录用户
     * @param id 举报id
     * @return 举报详情
     */
    @Login
    @GetMapping("/detail")
    @ApiOperation("举报详情")
    public Result<AccusationResponse> detail(@ApiIgnore @LoginUser UserEntity user, Integer id){
        AccusationResponse accusation = appAccusationService.getAccusationDetail(user, id);
        return new Result<AccusationResponse>().ok(accusation);
    }

}
