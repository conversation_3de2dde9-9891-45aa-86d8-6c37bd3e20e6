package io.linfeng.business.petal.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
@ApiModel(description="用户账户记录响应实体")
public class PetalRecordResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 类型（1充值、2消费）
	 */
	@ApiModelProperty(value = "类型（1充值、2消费）")
	private Integer type;
	/**
	 * 子类型
	 */
	@ApiModelProperty(value = "子类型")
	private Integer subType;
	/**
	 * 永久花瓣
	 */
	@ApiModelProperty(value = "永久花瓣")
	private BigDecimal amountForever;
	/**
	 * 临时花瓣
	 */
	@ApiModelProperty(value = "临时花瓣")
	private BigDecimal amountLimit;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

}
