package io.linfeng.business.pay.service;

import io.linfeng.business.offline.request.ActivityExitRequest;
import io.linfeng.business.offline.request.ActivityJoinRequest;
import io.linfeng.business.pay.request.PayRequest;
import io.linfeng.business.pay.response.PayResultResponse;
import io.linfeng.business.pay.response.PrePayResultResponse;
import io.linfeng.business.petal.request.PetalRechargeRequest;
import io.linfeng.business.vip.request.VipRechargeRequest;
import io.linfeng.love.user.entity.UserEntity;

import javax.servlet.http.HttpServletRequest;

/**
 * App支付服务
 *
 * <AUTHOR>
 * @date 2023-09-06 16:22:14
 */
public interface AppPayService {

    /**
     * 支付结果回调
     * @param xmlData
     */
    void callBack(String xmlData);

    /**
     * 会员预支付订单生成
     * @param user 登录用户
     * @param request 订单请求
     * @param httpRequest http请求
     * @return 预支付订单
     * @throws Exception
     */
    PrePayResultResponse vipPrePay(UserEntity user, VipRechargeRequest request, HttpServletRequest httpRequest);

    /**
     * 花瓣充值预支付订单生成
     * @param user 登录用户
     * @param request 充值请求对象
     * @param httpRequest http请求
     * @return 预支付订单
     */
    PrePayResultResponse petalPrePay(UserEntity user, PetalRechargeRequest request, HttpServletRequest httpRequest);

    /**
     * 活动加入预支付订单生成
     * @param user 登录用户
     * @param request 充值请求对象
     * @param httpRequest http请求
     * @return 预支付订单
     */
    PrePayResultResponse activityJoinPrePay(UserEntity user, ActivityJoinRequest request, HttpServletRequest httpRequest);

    /**
     * 查询支付订单
     * @param user 登录用户
     * @param orderNo 支付订单号
     * @return 支付结果
     */
    PayResultResponse queryPayResult(UserEntity user, String orderNo);

    /**
     * 退款结果回调
     * @param xmlData
     */
    void refundCallBack(String xmlData);

    /**
     * 支付取消
     * @param user 登录用户
     * @param orderNo 订单号
     */
    void cancelPay(UserEntity user, String orderNo);

    /**
     * 解锁精选嘉宾预支付订单生成
     * @param user 登录用户
     * @param httpRequest 请求对象
     * @return 预支付订单
     */
    PrePayResultResponse qualityUnlockPrePay(UserEntity user, PayRequest request, HttpServletRequest httpRequest);
}

