package io.linfeng.business.guest.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(description="推荐请求对象")
public class RecommendQueryRequest implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 1刷新、2新增
	 */
	private Integer type;

}
