/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.mission.service;


import io.linfeng.business.mission.response.MissionResponse;
import io.linfeng.love.user.entity.UserEntity;

import java.util.List;

/**
 * 任务服务
 * 业务系统目前采用异步线程池的方式来和任务系统交互
 * 用户体量如果比较大，这边可以将任务体系设计成单独系统，设计思路如下：
 * 1、业务系统与任务系统通过消息中间件mq降低两者的耦合度，同时提升业务系统的响应速度
 * 2、领取任务、更新进度、任务达标等服务通过接收消息事件来处理
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
public interface AppMissionService {

    /**
     * 查询任务列表
     * @param user 用户
     * @param activityCode 活动编码
     * @return 任务列表
     */
    List<MissionResponse> getMissionList(UserEntity user, String activityCode);

    /**
     * 领取今日任务
     * @param user 用户
     */
    void receiveToDayMissionList(UserEntity user);

}

