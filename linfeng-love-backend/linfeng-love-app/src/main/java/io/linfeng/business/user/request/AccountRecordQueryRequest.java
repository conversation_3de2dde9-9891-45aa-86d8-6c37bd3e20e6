package io.linfeng.business.user.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="用户账户记录请求对象")
public class AccountRecordQueryRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 1收入/2提现
	 */
	@ApiModelProperty(value = "类型")
	private Integer type;
	/**
	 * 页数
	 */
	@ApiModelProperty(value = "页数")
	private Integer pageNum;
	/**
	 * 页码
	 */
	@ApiModelProperty(value = "页码")
	private Integer pageSize;

}
