/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.offline.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.offline.request.ActivityExitRequest;
import io.linfeng.business.offline.request.ActivityJoinRequest;
import io.linfeng.business.offline.request.ActivityQueryRequest;
import io.linfeng.business.offline.response.ActivityDetailResponse;
import io.linfeng.business.offline.response.ActivitySimpleResponse;
import io.linfeng.business.offline.service.AppOfflineActivityService;
import io.linfeng.business.pay.response.PrePayResultResponse;
import io.linfeng.business.pay.service.AppPayService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.config.entity.ConfigOfflineCityEntity;
import io.linfeng.love.config.service.ConfigOfflineCityService;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * 线下活动
 *
 * <AUTHOR>
 * @date 2024-03-28 14:26:17
 */
@RestController
@RequestMapping("app/offline/activity")
@Api(tags = "线下活动Api")
public class AppOfflineActivityController {

    private final AppOfflineActivityService appOfflineActivityService;

    private final AppPayService appPayService;

    private final ConfigOfflineCityService configOfflineCityService;

    public AppOfflineActivityController(AppOfflineActivityService appOfflineActivityService, AppPayService appPayService, ConfigOfflineCityService configOfflineCityService) {
        this.appOfflineActivityService = appOfflineActivityService;
        this.appPayService = appPayService;
        this.configOfflineCityService = configOfflineCityService;
    }

    /**
     * 线下活动列表
     * @param user 登录用户
     * @return 线下活动列表
     */
    @Login
    @GetMapping("/list")
    @ApiOperation("线下活动列表")
    public Result<IPage<ActivitySimpleResponse>> list(@ApiIgnore @LoginUser UserEntity user, ActivityQueryRequest request){
        IPage<ActivitySimpleResponse> activityList = appOfflineActivityService.getOfflineActivityList(user, request);
        return new Result<IPage<ActivitySimpleResponse>>().ok(activityList);
    }

    /**
     * 我的活动列表
     * @param user 登录用户
     * @return 我的活动列表
     */
    @Login
    @GetMapping("/list/my")
    @ApiOperation("我的活动列表")
    public Result<IPage<ActivitySimpleResponse>> myList(@ApiIgnore @LoginUser UserEntity user, ActivityQueryRequest request) {
        IPage<ActivitySimpleResponse> activityList = appOfflineActivityService.getMyActivityList(user, request);
        return new Result<IPage<ActivitySimpleResponse>>().ok(activityList);
    }


        /**
         * 查询活动详情
         * @param user 登录用户
         * @param id 查询请求
         * @return 活动详情
         */
    @Login
    @GetMapping("/detail/{id}")
    @ApiOperation("查询活动详情")
    public Result<ActivityDetailResponse> detail(@ApiIgnore @LoginUser UserEntity user, @PathVariable("id") Integer id){
        ActivityDetailResponse activityDetail = appOfflineActivityService.getOfflineActivityDetail(user, id);
        return new Result<ActivityDetailResponse>().ok(activityDetail);
    }

    /**
     * 加入活动
     * @param user 登录用户
     * @param request 加入活动请求
     * @return 加入结果
     */
    @Login
    @ApiOperation(value = "加入活动")
    @PostMapping("/join")
    public Result<PrePayResultResponse> join(@ApiIgnore @LoginUser UserEntity user, @RequestBody ActivityJoinRequest request, HttpServletRequest httpRequest){
        PrePayResultResponse payResult = appPayService.activityJoinPrePay(user, request, httpRequest);
        return new Result<PrePayResultResponse>().ok(payResult);
    }

    /**
     * 退出活动
     * @param user 登录用户
     * @param request 加入活动请求
     * @return 退出结果
     */
    @Login
    @ApiOperation(value = "退出活动")
    @PostMapping("/exit")
    public R exit(@ApiIgnore @LoginUser UserEntity user, @RequestBody ActivityExitRequest request){
        appOfflineActivityService.activityExitApply(user, request);
        return R.ok();
    }

    /**
     * 城市列表
     */
    @RequestMapping("/city")
    public R city(){
        List<ConfigOfflineCityEntity> list = configOfflineCityService.list();
        return R.ok().put("list", list);
    }

}
