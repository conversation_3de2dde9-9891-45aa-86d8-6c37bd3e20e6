/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.topic.controller;

import io.linfeng.business.topic.response.TopicResponse;
import io.linfeng.business.topic.service.AppTopicService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.api.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 话题Api
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
@RestController
@RequestMapping("app/topic")
@Api(tags = "话题Api")
public class AppTopicController {

    private final AppTopicService appTopicService;

    public AppTopicController(AppTopicService appTopicService) {
        this.appTopicService = appTopicService;
    }

    /**
     * 查询话题列表
     * @return 话题列表
     */
    @Login
    @GetMapping("/list")
    @ApiOperation("话题列表")
    public Result<List<TopicResponse>> list(){
        List<TopicResponse> topicList = appTopicService.getTopicList();
        return new Result<List<TopicResponse>>().ok(topicList);
    }

    /**
     * 查询热门话题列表
     * @return 热门话题列表
     */
    @Login
    @GetMapping("/hot/list")
    @ApiOperation("热门话题列表")
    public Result<List<TopicResponse>> hotList(){
        List<TopicResponse> topicList = appTopicService.getHotList();
        return new Result<List<TopicResponse>>().ok(topicList);
    }

    /**
     * 查询话题详情
     * @param topicId 话题id
     * @return 话题详情
     */
    @Login
    @GetMapping("/detail")
    @ApiOperation("话题详情")
    public Result<TopicResponse> detail(Integer topicId){
        TopicResponse topicResponse = appTopicService.getTopicById(topicId);
        return new Result<TopicResponse>().ok(topicResponse);
    }

}
