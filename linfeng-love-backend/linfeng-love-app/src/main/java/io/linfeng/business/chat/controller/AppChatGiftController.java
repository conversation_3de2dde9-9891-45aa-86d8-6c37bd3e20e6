/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245/793326982
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.chat.controller;


import io.linfeng.business.chat.response.ChatMessageResponse;
import io.linfeng.business.chat.request.ChatGiftRequest;
import io.linfeng.business.chat.service.AppChatGiftService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 聊天礼物Api
 */
@RestController
@RequestMapping("/app/chat/gift")
@Api(tags = "聊天礼物Api")
public class AppChatGiftController {

    private final AppChatGiftService appChatGiftService;

    public AppChatGiftController(AppChatGiftService appChatGiftService) {
        this.appChatGiftService = appChatGiftService;
    }

    /**
     * 礼物赠送
     * @param user 登录用户
     * @param request 操作请求
     * @return 操作结果
     */
    @Login
    @PostMapping("/give")
    @ApiOperation("礼物赠送")
    public Result<ChatMessageResponse> give(@ApiIgnore @LoginUser UserEntity user, @RequestBody ChatGiftRequest request){
        ChatMessageResponse chatMessageResponse = appChatGiftService.giveGift(user, request);
        return new Result<ChatMessageResponse>().ok(chatMessageResponse);
    }

}
