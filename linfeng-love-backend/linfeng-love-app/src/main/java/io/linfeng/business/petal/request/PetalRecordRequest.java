package io.linfeng.business.petal.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "花瓣记录查询请求对象")
public class PetalRecordRequest {

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private Integer type;
    /**
     * 子类型
     */
    @ApiModelProperty(value = "子类型")
    private Integer subType;
    /**
     * 页数
     */
    @ApiModelProperty(value = "页数")
    private Integer pageNum;
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer pageSize;

}
