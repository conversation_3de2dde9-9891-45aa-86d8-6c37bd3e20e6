package io.linfeng.business.question.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="问题响应对象")
public class QuestionResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 问题id
	 */
	@ApiModelProperty(value = "问题id")
	private Integer id;
	/**
	 * 跳转类型
	 */
	@ApiModelProperty(value = "跳转类型")
	private Integer linkType;
	/**
	 * 问题内容
	 */
	@ApiModelProperty(value = "问题内容")
	private String question;
	/**
	 * 回答内容
	 */
	@ApiModelProperty(value = "回答内容")
	private String answer;

}
