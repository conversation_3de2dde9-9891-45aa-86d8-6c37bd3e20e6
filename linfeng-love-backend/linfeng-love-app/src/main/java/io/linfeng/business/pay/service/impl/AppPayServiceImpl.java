package io.linfeng.business.pay.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import io.linfeng.business.guest.service.AppGuestQualityService;
import io.linfeng.business.offline.request.ActivityJoinRequest;
import io.linfeng.business.offline.service.AppOfflineActivityService;
import io.linfeng.business.pay.request.PayRequest;
import io.linfeng.business.pay.response.PayResultResponse;
import io.linfeng.business.pay.response.PrePayResultResponse;
import io.linfeng.business.pay.service.AppPayService;
import io.linfeng.business.petal.request.PetalRechargeRequest;
import io.linfeng.business.petal.service.AppPetalService;
import io.linfeng.business.vip.request.VipRechargeRequest;
import io.linfeng.business.vip.service.AppVipService;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.*;
import io.linfeng.love.config.entity.VipOptionEntity;
import io.linfeng.love.config.service.ConfigBusinessService;
import io.linfeng.love.config.service.ConfigSystemService;
import io.linfeng.love.config.service.VipOptionService;
import io.linfeng.love.guest.dto.response.GuestSimpleResponseDTO;
import io.linfeng.love.offline.entity.OfflineActivityEntity;
import io.linfeng.love.offline.entity.OfflineActivityJoinEntity;
import io.linfeng.love.offline.enums.ActivityJoinStatus;
import io.linfeng.love.offline.service.OfflineActivityJoinService;
import io.linfeng.love.offline.service.OfflineActivityService;
import io.linfeng.love.pay.entity.PayOrderEntity;
import io.linfeng.love.pay.entity.PayRefundEntity;
import io.linfeng.love.pay.enums.BusinessType;
import io.linfeng.love.pay.enums.PayStatus;
import io.linfeng.love.pay.enums.RefundStatus;
import io.linfeng.love.pay.service.PayOrderService;
import io.linfeng.love.pay.service.PayRefundService;
import io.linfeng.love.petal.entity.PetalOptionEntity;
import io.linfeng.love.petal.service.PetalOptionService;
import io.linfeng.love.user.entity.UserEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Service("appPayService")
public class AppPayServiceImpl implements AppPayService {

    private static final String CACHE_USER_QUALITY_PREFIX = "quality:";

    private final WxPayService wxPayService;

    private final PayOrderService payOrderService;

    private final PayRefundService payRefundService;

    private final VipOptionService vipOptionService;

    private final AppVipService appVipService;

    private final PetalOptionService petalOptionService;

    private final AppPetalService appPetalService;

    private final ConfigSystemService configSystemService;

    private final ConfigBusinessService configBusinessService;

    private final OfflineActivityService offlineActivityService;

    private final OfflineActivityJoinService offlineActivityJoinService;

    private final AppOfflineActivityService appOfflineActivityService;

    private final AppGuestQualityService appGuestQualityService;

    private final RedisUtil redisUtils;

    public AppPayServiceImpl(WxPayService wxPayService, PayOrderService payOrderService, PayRefundService payRefundService, VipOptionService vipOptionService, AppVipService appVipService, PetalOptionService petalOptionService, AppPetalService appPetalService, ConfigSystemService configSystemService, ConfigBusinessService configBusinessService, OfflineActivityService offlineActivityService, OfflineActivityJoinService offlineActivityJoinService, AppOfflineActivityService appOfflineActivityService, AppGuestQualityService appGuestQualityService, RedisUtil redisUtils) {
        this.wxPayService = wxPayService;
        this.payOrderService = payOrderService;
        this.payRefundService = payRefundService;
        this.vipOptionService = vipOptionService;
        this.appVipService = appVipService;
        this.petalOptionService = petalOptionService;
        this.appPetalService = appPetalService;
        this.configSystemService = configSystemService;
        this.configBusinessService = configBusinessService;
        this.offlineActivityService = offlineActivityService;
        this.offlineActivityJoinService = offlineActivityJoinService;
        this.appOfflineActivityService = appOfflineActivityService;
        this.appGuestQualityService = appGuestQualityService;
        this.redisUtils = redisUtils;
    }

    @Override
    @Transactional
    public void callBack(String xmlData) {
        log.info("支付回调报文[{}]", xmlData);
        try{
            WxPayOrderNotifyResult notifyResult = wxPayService.parseOrderNotifyResult(xmlData);

            String orderNo = notifyResult.getOutTradeNo();

            if(StringUtil.isEmpty(orderNo)){
                throw new LinfengException("微信支付回调异常-订单号为空");
            }

            LambdaQueryWrapper<PayOrderEntity> wrapper =new LambdaQueryWrapper<>();
            wrapper.eq(PayOrderEntity::getOrderNo, orderNo);
            PayOrderEntity payOrder = payOrderService.getOne(wrapper);

            if (payOrder == null){
                throw new LinfengException("微信支付回调异常-订单不存在");
            }

            if (payOrder.getStatus() == PayStatus.SUCCESS.getValue()){
                throw new LinfengException("微信支付回调异常-订单状态已完成");
            }
            payOrder.setResultTime(DateUtil.nowDateTime());
            payOrder.setResultMessage(notifyResult.getReturnMsg());
            payOrder.setOrderNo(notifyResult.getOutTradeNo());
            payOrder.setOutOrderNo(notifyResult.getTransactionId());
            payOrder.setResultMessage(notifyResult.getReturnMsg());
            payOrder.setUpdateTime(DateUtil.nowDateTime());
            if(notifyResult.getResultCode().equals(Constant.CALLBACK_SUCCESS)){
                payOrder.setStatus(PayStatus.SUCCESS.getValue());
            }else{
                payOrder.setStatus(PayStatus.FAIL.getValue());
            }

            payOrderService.updateById(payOrder);

            if(BusinessType.VIP.getValue() == payOrder.getBusinessType()){
                appVipService.vipRecharge(payOrder.getUid(), Integer.parseInt(payOrder.getAttach()));
            }

            if(BusinessType.CCY.getValue() == payOrder.getBusinessType()){
                appPetalService.petalRecharge(payOrder.getUid(), Integer.parseInt(payOrder.getAttach()));
            }

            if(BusinessType.ACTIVITY_JOIN.getValue() == payOrder.getBusinessType()){
                appOfflineActivityService.joinActivity(payOrder);
            }

            if(BusinessType.UNLOCK_QUALITY.getValue() == payOrder.getBusinessType()){
                appGuestQualityService.unLock(payOrder);
            }

        }catch (WxPayException e){
            log.error("支付结果通知异常", e);
            throw new LinfengException("支付结果通知异常");
        }
    }

    @Override
    public PrePayResultResponse vipPrePay(UserEntity user, VipRechargeRequest request, HttpServletRequest httpRequest) {

        PrePayResultResponse payResultResponse = new PrePayResultResponse();

        //校验充值方案是否存在
        VipOptionEntity vipOption = vipOptionService.getById(request.getOptionId());
        if(ObjectUtil.isNull(vipOption)){
            throw new LinfengException("充值方案不存在");
        }

        //创建内部订单
        PayOrderEntity payOrder = new PayOrderEntity();
        payOrder.setUid(user.getUid());
        if(request.getTerminalType().equals(Constant.TERMINAL_TYPE_WX_MA)){
            payOrder.setOpenid(user.getOpenid());
        }
        if(request.getTerminalType().equals(Constant.TERMINAL_TYPE_WX_MP)){
            payOrder.setOpenid(user.getMpOpenid());
        }
        payOrder.setAmount(vipOption.getPrice());
        payOrder.setBusinessType(BusinessType.VIP.getValue());
        payOrder.setTerminalType(request.getTerminalType());
        payOrder.setIp(IPUtil.getIp(httpRequest));
        payOrder.setAttach(vipOption.getId()+"");
        payOrderService.save(payOrder);

        //统一支付下单
        try{
            payResultResponse.setWxPrePayResult(buildPayResult(payOrder));
            payResultResponse.setOrderNo(payOrder.getOrderNo());
        }catch(WxPayException e){
            log.error("充值失败", e);
            throw new LinfengException("充值失败");
        }

        return payResultResponse;
    }

    @Override
    public PrePayResultResponse petalPrePay(UserEntity user, PetalRechargeRequest request, HttpServletRequest httpRequest) {

        PrePayResultResponse payResultResponse = new PrePayResultResponse();

        //校验充值方案是否存在
        PetalOptionEntity petalOption = petalOptionService.getById(request.getOptionId());
        if(ObjectUtil.isNull(petalOption)){
            throw new LinfengException("充值方案不存在");
        }

        //创建内部订单
        PayOrderEntity payOrder = new PayOrderEntity();
        payOrder.setUid(user.getUid());
        if(request.getTerminalType().equals(Constant.TERMINAL_TYPE_WX_MA)){
            payOrder.setOpenid(user.getOpenid());
        }
        if(request.getTerminalType().equals(Constant.TERMINAL_TYPE_WX_MP)){
            payOrder.setOpenid(user.getMpOpenid());
        }
        payOrder.setAmount(petalOption.getPrice());
        payOrder.setBusinessType(BusinessType.CCY.getValue());
        payOrder.setTerminalType(request.getTerminalType());
        payOrder.setIp(IPUtil.getIp(httpRequest));
        payOrder.setAttach(petalOption.getId()+"");
        payOrderService.save(payOrder);

        //统一支付下单
        try{
            payResultResponse.setWxPrePayResult(buildPayResult(payOrder));
            payResultResponse.setOrderNo(payOrder.getOrderNo());
        }catch(WxPayException e){
            log.error("充值失败", e);
            throw new LinfengException("充值失败");
        }

        return payResultResponse;
    }

    @Override
    public PrePayResultResponse activityJoinPrePay(UserEntity user, ActivityJoinRequest request, HttpServletRequest httpRequest) {
        PrePayResultResponse payResultResponse = new PrePayResultResponse();

        //校验活动是否存在
        OfflineActivityEntity offlineActivity = offlineActivityService.getById(request.getActivityId());
        if(ObjectUtil.isNull(offlineActivity)){
            throw new LinfengException("活动不存在");
        }

        if(offlineActivity.getManJoinNumber() + offlineActivity.getWomanJoinNumber() >= offlineActivity.getTotalNumber()){
            throw new LinfengException("活动人数已达上线");
        }

        if(user.getGender() == Constant.MAN && offlineActivity.getManJoinNumber()>=offlineActivity.getManNumber()){
            throw new LinfengException("活动人数已达上线");
        }

        if(user.getGender() == Constant.WOMAN && offlineActivity.getWomanJoinNumber()>=offlineActivity.getWomanNumber()){
            throw new LinfengException("活动人数已达上线");
        }

        //校验是否已经加入活动
        LambdaQueryWrapper<OfflineActivityJoinEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OfflineActivityJoinEntity::getActivityId, request.getActivityId());
        wrapper.eq(OfflineActivityJoinEntity::getUid, user.getUid());
        wrapper.ne(OfflineActivityJoinEntity::getStatus, ActivityJoinStatus.EXITED.getValue());
        OfflineActivityJoinEntity offlineActivityJoin = offlineActivityJoinService.getOne(wrapper);
        if(ObjectUtil.isNotNull(offlineActivityJoin)){
            throw new LinfengException("您已加入活动，请勿重复加入");
        }

        //创建内部订单
        LambdaQueryWrapper<PayOrderEntity> payWrapper = new LambdaQueryWrapper<>();
        payWrapper.eq(PayOrderEntity::getUid, user.getUid());
        payWrapper.eq(PayOrderEntity::getAttach, offlineActivity.getId()+"");
        payWrapper.eq(PayOrderEntity::getStatus, PayStatus.PAYING.getValue());
        PayOrderEntity payOrder = payOrderService.getOne(payWrapper);
        if(payOrder != null){
            throw new LinfengException("你有一笔相同的订单支付处理中，请勿重复下单");
        }
        payOrder = new PayOrderEntity();
        payOrder.setUid(user.getUid());
        if(request.getTerminalType().equals(Constant.TERMINAL_TYPE_WX_MA)){
            payOrder.setOpenid(user.getOpenid());
        }
        if(request.getTerminalType().equals(Constant.TERMINAL_TYPE_WX_MP)){
            payOrder.setOpenid(user.getMpOpenid());
        }
        //根据性别、当前时间判断价格
        if(DateUtil.secondsBetween(DateUtil.nowDateTime(), offlineActivity.getEarlyEndTime()) > 0){
            if(user.getGender() == Constant.MAN){
                payOrder.setAmount(offlineActivity.getManEarlyAmount());
            }else{
                payOrder.setAmount(offlineActivity.getWomanEarlyAmount());
            }
        }else{
            if(user.getGender() == Constant.MAN){
                payOrder.setAmount(offlineActivity.getManAmount());
            }else{
                payOrder.setAmount(offlineActivity.getWomanAmount());
            }
        }
        payOrder.setBusinessType(BusinessType.ACTIVITY_JOIN.getValue());
        payOrder.setTerminalType(request.getTerminalType());
        payOrder.setIp(IPUtil.getIp(httpRequest));
        payOrder.setAttach(offlineActivity.getId()+"");
        payOrderService.save(payOrder);

        //统一支付下单
        try{
            payResultResponse.setWxPrePayResult(buildPayResult(payOrder));
            payResultResponse.setOrderNo(payOrder.getOrderNo());
        }catch(WxPayException e){
            log.error("活动加入失败", e);
            throw new LinfengException("活动加入失败");
        }

        return payResultResponse;
    }

    @Override
    public PayResultResponse queryPayResult(UserEntity user, String orderNo) {
        PayResultResponse response = new PayResultResponse();
        LambdaQueryWrapper<PayOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PayOrderEntity::getOrderNo, orderNo);
        wrapper.eq(PayOrderEntity::getUid, user.getUid());
        PayOrderEntity payOrder = payOrderService.getOne(wrapper);
        if(payOrder == null){
            response.setStatus(PayStatus.FAIL.getValue());
            response.setMessage("支付订单不存在");
        }else{
            response.setStatus(payOrder.getStatus());
            response.setMessage(payOrder.getResultMessage());
        }
        return response;
    }

    @Override
    @Transactional
    public void refundCallBack(String xmlData) {
        log.info("退款回调报文[{}]", xmlData);
        try{
            WxPayRefundNotifyResult notifyResult = wxPayService.parseRefundNotifyResult(xmlData);
            WxPayRefundNotifyResult.ReqInfo reqInfo = notifyResult.getReqInfo();

            if(reqInfo == null || StringUtil.isEmpty(reqInfo.getOutRefundNo())){
                throw new LinfengException("微信退款回调异常-退款订单号为空");
            }
            LambdaQueryWrapper<PayRefundEntity> wrapper =new LambdaQueryWrapper<>();
            wrapper.eq(PayRefundEntity::getRefundNo, reqInfo.getOutRefundNo());
            PayRefundEntity payRefund = payRefundService.getOne(wrapper);

            if (payRefund == null){
                throw new LinfengException("微信退款回调异常-订单不存在");
            }
            if (payRefund.getStatus() == RefundStatus.SUCCESS.getValue()){
                throw new LinfengException("微信退款回调异常-退款状态已完成");
            }
            payRefund.setResultTime(DateUtil.nowDateTime());
            payRefund.setOutRefundNo(reqInfo.getRefundId());
            payRefund.setUpdateTime(DateUtil.nowDateTime());
            if(reqInfo.getRefundStatus().equals(Constant.CALLBACK_SUCCESS)){
                payRefund.setStatus(RefundStatus.SUCCESS.getValue());
                LambdaQueryWrapper<PayOrderEntity> payWrapper = new LambdaQueryWrapper<>();
                payWrapper.eq(PayOrderEntity::getOrderNo, payRefund.getOrderNo());
                PayOrderEntity payOrder =payOrderService.getOne(payWrapper);
                payOrder.setRefundStatus(RefundStatus.SUCCESS.getValue());
                payOrder.setUpdateTime(DateUtil.nowDateTime());
                payOrderService.updateById(payOrder);
            }else{
                payRefund.setStatus(RefundStatus.FAIL.getValue());
            }

            payRefundService.updateById(payRefund);

            //目前只有活动存在退款场景
            appOfflineActivityService.activityExit(payRefund.getRefundNo());
        }catch (WxPayException e){
            log.error("退款结果通知异常", e);
            throw new LinfengException("退款结果通知异常");
        }

    }

    @Override
    public void cancelPay(UserEntity user, String orderNo) {
        LambdaQueryWrapper<PayOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PayOrderEntity::getOrderNo, orderNo);
        wrapper.eq(PayOrderEntity::getUid, user.getUid());
        PayOrderEntity payOrder = payOrderService.getOne(wrapper);
        payOrder.setStatus(PayStatus.FAIL.getValue());
        payOrderService.updateById(payOrder);
    }

    @Override
    public PrePayResultResponse qualityUnlockPrePay(UserEntity user, PayRequest request, HttpServletRequest httpRequest) {
        List<GuestSimpleResponseDTO> guestSimpleResponseList = redisUtils.getList(CACHE_USER_QUALITY_PREFIX + user.getUid(), GuestSimpleResponseDTO.class);
        if(guestSimpleResponseList == null || guestSimpleResponseList.size() == 0){
            throw new LinfengException("暂无嘉宾需要解锁");
        }
        //创建内部订单
        PrePayResultResponse payResultResponse = new PrePayResultResponse();
        LambdaQueryWrapper<PayOrderEntity> payWrapper = new LambdaQueryWrapper<>();
        payWrapper.eq(PayOrderEntity::getUid, user.getUid());
        payWrapper.eq(PayOrderEntity::getBusinessType, BusinessType.UNLOCK_QUALITY.getValue());
        payWrapper.eq(PayOrderEntity::getAttach, user.getUid()+"");
        payWrapper.eq(PayOrderEntity::getStatus, PayStatus.PAYING.getValue());
        PayOrderEntity payOrder = payOrderService.getOne(payWrapper);
        if(payOrder != null){
            throw new LinfengException("你有一笔相同的订单支付处理中，请勿重复下单");
        }
        payOrder = new PayOrderEntity();
        payOrder.setUid(user.getUid());
        if(request.getTerminalType().equals(Constant.TERMINAL_TYPE_WX_MA)){
            payOrder.setOpenid(user.getOpenid());
        }
        if(request.getTerminalType().equals(Constant.TERMINAL_TYPE_WX_MP)){
            payOrder.setOpenid(user.getMpOpenid());
        }
        BigDecimal amount = new BigDecimal(configBusinessService.getValue(Constant.QUALITY_BATCH_PERCENT));
        payOrder.setAmount(amount);
        payOrder.setBusinessType(BusinessType.UNLOCK_QUALITY.getValue());
        payOrder.setTerminalType(request.getTerminalType());
        payOrder.setIp(IPUtil.getIp(httpRequest));
        payOrder.setAttach(user.getUid()+"");
        payOrderService.save(payOrder);

        //统一支付下单
        try{
            payResultResponse.setWxPrePayResult(buildPayResult(payOrder));
            payResultResponse.setOrderNo(payOrder.getOrderNo());
        }catch(WxPayException e){
            log.error("嘉宾解锁失败", e);
            throw new LinfengException("嘉宾解锁失败");
        }

        return payResultResponse;
    }

    private <T> T buildPayResult(PayOrderEntity payOrderEntity) throws WxPayException {
        WxPayUnifiedOrderRequest wxPayRequest = new WxPayUnifiedOrderRequest();

        wxPayRequest.setSpbillCreateIp(payOrderEntity.getIp());

        //单位为分
        int totalFee = payOrderEntity.getAmount().multiply(new BigDecimal(100)).intValue();
        wxPayRequest.setTotalFee(totalFee);

        wxPayRequest.setOpenid(payOrderEntity.getOpenid());
        wxPayRequest.setOutTradeNo(payOrderEntity.getOrderNo());
        wxPayRequest.setAttach(payOrderEntity.getAttach());

        switch (payOrderEntity.getTerminalType()) {
            case Constant.TERMINAL_TYPE_APP : {
                wxPayRequest.setTradeType(WxPayConstants.TradeType.APP);
                wxPayRequest.setAppid(configSystemService.getValue(Constant.WX_SUB_APP_ID));
                break;
            }
            case Constant.TERMINAL_TYPE_H5 : {
                wxPayRequest.setTradeType(WxPayConstants.TradeType.MWEB);
                break;
            }
            case Constant.TERMINAL_TYPE_WX_MA: {
                wxPayRequest.setTradeType(WxPayConstants.TradeType.JSAPI);
                wxPayRequest.setAppid(configSystemService.getValue(Constant.WX_MA_APP_ID));
                break;
            }
            case Constant.TERMINAL_TYPE_WX_MP: {
                wxPayRequest.setTradeType(WxPayConstants.TradeType.JSAPI);
                wxPayRequest.setAppid(configSystemService.getValue(Constant.WX_MP_APP_ID));
                break;
            }
        }
        switch (payOrderEntity.getBusinessType()) {
            case 1 : {
                wxPayRequest.setBody(BusinessType.VIP.getText());
                break;
            }
            case 2 : {
                wxPayRequest.setBody(BusinessType.CCY.getText());
                break;
            }
            case 3 : {
                wxPayRequest.setBody(BusinessType.ACTIVITY_JOIN.getText());
                break;
            }
        }

        return wxPayService.createOrder(wxPayRequest);
    }

}