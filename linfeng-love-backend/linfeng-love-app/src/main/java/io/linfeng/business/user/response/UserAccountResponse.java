package io.linfeng.business.user.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
@ApiModel(description="用户账户响应实体")
public class UserAccountResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 收益总金额
	 */
	@ApiModelProperty(value = "收益总金额")
	private BigDecimal totalAmount;
	/**
	 * 已提现金额
	 */
	@ApiModelProperty(value = "已提现金额")
	private BigDecimal cashedAmount;
	/**
	 * 已兑换花瓣金额
	 */
	@ApiModelProperty(value = "已兑换花瓣金额")
	private BigDecimal petalAmount;
	/**
	 * 未提现金额
	 */
	@ApiModelProperty(value = "未提现金额")
	private BigDecimal unCashedAmount;

	/**
	 * 冻结金额
	 */
	@ApiModelProperty(value = "冻结金额")
	private BigDecimal freezeAmount;

	/**
	 * 今日收益
	 */
	@ApiModelProperty(value = "今日收益")
	private BigDecimal toDayAmount;
	/**
	 * 当日提现次数
	 */
	@ApiModelProperty(value = "当日提现次数")
	private Integer dayCashedCount;
	/**
	 * 本周提现次数
	 */
	@ApiModelProperty(value = "本周提现次数")
	private Integer weekCashedCount;
	/**
	 * 最近提现时间
	 */
	@ApiModelProperty(value = "最近提现时间")
	private Date lastCashedTime;

}
