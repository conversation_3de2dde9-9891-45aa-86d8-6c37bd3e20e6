package io.linfeng.business.auth.service;

import io.linfeng.business.auth.request.SmsLoginRequest;
import io.linfeng.business.auth.request.WxLoginRequest;
import io.linfeng.business.auth.response.LoginResponse;
import io.linfeng.business.auth.request.BindWxPhoneRequest;
import io.linfeng.love.user.entity.UserEntity;

/**
 * App登录授权服务
 *
 * <AUTHOR>
 * @date 2023-09-06 16:22:14
 */
public interface AppAuthService {

    /**
     * 微信小程序静默登录
     * @param request 请求信息
     * @return 登录结果
     */
    LoginResponse miniWxLogin(WxLoginRequest request);

    /**
     * 微信公众号openid绑定
     * @param request 登录请求
     * @return 绑定结果
     */
    void bindMpOpenid(UserEntity user, WxLoginRequest request);

    /**
     * 短信验证码登录
     * @param request 请求信息
     * @return 登录结果
     */
    LoginResponse smsLogin(SmsLoginRequest request);

    /**
     * 微信静默登录
     * @param request 请求信息
     * @return token相关信息
     */
    LoginResponse bindWxPhone(UserEntity user, BindWxPhoneRequest request);


}

