/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.chat.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.chat.request.ChatMessageRequest;
import io.linfeng.business.chat.response.ChatMessageResponse;
import io.linfeng.business.chat.service.AppChatMessageService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

/**
 *  聊天消息Api
 */
@RestController
@RequestMapping("/app/chat/message")
@Api(tags = "聊天消息Api")
public class AppChatMessageController {

    private final AppChatMessageService appChaMessageService;

    public AppChatMessageController(AppChatMessageService appChaMessageService) {
        this.appChaMessageService = appChaMessageService;
    }

    /**
     * 聊天记录查询
     * @param user 登录用户
     * @param request 查询
     * @return 聊天记录列表
     */
    @Login
    @GetMapping("/list")
    @ApiOperation("查询聊天记录")
    public Result<IPage<ChatMessageResponse>> list(@ApiIgnore @LoginUser UserEntity user, ChatMessageRequest request){
        IPage<ChatMessageResponse> chatMessageList = appChaMessageService.getMessageList(user, request);
        return new Result<IPage<ChatMessageResponse>>().ok(chatMessageList);
    }

    /**
     * 聊天消息发送
     * @param user 登录用户
     * @param request 消息请求
     * @return 发送结果
     */
    @Login
    @PostMapping("/send")
    @ApiOperation("发消息")
    public Result<ChatMessageResponse> send(@ApiIgnore @LoginUser UserEntity user, @RequestBody ChatMessageRequest request){
        ChatMessageResponse chatMessageResponse = appChaMessageService.sendMessage(user, request);
        return new Result<ChatMessageResponse>().ok(chatMessageResponse);
    }

    /**
     * 消息撤回
     * @param user 登录用户
     * @param request 撤回请求
     * @return 撤回结果
     */
    @Login
    @PostMapping("/recall")
    @ApiOperation("消息撤回")
    public R recall(@ApiIgnore @LoginUser UserEntity user, @RequestBody ChatMessageRequest request){
        appChaMessageService.recallMessage(user, request);
        return R.ok();
    }

    /**
     * 消息已读
     * @param user 登录用户
     * @param request 请求
     * @return 处理结果
     */
    @Login
    @PostMapping("/read")
    @ApiOperation("消息已读")
    public R read(@ApiIgnore @LoginUser UserEntity user, @RequestBody ChatMessageRequest request){
        appChaMessageService.readMessage(user, request);
        return R.ok();
    }

}
