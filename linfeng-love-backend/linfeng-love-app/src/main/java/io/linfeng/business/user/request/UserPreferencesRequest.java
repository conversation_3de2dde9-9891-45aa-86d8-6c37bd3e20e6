package io.linfeng.business.user.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 偏好设置请求对象
 */
@Data
@ApiModel(description="偏好设置请求对象")
public class UserPreferencesRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 最小年龄
	 */
	@ApiModelProperty(value = "最小年龄")
	private Integer minAge;
	/**
	 * 最大年龄
	 */
	@ApiModelProperty(value = "最大年龄")
	private Integer maxAge;
	/**
	 * 最小身高
	 */
	@ApiModelProperty(value = "最小身高")
	private Integer minStature;
	/**
	 * 最大身高
	 */
	@ApiModelProperty(value = "最大身高")
	private Integer maxStature;
	/**
	 * 最小体重
	 */
	@ApiModelProperty(value = "最小体重")
	private Integer minWeight;
	/**
	 * 最大体重
	 */
	@ApiModelProperty(value = "最大体重")
	private Integer maxWeight;
	/**
	 * 最低学历
	 */
	@ApiModelProperty(value = "最低学历")
	private Integer minEducation;
	/**
	 * 0同城优先，1只要同城
	 */
	@ApiModelProperty(value = "居住地设置")
	private Integer living;
	/**
	 * 0不限，1只要同省
	 */
	@ApiModelProperty(value = "家乡设置")
	private Integer home;
	/**
	 * 0不限，1未婚
	 */
	@ApiModelProperty(value = "婚姻状态设置")
	private Integer marriage;
	/**
	 * 0不限，1实名
	 */
	@ApiModelProperty(value = "实名状态设置")
	private Integer realName;


}
