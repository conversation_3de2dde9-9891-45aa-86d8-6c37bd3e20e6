package io.linfeng.business.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.user.request.ExchangeCashRequest;
import io.linfeng.business.user.request.UserCashedQueryRequest;
import io.linfeng.business.user.response.UserCashedResponse;
import io.linfeng.love.user.entity.UserEntity;

/**
 * 用户提现业务服务
 *
 * <AUTHOR>
 * @date 2023-09-06 16:22:14
 */
public interface AppUserCashedService {


    /**
     * 获取用户提现明细列表（分页）
     * @param user 登录用户
     * @param request 请求对象
     * @return 用户提现明细列表
     */
    IPage<UserCashedResponse> getUserCashedPage(UserEntity user, UserCashedQueryRequest request);

    /**
     * 用户提现提交
     * @param user 登录用户
     * @param request 提交请求
     */
    void exchangeCashSubmit(UserEntity user, ExchangeCashRequest request);
}

