package io.linfeng.business.robot;

import cn.hutool.core.util.IdUtil;
import io.linfeng.common.enums.MediaType;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.love.common.enums.CommonStatus;
import io.linfeng.love.config.service.ConfigSystemService;
import io.linfeng.love.file.service.FileService;
import io.linfeng.love.moment.entity.MomentEntity;
import io.linfeng.love.moment.entity.MomentMediaEntity;
import io.linfeng.love.moment.service.MomentMediaService;
import io.linfeng.love.moment.service.MomentService;
import io.linfeng.love.user.entity.UserAccountEntity;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.enums.RealNameStatus;
import io.linfeng.love.user.service.UserAccountService;
import io.linfeng.love.user.service.UserService;
import io.linfeng.transport.random.RandomTransport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;


/**
 * 花瓣清理定时任务
 */
@Component
@Slf4j
public class RobotScheduled {

    private final UserService userService;

    private final RandomTransport randomTransport;

    private final UserAccountService userAccountService;

    private final MomentService momentService;

    private final MomentMediaService momentMediaService;

    private final FileService fileService;

    private final ConfigSystemService configSystemService;

    private static final int DELAY_IN_MILLISECONDS = 1000 * 60 * 60 * 24;

    private static int[] status = {1, 2, 3, 4, 5, 6, 7, 8, 9};

    private static List<Integer> sratureList = new ArrayList<>();

    private static List<Integer> weightList = new ArrayList<>();

    private static String[] letter = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "G", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};

    private static String[] number = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9"};

    private static String[] schoolList = {"清华大学", "北京大学", "浙江大学", "上海交通大学", "复旦大学", "南京大学", "中国科学技术大学", "华中科技大学", "武汉大学", "西安交通大学", "中山大学", "四川大学", "哈尔滨工业大学"
            , "北京航空航天大学", "东南大学", "北京理工大学", "同济大学", "中国人民大学", "北京师范大学", "南开大学", "北京协和医学院", "天津大学", "山东大学", "中南大学", "厦门大学", "西北工业大学", "华南理工大学", "吉林大学", "电子科技大学", "湖南大学", "中国农业大学"
            , "华东师范大学", "大连理工大学"};

    private static String[] provinceList = {"北京", "天津", "上海", "重庆", "香港", "澳门", "河北", "山西", "内蒙古", "辽宁", "吉林", "黑龙江", "江苏"
            , "浙江", "福建", "江西", "山东", "河南", "湖北", "湖南", "广东", "广西", "海南", "四川", "贵州", "云南", "陕西", "甘肃", "青海", "西藏", "宁夏"
            , "新疆", "台湾"};


    private static Map<String, String[]> cityMap = new HashMap<>();

    static{
        for(int i=140; i<220; i++){
            sratureList.add(i);
        }
        for(int i=40; i<120; i++){
            weightList.add(i);
        }
        cityMap.put("北京", new String[]{"北京"});
        cityMap.put("天津", new String[]{"天津"});
        cityMap.put("上海", new String[]{"上海"});
        cityMap.put("重庆", new String[]{"重庆"});
        cityMap.put("香港", new String[]{"香港"});
        cityMap.put("澳门", new String[]{"澳门"});
        cityMap.put("河北", new String[]{"石家庄","唐山","秦皇岛","邯郸","邢台","保定","张家口","承德","沧州","廊坊","衡水","辛集","晋州","新乐","遵化","迁安","武安","南宫","沙河","涿州","定州","安国","高碑店","平泉","泊头","任丘","黄骅","河间","霸州","三河","深州"});
        cityMap.put("山西", new String[]{"太原","大同","阳泉","长治","晋城","朔州","晋中","运城","忻州","临汾","吕梁","古交","高平","介休","永济","河津","原平","侯马","霍州","孝义","汾阳","怀仁"});
        cityMap.put("内蒙古", new String[]{"呼和浩特","包头","乌海","赤峰","通辽","鄂尔多斯","呼伦贝尔","巴彦淖尔","乌兰察布","霍林郭勒","满洲里","牙克石","扎兰屯","额尔古纳","根河","丰镇","乌兰浩特","阿尔山","二连浩特","锡林浩特"});
        cityMap.put("辽宁", new String[]{"沈阳","大连","鞍山","抚顺","本溪","丹东","锦州","营口","阜新","辽阳","盘锦","铁岭","朝阳","葫芦岛","新民","瓦房店","庄河","海城","东港","凤城","凌海","北镇","盖州","大石桥","灯塔","调兵山","开原","北票","凌源","兴城"});
        cityMap.put("吉林", new String[]{"长春","吉林","四平","辽源","通化","白山","松原","白城","榆树","德惠","蛟河","桦甸","舒兰","磐石","公主岭","双辽","梅河口","集安","洮南","大安","临江","延吉","图们","敦化","珲春","龙井","和龙","扶余"});
        cityMap.put("黑龙江", new String[]{"哈尔滨","齐齐哈尔","黑河","大庆","伊春","鹤岗","佳木斯","双鸭山","七台河","鸡西","牡丹江","绥化","尚志","五常","讷河","北安","五大连池","嫩江","铁力","同江","富锦","虎林","密山","绥芬河","海林","宁安","安达","肇东","海伦","穆棱","东宁","抚远","漠河"});
        cityMap.put("江苏", new String[]{"南京","徐州","连云港","宿迁","淮安","盐城","扬州","泰州","南通","镇江","常州","无锡","苏州","常熟","张家港","太仓","昆山","江阴","宜兴","溧阳","扬中","句容","丹阳","如皋","启东","海安","高邮","仪征","兴化","泰兴","靖江","东台","邳州","新沂"});
        cityMap.put("浙江", new String[]{"杭州","宁波","湖州","嘉兴","舟山","绍兴","衢州","金华","台州","温州","丽水","建德","慈溪","余姚","平湖","海宁","桐乡","诸暨","嵊州","江山","兰溪","永康","义乌","东阳","临海","温岭","瑞安","乐清","龙港","龙泉","玉环"});
        cityMap.put("安徽", new String[]{"合肥","芜湖","蚌埠","淮南","马鞍山","淮北","铜陵","安庆","黄山","滁州","阜阳","宿州","六安","亳州","池州","宣城","巢湖","桐城","天长","明光","界首","宁国","广德","潜山","无为"});
        cityMap.put("福建", new String[]{"厦门","福州","南平","三明","莆田","泉州","漳州","龙岩","宁德","福清","邵武","武夷山","建瓯","永安","石狮","晋江","南安","龙海","漳平","福安","福鼎"});
        cityMap.put("江西", new String[]{"南昌","九江","景德镇","鹰潭","新余","萍乡","赣州","上饶","抚州","宜春","吉安","瑞昌","共青城","庐山","乐平","瑞金","德兴","丰城","樟树","高安","井冈山","贵溪"});
        cityMap.put("山东", new String[]{"济南","青岛","聊城","德州","东营","淄博","潍坊","烟台","威海","日照","临沂","枣庄","济宁","泰安","滨州","菏泽","胶州","平度","莱西","临清","乐陵","禹城","安丘","昌邑","高密","青州","诸城","寿光","栖霞","海阳","龙口","莱阳","莱州","蓬莱","招远","荣成","乳山","滕州","曲阜","邹城","新泰","肥城","邹平"});
        cityMap.put("河南", new String[]{"郑州","开封","洛阳","平顶山","安阳","鹤壁","新乡","焦作","濮阳","许昌","漯河","三门峡","南阳","商丘","周口","驻马店","信阳","荥阳","新郑","登封","新密","偃师","孟州","沁阳","卫辉","辉县","长垣","林州","禹州","长葛","舞钢","义马","灵宝","项城","巩义","邓州","永城","汝州","济源"});
        cityMap.put("湖北", new String[]{"武汉","十堰","襄阳","荆门","孝感","黄冈","鄂州","黄石","咸宁","荆州","宜昌","随州","丹江口","老河口","枣阳","宜城","钟祥","京山","汉川","应城","安陆","广水","麻城","武穴","大冶","赤壁","石首","洪湖","松滋","宜都","枝江","当阳","恩施","利川","仙桃","天门","潜江"});
        cityMap.put("湖南", new String[]{"长沙","衡阳","张家界","常德","益阳","岳阳","株洲","湘潭","郴州","永州","邵阳","怀化","娄底","耒阳","常宁","浏阳","津","沅江","汨罗","临湘","醴陵","湘乡","韶山","资兴","武冈","邵东","洪江","冷水江","涟源","吉首","宁乡"});
        cityMap.put("广东", new String[]{"广州","深圳","清远","韶关","河源","梅州","潮州","汕头","揭阳","汕尾","惠州","东莞","珠海","中山","江门","佛山","肇庆","云浮","阳江","茂名","湛江","英德","连州","乐昌","南雄","兴宁","普宁","陆丰","恩平","台山","开平","鹤山","四会","罗定","阳春","化州","信宜","高州","吴川","廉江","雷州"});
        cityMap.put("广西", new String[]{"南宁","桂林","柳州","梧州","贵港","玉林","钦州","北海","防城港","崇左","百色","河池","来宾","贺州","岑溪","桂平","北流","东兴","凭祥","合山","靖西","平果","荔浦"});
        cityMap.put("海南", new String[]{"海口","三亚","三沙","儋州","文昌","琼海","万宁","东方","五指山"});
        cityMap.put("四川", new String[]{"成都","广元","绵阳","德阳","南充","广安","遂宁","内江","乐山","自贡","泸州","宜宾","攀枝花","巴中","达州","资阳","眉山","雅安","崇州","邛崃","都江堰","彭州","江油","什邡","广汉","绵竹","阆中","华蓥","峨眉山","万源","简阳","西昌","康定","马尔康","隆昌","射洪","会理"});
        cityMap.put("贵州", new String[]{"贵阳","六盘水","遵义","安顺","毕节","铜仁","清镇","赤水","仁怀","凯里","都匀","兴义","福泉","盘州","兴仁"});
        cityMap.put("云南", new String[]{"昆明","曲靖","玉溪","丽江","昭通","普洱","临沧","保山","安宁","宣威","芒","瑞丽","大理","楚雄","个旧","开远","蒙自","弥勒","景洪","文山","香格里拉","腾冲","水富","澄江","泸水"});
        cityMap.put("陕西", new String[]{"西安","延安","铜川","渭南","咸阳","宝鸡","汉中","榆林","商洛","安康","韩城","华阴","兴平","彬州","神木","子长"});
        cityMap.put("甘肃", new String[]{"兰州","嘉峪关","金昌","白银","天水","酒泉","张掖","武威","庆阳","平凉","定西","陇南","玉门","敦煌","临夏","合作","华亭"});
        cityMap.put("青海", new String[]{"西宁","海东","格尔木","德令哈","玉树","茫崖"});
        cityMap.put("西藏", new String[]{"拉萨","日喀则","昌都","林芝","山南","那曲"});
        cityMap.put("宁夏", new String[]{"银川","石嘴山","吴忠","中卫","固原","灵武","青铜峡"});
        cityMap.put("新疆", new String[]{"乌鲁木齐","克拉玛依","吐鲁番","哈密","喀什","阿克苏","库车","和田","阿图什","阿拉山口","博乐","昌吉","阜康","库尔勒","伊宁","奎屯","塔城","乌苏","阿勒泰","霍尔果斯","石河子","阿拉尔","图木舒克","五家渠","北屯","铁门关","双河","可克达拉","昆玉","胡杨河"});
        cityMap.put("台湾", new String[]{"台北","新北","桃园","台中","台南","高雄","基隆","新竹","嘉义"});
    }

    public RobotScheduled(UserService userService, RandomTransport randomTransport, UserAccountService userAccountService, MomentService momentService, MomentMediaService momentMediaService, FileService fileService, ConfigSystemService configSystemService) {
        this.userService = userService;
        this.randomTransport = randomTransport;
        this.userAccountService = userAccountService;
        this.momentService = momentService;
        this.momentMediaService = momentMediaService;
        this.fileService = fileService;
        this.configSystemService = configSystemService;
    }

    @Scheduled(fixedRate = DELAY_IN_MILLISECONDS)
    public void randomRegisterAndMoment() throws ParseException {

        boolean robotRandomOpen = configSystemService.getValue(Constant.ROBOT_RANDOM_OPEN).equals("1");
        if(!robotRandomOpen){
            log.info("机器人注册发动态未开启");
            return;
        }
        log.info("机器人注册发动态开始");

        //机器人注册
        UserEntity userEntity = new UserEntity();
        StringBuffer mobile = new StringBuffer("15");
        Random random = new Random();
        for (int j = 0; j < 9; j++) {
            mobile.append(random.nextInt(10));
        }
        userEntity.setUserName("LF_"+letter[random.nextInt(letter.length)]+number[random.nextInt(number.length)]
                +letter[random.nextInt(letter.length)]+number[random.nextInt(number.length)]
                +letter[random.nextInt(letter.length)]+number[random.nextInt(number.length)]
                +letter[random.nextInt(letter.length)]+number[random.nextInt(number.length)]);

        userEntity.setGender(2);

        //开放接口目前只找到女生的随机图片
        String avatarUrl = fileService.upload(randomTransport.randomWoman());

        userEntity.setAvatar(avatarUrl);

        userEntity.setMobile(mobile.toString());
        userEntity.setOid(IdUtil.fastSimpleUUID());
        userEntity.setRealNameStatus(RealNameStatus.WEAK.getValue());
        userEntity.setStatus(CommonStatus.DEFAULT.getValue());
        LocalDate startDate = LocalDate.of(1970, 1, 1);
        LocalDate endDate = LocalDate.of(2005, 12, 31);
        long days = ChronoUnit.DAYS.between(startDate, endDate);
        LocalDate randomDate = startDate.plusDays(ThreadLocalRandom.current().nextLong(days + 1));
        String birthday = randomDate.toString();
        userEntity.setBirthday(birthday);
        userEntity.setConstellation(DateUtil.getConstellation(birthday));
        userEntity.setAge(DateUtil.getAge(birthday));
        userEntity.setStature(sratureList.get(random.nextInt(sratureList.size())));
        userEntity.setWeight(weightList.get(random.nextInt(weightList.size())));
        String livingProvince = provinceList[random.nextInt(provinceList.length)];
        userEntity.setLivingProvince(livingProvince);
        String[] livingCityList = cityMap.get(livingProvince);
        userEntity.setLivingCity(livingCityList[random.nextInt(livingCityList.length)]);
        String homeProvince = provinceList[random.nextInt(provinceList.length)];
        String[] homeCityList = cityMap.get(homeProvince);
        userEntity.setHomeProvince(homeProvince);
        userEntity.setHomeCity(homeCityList[random.nextInt(homeCityList.length)]);
        userEntity.setMarriage(status[random.nextInt(2)]);
        userEntity.setSchool(schoolList[random.nextInt(schoolList.length)]);
        userEntity.setEducation(status[random.nextInt(4)]);
        userEntity.setJob(status[random.nextInt(9)]);
        userEntity.setSalary(status[random.nextInt(5)]);
        userEntity.setOpenid(UUID.randomUUID().toString());
        userEntity.setCreateTime(DateUtil.nowDateTime());
        userService.save(userEntity);

        //创建账户
        UserAccountEntity userAccount = new UserAccountEntity();
        userAccount.setUid(userEntity.getUid());
        userAccount.setTotalAmount(new BigDecimal(0));
        userAccount.setCashedAmount(new BigDecimal(0));
        userAccount.setPetalAmount(new BigDecimal(0));
        userAccount.setFreezeAmount(new BigDecimal(0));
        userAccount.setUnCashedAmount(new BigDecimal(0));
        userAccount.setDayCashedCount(0);
        userAccount.setWeekCashedCount(0);
        userAccount.setCreateTime(DateUtil.nowDateTime());
        userAccountService.save(userAccount);

        //发表动态
        MomentEntity momentEntity = new MomentEntity();
        String content = randomTransport.randomText();
        momentEntity.setUid(userEntity.getUid());
        momentEntity.setContent(content);
        momentEntity.setPv(0);
        momentEntity.setLv(0);
        momentEntity.setCv(0);
        momentEntity.setMediaType(MediaType.IMAGE.getValue());
        momentEntity.setStatus(CommonStatus.CHECKED.getValue());
        momentEntity.setAnonymous(0);
        momentEntity.setPrivacy(0);
        momentEntity.setCreateTime(DateUtil.nowDateTime());
        momentService.save(momentEntity);

        List<MomentMediaEntity> momentMediaEntityList = new ArrayList<>();
        for(int i=0; i< random.nextInt(5); i++){
            MomentMediaEntity momentMediaEntity = new MomentMediaEntity();
            momentMediaEntity.setMomentId(momentEntity.getId());
            momentMediaEntity.setUid(userEntity.getUid());
            momentMediaEntity.setMediaName(IdUtil.fastSimpleUUID());
            String momentUrl = fileService.upload(randomTransport.randomWoman());
            momentMediaEntity.setUrl(momentUrl);
            momentMediaEntity.setMediaType(momentEntity.getMediaType());
            momentMediaEntity.setCreateTime(DateUtil.nowDateTime());
            momentMediaEntityList.add(momentMediaEntity);
        }
        momentMediaService.saveBatch(momentMediaEntityList);
        log.info("机器人注册发动态结束");
    }

}
