/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.identity.controller;

import io.linfeng.business.identity.service.AppIdentityService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.business.identity.request.EducationCheckRequest;
import io.linfeng.business.identity.request.JobCheckRequest;
import io.linfeng.business.identity.request.RealNameRequest;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;


/**
 * 认证服务Api
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
@RestController
@RequestMapping("app/identify")
@Api(tags = "认证服务Api")
public class AppIdentityController {

    private final AppIdentityService appIdentityService;

    public AppIdentityController(AppIdentityService appIdentityService) {
        this.appIdentityService = appIdentityService;
    }

    /**
     * 实名认证
     * @param user 登录用户
     * @param request 实名认证请求
     * @return 认证结果
     */
    @Login
    @ApiOperation(value = "实名认证")
    @PostMapping("/realName")
    public R realNameCheck(@ApiIgnore @LoginUser UserEntity user, @RequestBody RealNameRequest request) {
        appIdentityService.realNameCheck(user, request);
        return R.ok();
    }

    /**
     * 学历认证
     * @param user 登录用户
     * @param request 学历认证请求
     * @return 认证结果
     */
    @Login
    @ApiOperation(value = "学历认证")
    @PostMapping("/education")
    public R educationCheck(@ApiIgnore @LoginUser UserEntity user, @RequestBody EducationCheckRequest request) {
        appIdentityService.educationCheck(user, request);
        return R.ok();
    }

    /**
     * 工作认证
     * @param user 登录用户
     * @param request 工作认证请求
     * @return 认证结果
     */
    @Login
    @ApiOperation(value = "工作认证")
    @PostMapping("/job")
    public R jobCheck(@ApiIgnore @LoginUser UserEntity user, @RequestBody JobCheckRequest request) {
        appIdentityService.jobCheck(user, request);
        return R.ok();
    }

}
