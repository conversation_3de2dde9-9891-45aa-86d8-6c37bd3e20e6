package io.linfeng.business.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.business.chat.request.ChatMessageRequest;
import io.linfeng.business.chat.response.ChatMessageResponse;
import io.linfeng.business.chat.service.AppChatMessageService;
import io.linfeng.business.chat.request.ChatGiftRequest;
import io.linfeng.business.chat.service.AppChatGiftService;
import io.linfeng.business.petal.service.AppPetalService;
import io.linfeng.business.user.service.AppUserAccountService;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.love.chat.enums.MessageType;
import io.linfeng.love.config.entity.ConfigGiftEntity;
import io.linfeng.love.config.enums.SupportLimit;
import io.linfeng.love.config.service.ConfigBusinessService;
import io.linfeng.love.config.service.ConfigGiftService;
import io.linfeng.love.petal.enums.PetalRecordSubType;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.entity.UserGiftEntity;
import io.linfeng.love.user.enums.AccountRecordSubType;
import io.linfeng.love.user.service.UserGiftService;
import io.linfeng.love.user.service.UserService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;


@Service("appGiftService")
@AllArgsConstructor
public class AppChatGiftServiceImpl implements AppChatGiftService {

    private final ConfigGiftService configGiftService;

    private final AppPetalService appPetalService;

    private final AppChatMessageService appChaMessageService;

    private final UserService userService;

    private final UserGiftService userGiftService;

    private final AppUserAccountService appUserAccountService;

    private final ConfigBusinessService configBusinessService;

    @Override
    @Transactional
    public ChatMessageResponse giveGift(UserEntity user, ChatGiftRequest request) {

        ConfigGiftEntity configGiftEntity = configGiftService.getById(request.getGiftId());

        if(configGiftEntity == null){
            throw new LinfengException("礼物参数错误");
        }

        if(configGiftEntity.getVip() == Constant.VIP_USER && user.getVip() == Constant.COMMON_USER){
            throw new LinfengException("仅vip用户可赠送该礼物");
        }

        Integer totalPetal = configGiftEntity.getPetal() * request.getNumber();
        Integer limitPetal = 0;
        if(configGiftEntity.getSupportLimit() == SupportLimit.YES.getValue()){
            limitPetal = appPetalService.deductPetal(user, totalPetal, PetalRecordSubType.GIFT_GIVE.getValue());
        }else{
            appPetalService.deductPetalForever(user, totalPetal, PetalRecordSubType.GIFT_GIVE.getValue());
        }

        UserEntity friendUser = userService.getUserByOid(request.getOid());

        //对方账户入账
        //临时花瓣不参与分成
        BigDecimal giftPercent = new BigDecimal(configBusinessService.getValue(Constant.GIFT_PERCENT));
        BigDecimal petalRate = new BigDecimal(configBusinessService.getValue(Constant.PETAL_RATE));
        BigDecimal realPetal = new BigDecimal(totalPetal - limitPetal);
        BigDecimal realAmount = realPetal.divide(petalRate).multiply(giftPercent).setScale(2, BigDecimal.ROUND_FLOOR);
        if(!realAmount.equals(BigDecimal.ZERO)){
            //备注信息 oid_userName_giftName_giftNumber 赠送者oid_赠送者姓名_礼物名称_礼物数量
            //目前只涉及到礼物赠送模块，方便后续扩展由remark字段来组装前端需要展示的信息
            String remark = friendUser.getOid() + "_" + friendUser.getUserName() + "_" + configGiftEntity.getName() + "_" + request.getNumber();
            appUserAccountService.entry(friendUser.getUid(), realAmount, AccountRecordSubType.GIFT, remark);
        }

        //点亮对方礼物墙
        UserGiftEntity userGiftEntity = new UserGiftEntity();
        userGiftEntity.setUid(user.getUid());
        userGiftEntity.setGuestUid(friendUser.getUid());
        userGiftEntity.setGiftId(request.getGiftId());
        userGiftEntity.setGiftNumber(request.getNumber());
        userGiftEntity.setGiftLimitPetal(limitPetal);
        userGiftEntity.setGiftForeverPetal(totalPetal - limitPetal);
        userGiftEntity.setGiftPercent(giftPercent);
        userGiftEntity.setGuestReward(realAmount);
        userGiftEntity.setCreateTime(DateUtil.nowDateTime());
        userGiftService.save(userGiftEntity);

        //发送聊天
        ChatMessageRequest chatMessageRequest = new ChatMessageRequest();
        chatMessageRequest.setFriendOid(request.getOid());
        chatMessageRequest.setMessageType(MessageType.GIFT.getValue());
        //聊天信息组成  礼物名称_礼物静态图片_礼物数量_是否首次点亮
        chatMessageRequest.setContent(configGiftEntity.getName() + "_" + configGiftEntity.getStaticUrl() + "_" + request.getNumber());
        ChatMessageResponse response = appChaMessageService.sendMessage(user, chatMessageRequest);
        return response;

    }


}