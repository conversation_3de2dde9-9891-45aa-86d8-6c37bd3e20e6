package io.linfeng.business.censor.impl;

import io.linfeng.business.censor.service.AppCensorService;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import io.linfeng.love.config.service.ConfigBusinessService;
import io.linfeng.love.config.service.ConfigSystemService;
import io.linfeng.transport.cloud.baidu.BaiduCensorTransport;
import org.springframework.stereotype.Service;


@Service("appCensorService")
public class AppCensorImpl implements AppCensorService {

    protected final ConfigSystemService configSystemService;

    private final ConfigBusinessService configBusinessService;

    private final BaiduCensorTransport baiduCensorTransport;

    public AppCensorImpl(ConfigSystemService configSystemService, ConfigBusinessService configBusinessService, BaiduCensorTransport baiduCensorTransport) {
        this.configSystemService = configSystemService;
        this.configBusinessService = configBusinessService;
        this.baiduCensorTransport = baiduCensorTransport;
    }

    @Override
    public void censorText(String text) {

        if(configBusinessService.hasViolationWord(text)){
            throw  new LinfengException("内容包含违规内容");
        }

        boolean baiduCensorTextOpen = configSystemService.getValue(Constant.BAIDU_CENSOR_TEXT_OPEN).equals("1");
        if(!baiduCensorTextOpen){
            return;
        }

        baiduCensorTransport.textCensor(text);

    }

    @Override
    public void censorImage(String url) {
        boolean baiduCensorImageOpen = configSystemService.getValue(Constant.BAIDU_CENSOR_IMAGE_OPEN).equals("1");
        if(!baiduCensorImageOpen){
            return;
        }

        baiduCensorTransport.imageCensor(url);

    }

    @Override
    public void censorVoice(String url) {
        boolean baiduCensorVoiceOpen = configSystemService.getValue(Constant.BAIDU_CENSOR_VOICE_OPEN).equals("1");
        if(!baiduCensorVoiceOpen){
            return;
        }

        baiduCensorTransport.voiceCensor(url);

    }

    @Override
    public void censorVideo(String url) {
        boolean baiduCensorVideoOpen = configSystemService.getValue(Constant.BAIDU_CENSOR_VIDEO_OPEN).equals("1");
        if(!baiduCensorVideoOpen){
            return;
        }

        baiduCensorTransport.videoCensor(url);

    }
}