package io.linfeng.business.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.business.censor.service.AppCensorService;
import io.linfeng.business.chat.response.ChatHelloResponse;
import io.linfeng.business.chat.service.AppChatFriendService;
import io.linfeng.business.chat.service.AppChatHelloService;
import io.linfeng.business.chat.request.ChatHelloRequest;
import io.linfeng.business.petal.service.AppPetalService;
import io.linfeng.business.user.service.AppUserMessageService;
import io.linfeng.common.utils.*;
import io.linfeng.love.chat.enums.ChatHelloStatus;
import io.linfeng.love.chat.enums.ChatHelloType;
import io.linfeng.love.chat.enums.FriendStatus;
import io.linfeng.love.chat.service.ChatFriendService;
import io.linfeng.love.config.service.ConfigBusinessService;
import io.linfeng.love.chat.dto.response.ChatHelloResponseDTO;
import io.linfeng.love.guest.entity.ChatHelloEntity;
import io.linfeng.love.chat.service.ChatHelloService;
import io.linfeng.love.message.service.SubscribeMessageService;
import io.linfeng.love.petal.enums.PetalRecordSubType;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.entity.UserMessageInteractEntity;
import io.linfeng.love.user.service.UserMessageInteractService;
import io.linfeng.love.user.service.UserService;
import io.linfeng.push.client.enums.InteractMessageDataType;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;


@Service("appChatHelloService")
@AllArgsConstructor
public class AppChatHelloServiceImpl implements AppChatHelloService {

    private final ConfigBusinessService configBusinessService;

    private final UserService userService;

    private final AppPetalService appPetalService;

    private final ChatHelloService chatHelloService;

    private final AppChatFriendService appChatFriendService;

    private final UserMessageInteractService userMessageInteractService;

    private final AppUserMessageService appUserMessageService;

    private final AppCensorService appCensorService;

    private final ChatFriendService chatFriendService;

    private final SubscribeMessageService subscribeMessageService;


    @Override
    @Transactional
    public void hello(UserEntity user, ChatHelloRequest request) {

        //文本审核
        appCensorService.censorText(request.getContent());

        UserEntity guestUser = userService.getUserByOid(request.getOid());

        //校验免费次数刷新时间
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        if(StringUtil.isEmpty(user.getFreeHelloTime()) || user.getFreeHelloTime().compareTo(calendar.getTime()) < 0){
            user.setFreeHelloNum(0);
            user.setFreeHelloTime(DateUtil.nowDateTime());
        }

        //校验是否是vip以及当日剩余打招呼次数，次数没用完就扣打招呼余额
        Integer configFreeHelloNum = user.getVip() == Constant.VIP_USER
                ? Integer.parseInt(configBusinessService.getValue(Constant.VIP_HI_FREE))
                : Integer.parseInt(configBusinessService.getValue(Constant.COMMON_HI_FREE));

        Integer type = ChatHelloType.FREE.getValue();

        if(configFreeHelloNum > user.getFreeHelloNum()){
            //使用免费打招呼次数来打招呼
            user.setFreeHelloNum(user.getFreeHelloNum() + 1);
            user.setUpdateTime(DateUtil.nowDateTime());
            userService.updateAndDeleteCache(user);
        }else{
            //使用花瓣余额来打招呼
            Integer configPetalHello = Integer.parseInt(configBusinessService.getValue(Constant.PETAL_HI));
            appPetalService.deductPetal(user, configPetalHello, PetalRecordSubType.HELLO.getValue());
            type = ChatHelloType.PETAL.getValue();
        }

        //检验对方的是否删除了我的好友，无感添加
        Integer friendStatus = chatFriendService.checkFriendStatus(guestUser.getUid(), user.getUid());

        if(friendStatus == FriendStatus.NO.getValue()){
            //添加或更新我的打招呼记录
            addOrUpdateHello(user.getUid(), guestUser.getUid(), user.getUid(), request.getContent(), type, ChatHelloStatus.WAITING.getValue());
            //添加或更新对方的打招呼记录
            addOrUpdateHello(guestUser.getUid(), user.getUid(), user.getUid(), request.getContent(), type, ChatHelloStatus.WAITING.getValue());
            //发送打招呼消息
            sendHelloMessage(guestUser.getUid(), user);
        }

        if(friendStatus == FriendStatus.NORMAL.getValue()){
            addOrUpdateHello(guestUser.getUid(), user.getUid(), user.getUid(), request.getContent(), type, ChatHelloStatus.SUCCESS.getValue());
            appChatFriendService.buildFriendRelation(user, guestUser);
        }

    }

    @Override
    public List<ChatHelloResponse> helloApplyList(UserEntity user) {
        LambdaQueryWrapper<ChatHelloEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatHelloEntity::getUid, user.getUid());
        List<ChatHelloResponseDTO> guestHelloResponseList = chatHelloService.getChatHelloList(user.getUid());

        Integer guestHelloExpireDay = Integer.parseInt(configBusinessService.getValue(Constant.GUEST_HELLO_EXPIRE_DAY));

        for(ChatHelloResponseDTO chatHelloResponseDTO : guestHelloResponseList){
            //等地回应状态校验过期时间
            if(DateUtil.daysBetween(chatHelloResponseDTO.getHelloTime(), DateUtil.nowDateTime()) >  guestHelloExpireDay){
                chatHelloResponseDTO.setStatus(ChatHelloStatus.EXPIRED.getValue());
            }
        }

        List<ChatHelloResponse> responseList = ObjectMapperUtil.convert(guestHelloResponseList, ChatHelloResponse.class);

        return responseList;
    }

    @Override
    public void deleteHelloApply(UserEntity user, ChatHelloRequest request) {
        UserEntity guestUser = userService.getUserByOid(request.getOid());
        LambdaQueryWrapper<ChatHelloEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatHelloEntity::getUid, user.getUid());
        wrapper.eq(ChatHelloEntity::getGuestUid, guestUser.getUid());
        chatHelloService.remove(wrapper);
    }

    @Override
    @Transactional
    public void helloReply(UserEntity user, ChatHelloRequest request) {
        //更新我的打招呼状态
        UserEntity guestUser = userService.getUserByOid(request.getOid());
        LambdaQueryWrapper<ChatHelloEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatHelloEntity::getUid, user.getUid());
        wrapper.eq(ChatHelloEntity::getGuestUid, guestUser.getUid());
        ChatHelloEntity chatHelloEntity = new ChatHelloEntity();
        chatHelloEntity.setStatus(ChatHelloStatus.SUCCESS.getValue());
        chatHelloEntity.setUpdateTime(DateUtil.nowDateTime());
        chatHelloService.update(chatHelloEntity, wrapper);

        //更新对方打招呼状态
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatHelloEntity::getUid, guestUser.getUid());
        wrapper.eq(ChatHelloEntity::getGuestUid, user.getUid());
        ChatHelloEntity otherChatHelloEntity = chatHelloService.getOne(wrapper);
        if(otherChatHelloEntity != null){
            otherChatHelloEntity.setStatus(ChatHelloStatus.SUCCESS.getValue());
            otherChatHelloEntity.setUpdateTime(DateUtil.nowDateTime());
            chatHelloService.update(otherChatHelloEntity, wrapper);
        }

        //建立朋友关系，发送消息
        appChatFriendService.buildFriendRelation(user, guestUser);

        //查询并更新订阅消息推送
        subscribeMessageService.updateSendTime(user.getOid(), guestUser.getUid(), "helloReply", DateUtil.nowDateTime());
    }

    private void sendHelloMessage(Integer guestUid, UserEntity user){
        //保存消息记录
        UserMessageInteractEntity userMessageInteractEntity = new UserMessageInteractEntity();
        userMessageInteractEntity.setUid(guestUid);
        userMessageInteractEntity.setGuestOid(user.getOid());
        userMessageInteractEntity.setGuestUserName(user.getUserName());
        userMessageInteractEntity.setGuestAvatar(user.getAvatar());
        userMessageInteractEntity.setTitle(PushMessageConstant.HELLO_TITLE);
        userMessageInteractEntity.setContent(PushMessageConstant.HELLO_TITLE);
        userMessageInteractEntity.setLinkUrl(PushMessageConstant.HELLO_LINK_URL);
        userMessageInteractEntity.setDataType(InteractMessageDataType.HELLO.getValue());
        userMessageInteractEntity.setSendTime(DateUtil.nowDateTime());
        userMessageInteractService.save(userMessageInteractEntity);
        appUserMessageService.sendUserInteractMessage(userMessageInteractEntity);
    }

    private void addOrUpdateHello(Integer uid, Integer guestUid, Integer sendUid, String content, Integer type, Integer status){
        LambdaQueryWrapper<ChatHelloEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatHelloEntity::getUid, uid);
        wrapper.eq(ChatHelloEntity::getGuestUid, guestUid);
        ChatHelloEntity chatHelloEntity = chatHelloService.getOne(wrapper);
        if(chatHelloEntity == null){
            chatHelloEntity = new ChatHelloEntity();
        }

        chatHelloEntity.setSenderUid(sendUid);
        chatHelloEntity.setType(type);
        chatHelloEntity.setStatus(status);
        chatHelloEntity.setHelloTime(DateUtil.nowDateTime());
        if(chatHelloEntity.getId() == null){
            chatHelloEntity.setUid(uid);
            chatHelloEntity.setGuestUid(guestUid);
            chatHelloEntity.setContent(content);
            chatHelloEntity.setCreateTime(DateUtil.nowDateTime());
            chatHelloService.save(chatHelloEntity);
        }else{
            chatHelloEntity.setContent(chatHelloEntity.getContent() + Constant.HELLO_CONTENT_SEPARATOR + content);
            chatHelloEntity.setUpdateTime(DateUtil.nowDateTime());
            chatHelloService.updateById(chatHelloEntity);
        }
    }
}