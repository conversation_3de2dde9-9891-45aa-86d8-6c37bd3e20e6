/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.offline.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.offline.request.ActivityExitRequest;
import io.linfeng.business.offline.request.ActivityJoinRequest;
import io.linfeng.business.offline.request.ActivityQueryRequest;
import io.linfeng.business.offline.response.ActivityDetailResponse;
import io.linfeng.business.offline.response.ActivitySimpleResponse;
import io.linfeng.love.pay.entity.PayOrderEntity;
import io.linfeng.love.user.entity.UserEntity;

/**
 * 线下活动
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
public interface AppOfflineActivityService {

    /**
     * 获取线下活动列表
     * @param user 用户
     * @param request 分页请求
     * @return 线下活动列表
     */
    IPage<ActivitySimpleResponse> getOfflineActivityList(UserEntity user, ActivityQueryRequest request);

    /**
     * 查询活动详情
     * @param user 登录用户
     * @param id 活动id
     * @return 活动详情
     */
    ActivityDetailResponse getOfflineActivityDetail(UserEntity user, Integer id);

    /**
     * 获取我参与的活动列表
     * @param user 用户
     * @param request 分页请求
     * @return 我参与的活动列表
     */
    IPage<ActivitySimpleResponse> getMyActivityList(UserEntity user, ActivityQueryRequest request);

    /**
     * 加入活动
     * @param payOrder 支付订单
     */
    void joinActivity(PayOrderEntity payOrder);

    /**
     * 退出活动退款申请
     * @param user 登录用户
     * @param request 退款请求
     */
    void activityExitApply(UserEntity user, ActivityExitRequest request);

    /**
     * 退出活动
     * @param refundNo 退款订单号
     */
    void activityExit(String refundNo);
}

