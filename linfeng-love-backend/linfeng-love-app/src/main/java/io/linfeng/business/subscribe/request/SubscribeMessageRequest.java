package io.linfeng.business.subscribe.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="订阅消息请求对象")
public class SubscribeMessageRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 渠道
	 */
	@ApiModelProperty(value = "渠道")
	private String channel;
	/**
	 * 模板id
	 */
	@ApiModelProperty(value = "模板id")
	private String tmplId;
	/**
	 * 关联id
	 */
	@ApiModelProperty(value = "关联id")
	private String linkId;
	/**
	 * 业务类型
	 */
	@ApiModelProperty(value = "业务类型")
	private String type;

}
