package io.linfeng.business.user.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
@ApiModel(description="用户提现记录响应实体")
public class UserCashedResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 金额
	 */
	@ApiModelProperty(value = "金额")
	private BigDecimal amount;
	/**
	 * 提现渠道
	 */
	@ApiModelProperty(value = "提现渠道")
	private Integer channel;
	/**
	 * 提交时间
	 */
	@ApiModelProperty(value = "提交时间")
	private Date submitTime;
	/**
	 * 审核时间
	 */
	@ApiModelProperty(value = "审核时间")
	private Date checkedTime;
	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private Integer status;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

}
