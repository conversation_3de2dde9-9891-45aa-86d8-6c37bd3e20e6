package io.linfeng.business.bottle.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="用户漂流瓶")
public class UserBottleDetailResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 内容
	 */
	@ApiModelProperty(value = "内容")
	private String content;

	/**
	 * 捞起次数
	 */
	@ApiModelProperty(value = "捞起次数")
	private Integer pickNum;

	/**
	 * 抛出时间
	 */
	@ApiModelProperty(value = "抛出时间")
	private String throwTime;
	/**
	 * 回应时间
	 */
	@ApiModelProperty(value = "回应时间")
	private String replyTime;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private Integer status;


}
