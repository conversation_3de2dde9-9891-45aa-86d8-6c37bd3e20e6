/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.identity.service;


import io.linfeng.business.identity.request.EducationCheckRequest;
import io.linfeng.business.identity.request.JobCheckRequest;
import io.linfeng.business.identity.request.RealNameRequest;
import io.linfeng.love.user.entity.UserEntity;

/**
 * 身份认证服务
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
public interface AppIdentityService {

    /**
     * 实名认证
     * @param user 登录用户
     * @param request 实名认证请求
     * @return 认证结果
     */
    void realNameCheck(UserEntity user, RealNameRequest request);

    /**
     * 学历认证
     * @param user 登录用户
     * @param request 学历认证请求
     * @return 认证结果
     */
    void educationCheck(UserEntity user, EducationCheckRequest request);

    /**
     * 工作认证
     * @param user 登录用户
     * @param request 工作认证请求
     * @return 认证结果
     */
    void jobCheck(UserEntity user, JobCheckRequest request);
}

