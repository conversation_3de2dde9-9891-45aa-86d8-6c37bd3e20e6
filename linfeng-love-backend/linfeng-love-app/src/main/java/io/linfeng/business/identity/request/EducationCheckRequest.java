package io.linfeng.business.identity.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(description = "学历认证请求对象")
public class EducationCheckRequest {

    @ApiModelProperty(value = "身份证原件照片",required = true)
    @NotBlank(message="身份证原件照片不能为空")
    private String idCardImage;

    @ApiModelProperty(value = "学历证明照片1",required = true)
    @NotBlank(message="学历证明照片1不能为空")
    private String educationImageOne;

    @ApiModelProperty(value = "学历证明照片2",required = true)
    @NotBlank(message="学历证明照片2不能为空")
    private String educationImageTwo;

}
