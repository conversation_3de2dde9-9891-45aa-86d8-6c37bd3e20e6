package io.linfeng.business.offline.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
@ApiModel(description="搭子活动简易响应对象")
public class PartnerSimpleResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 活动id
	 */
	@ApiModelProperty(value = "活动id")
	private Integer id;
	/**
	 * 活动名称
	 */
	@ApiModelProperty(value = "活动名称")
	private String activityName;
	/**
	 * 省份
	 */
	@ApiModelProperty(value = "省份")
	private String province;
	/**
	 * 城市
	 */
	@ApiModelProperty(value = "城市")
	private String city;
	/**
	 * 地址标题
	 */
	@ApiModelProperty(value = "地址标题")
	private String addressTitle;

	/**
	 * 男生参加人数
	 */
	@ApiModelProperty(value = "男生参加人数")
	private Integer manJoinNumber;
	/**
	 * 女生参加人数
	 */
	@ApiModelProperty(value = "女生参加人数")
	private Integer womanJoinNumber;
	/**
	 * 男生人数
	 */
	@ApiModelProperty(value = "男生人数")
	private Integer manNumber;
	/**
	 * 女生人数
	 */
	@ApiModelProperty(value = "女生人数")
	private Integer womanNumber;
	/**
	 * 总人数
	 */
	@ApiModelProperty(value = "总人数")
	private Integer totalNumber;
	/**
	 * 男生价格
	 */
	@ApiModelProperty(value = "男生价格")
	private BigDecimal manAmount;
	/**
	 * 女生价格
	 */
	@ApiModelProperty(value = "女生价格")
	private BigDecimal womanAmount;
	/**
	 * 活动状态
	 */
	@ApiModelProperty(value = "活动状态")
	private Integer status;
	/**
	 * 活动封面照片
	 */
	@ApiModelProperty(value = "活动封面照片")
	private String urlCover;
	/**
	 * 活动开始时间
	 */
	@ApiModelProperty(value = "活动开始时间")
	private String startTime;
	/**
	 * 活动报名结束时间
	 */
	@ApiModelProperty(value = "活动报名结束时间")
	private String joinEndTime;

}
