package io.linfeng.business.mission.service.impl;

import io.linfeng.business.mission.response.MissionDetailResponse;
import io.linfeng.business.mission.response.MissionResponse;
import io.linfeng.business.mission.service.AppMissionService;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.executor.MissionEventExecutor;
import io.linfeng.love.mission.dto.response.MissionResponseDTO;
import io.linfeng.love.mission.enums.MissionType;
import io.linfeng.love.mission.service.UserMissionService;
import io.linfeng.love.user.entity.UserEntity;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("appMissionService")
public class AppMissionServiceImpl implements AppMissionService {

    private final UserMissionService userMissionService;

    private final MissionEventExecutor missionEventExecutor;

    public AppMissionServiceImpl(UserMissionService userMissionService, MissionEventExecutor missionEventExecutor) {
        this.userMissionService = userMissionService;
        this.missionEventExecutor = missionEventExecutor;
    }

    @Override
    public List<MissionResponse> getMissionList(UserEntity user, String activityCode) {
        List<MissionResponseDTO> missionResponseDTOList = userMissionService.getMissionList(user.getUid(), activityCode);
        List<MissionResponse> missionResponseList = ObjectMapperUtil.convert(missionResponseDTOList, MissionResponse.class);

        for(MissionResponse missionResponse : missionResponseList){
            //基础任务下阶段奖励和最大奖励一致
            if(MissionType.BASE.getValue() == missionResponse.getType()){
                missionResponse.setNextTargetValue(missionResponse.getMaxTargetValue());
                missionResponse.setNextPrizeName(missionResponse.getMaxPrizeName());
                missionResponse.setNextPrizeValue(missionResponse.getMaxPrizeValue());
                continue;
            }

            //成长任务查询当前任务进度，并找出下阶段奖励及目标
            for(MissionDetailResponse missionDetailResponse :  missionResponse.getDetailList()){
                if(missionResponse.getCurrentValue() >= missionDetailResponse.getTargetValue()){
                    continue;
                }
                missionResponse.setNextTargetValue(missionDetailResponse.getTargetValue());
                missionResponse.setNextPrizeName(missionDetailResponse.getPrizeName());
                missionResponse.setNextPrizeValue(missionDetailResponse.getPrizeValue());
                break;
            }

        }
        return missionResponseList;
    }

    @Override
    public void receiveToDayMissionList(UserEntity user) {
        missionEventExecutor.receiveToDayMissionList(user);
    }


}