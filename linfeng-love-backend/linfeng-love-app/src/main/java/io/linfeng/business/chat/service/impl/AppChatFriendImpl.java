package io.linfeng.business.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.business.chat.request.ChatFriendRequest;
import io.linfeng.business.chat.request.ChatMessageRequest;
import io.linfeng.business.chat.response.ChatFriendResponse;
import io.linfeng.business.chat.service.AppChatFriendService;
import io.linfeng.business.chat.service.AppChatMessageService;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.common.utils.PushMessageConstant;
import io.linfeng.love.chat.dto.response.ChatFriendResponseDTO;
import io.linfeng.love.chat.entity.ChatFriendEntity;
import io.linfeng.love.chat.entity.ChatSessionEntity;
import io.linfeng.love.chat.enums.FriendStatus;
import io.linfeng.love.chat.enums.MessageType;
import io.linfeng.love.chat.service.ChatFriendService;
import io.linfeng.love.chat.service.ChatSessionService;
import io.linfeng.love.config.service.DictItemService;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.enums.RealNameStatus;
import io.linfeng.love.user.service.UserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service("appChatFriendService")
public class AppChatFriendImpl implements AppChatFriendService {

    private final ChatFriendService chatFriendService;

    private final UserService userService;

    private final ChatSessionService chatSessionService;

    private final DictItemService dictItemService;

    private final AppChatMessageService appChatMessageService;

    public AppChatFriendImpl(ChatFriendService chatFriendService, UserService userService, ChatSessionService chatSessionService, DictItemService dictItemService, AppChatMessageService appChatMessageService) {
        this.chatFriendService = chatFriendService;
        this.userService = userService;
        this.chatSessionService = chatSessionService;
        this.dictItemService = dictItemService;
        this.appChatMessageService = appChatMessageService;
    }

    @Override
    public List<ChatFriendResponse> getFriendList(UserEntity user) {
        List<ChatFriendResponseDTO> chatFriendResponseDTOList = chatFriendService.getFriendList(user.getUid());
        List<ChatFriendResponse> responseList = new ArrayList<>();
        chatFriendResponseDTOList.forEach(chatFriendResponseDTO -> {
            ChatFriendResponse response = ObjectMapperUtil.convert(chatFriendResponseDTO, ChatFriendResponse.class);
            response.setJobText(dictItemService.getItemName(Constant.DICT_JOB, user.getJob()));
            response.setBirthdayYear(chatFriendResponseDTO.getBirthday().substring(2,4));
            responseList.add(response);
        });

        return responseList;
    }

    @Override
    public void deleteFriend(UserEntity user, String oid) {
        UserEntity friendUser = userService.getUserByOid(oid);
        LambdaQueryWrapper<ChatFriendEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatFriendEntity::getUid, user.getUid());
        wrapper.eq(ChatFriendEntity::getFriendUid, friendUser.getUid());
        chatFriendService.remove(wrapper);

        deleteSession(user.getUid(), friendUser.getUid());
    }

    @Override
    public void updateBlackStatus(UserEntity user, ChatFriendRequest request) {

        UserEntity friendUser = userService.getUserByOid(request.getOid());
        LambdaQueryWrapper<ChatFriendEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatFriendEntity::getUid, user.getUid());
        wrapper.eq(ChatFriendEntity::getFriendUid, friendUser.getUid());

        ChatFriendEntity chatFriendEntity = new ChatFriendEntity();
        chatFriendEntity.setStatus(request.getStatus());
        chatFriendEntity.setUpdateTime(DateUtil.nowDateTime());
        chatFriendService.update(chatFriendEntity, wrapper);

        if(FriendStatus.BLACK.getValue() == request.getStatus()){
            deleteSession(user.getUid(), friendUser.getUid());
        }
    }

    @Override
    @Transactional
    public void buildFriendRelation(UserEntity user, UserEntity friend) {
        LambdaQueryWrapper<ChatFriendEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatFriendEntity::getUid, user.getUid());
        wrapper.eq(ChatFriendEntity::getFriendUid, friend.getUid());
        ChatFriendEntity chatFriendEntity = chatFriendService.getOne(wrapper);
        if(chatFriendEntity == null){
            chatFriendEntity = new ChatFriendEntity();
            chatFriendEntity.setUid(user.getUid());
            chatFriendEntity.setFriendUid(friend.getUid());
            chatFriendEntity.setStatus(FriendStatus.NORMAL.getValue());
            chatFriendEntity.setCreateTime(DateUtil.nowDateTime());
            chatFriendService.save(chatFriendEntity);
            //发送配对成功消息通知
            sendCoupleChatMessage(friend, user.getOid());
            //判断是否实名并发送引导实名消息
            checkAndSendRealNameMessage(friend, user);
        }

        //查看对方是否删了好友，没有删除就不用操作
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatFriendEntity::getUid, friend.getUid());
        wrapper.eq(ChatFriendEntity::getFriendUid, user.getUid());
        ChatFriendEntity otherChatFriend = chatFriendService.getOne(wrapper);
        if(otherChatFriend != null){
            return;
        }
        otherChatFriend = new ChatFriendEntity();
        otherChatFriend.setUid(friend.getUid());
        otherChatFriend.setFriendUid(user.getUid());
        otherChatFriend.setStatus(FriendStatus.NORMAL.getValue());
        otherChatFriend.setCreateTime(DateUtil.nowDateTime());
        chatFriendService.save(otherChatFriend);

        //发送配对成功消息通知
        sendCoupleChatMessage(user, friend.getOid());
        //判断是否实名并发送引导实名消息
        checkAndSendRealNameMessage(user, friend);

    }

    /**
     * 删除会话
     * @param uid 用户uid
     * @param friendUid 朋友uid
     */
    private void deleteSession(Integer uid, Integer friendUid){
        //删除自己的会话
        LambdaQueryWrapper<ChatSessionEntity> sessionWrapper = new LambdaQueryWrapper<>();
        sessionWrapper.eq(ChatSessionEntity::getUid, uid);
        sessionWrapper.eq(ChatSessionEntity::getFriendUid, friendUid);
        ChatSessionEntity chatSession = chatSessionService.getOne(sessionWrapper);

        if(chatSession != null){
            chatSessionService.deleteAndRefreshCache(uid, chatSession.getSessionId());
        }
    }

    /**
     * 配对成功消息
     * @param user 用户
     * @param friendOid 朋友oid
     */
    private void sendCoupleChatMessage(UserEntity user, String friendOid){
        ChatMessageRequest request = new ChatMessageRequest();
        request.setFriendOid(friendOid);
        request.setMessageType(MessageType.TIPS.getValue());
        request.setContent(PushMessageConstant.COUPLE_TITLE);
        appChatMessageService.sendMessage(user, request);
    }

    private void checkAndSendRealNameMessage(UserEntity user, UserEntity friend){
        if(friend.getRealNameStatus() == RealNameStatus.STRONG.getValue()){
            return;
        }
        ChatMessageRequest request = new ChatMessageRequest();
        request.setFriendOid(friend.getOid());
        request.setMessageType(MessageType.TEXT.getValue());
        request.setContent(PushMessageConstant.REAL_NAME_MESSAGE);
        appChatMessageService.sendMessage(user, request);
    }
}