package io.linfeng.business.chat.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="打招呼响应对象")
public class ChatHelloResponse implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 嘉宾oid
	 */
	@ApiModelProperty(value = "嘉宾oid")
	private String oid;
	/**
	 * 发送者oid
	 */
	@ApiModelProperty(value = "发送者oid")
	private String senderOid;
	/**
	 * 用户名
	 */
	@ApiModelProperty(value = "用户名")
	private String userName;
	/**
	 * 头像
	 */
	@ApiModelProperty(value = "头像")
	private String avatar;
	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private Integer status;
	/**
	 * 内容
	 */
	@ApiModelProperty(value = "内容")
	private String content;

}
