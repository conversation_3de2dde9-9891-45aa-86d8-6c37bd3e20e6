package io.linfeng.business.user.service.impl;


import cn.binarywang.wx.miniapp.api.WxMaService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.business.censor.service.AppCensorService;
import io.linfeng.business.sms.service.AppSmsService;
import io.linfeng.business.user.request.BindPhoneRequest;
import io.linfeng.business.user.request.UserLocationRequest;
import io.linfeng.business.user.request.UserUpdateRequest;
import io.linfeng.business.user.response.UserResponse;
import io.linfeng.business.user.service.AppUserService;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.common.utils.StringUtil;
import io.linfeng.love.common.enums.CommonStatus;
import io.linfeng.love.config.service.ConfigBusinessService;
import io.linfeng.love.config.service.ConfigSystemService;
import io.linfeng.love.config.service.DictItemService;
import io.linfeng.love.executor.MissionEventExecutor;
import io.linfeng.love.mission.enums.MissionTarget;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.entity.UserLoginEntity;
import io.linfeng.love.user.entity.UserPreferencesEntity;
import io.linfeng.love.user.enums.LoginStatus;
import io.linfeng.love.user.enums.RealNameStatus;
import io.linfeng.love.user.service.UserLoginService;
import io.linfeng.love.user.service.UserPreferencesService;
import io.linfeng.love.user.service.UserService;
import io.linfeng.transport.cloud.tencent.TencentCloudFaceTransport;
import io.linfeng.transport.cloud.tencent.TencentMapTransport;
import io.linfeng.transport.cloud.tencent.response.IpLocationResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Map;

@Service("appUserService")
public class AppUserServiceImpl implements AppUserService {

    private final UserService userService;

    private final WxMaService wxMaService;

    private final UserPreferencesService userPreferencesService;
    
    private final DictItemService dictItemService;

    private final ConfigBusinessService configBusinessService;

    private final MissionEventExecutor missionEventExecutor;

    private final AppSmsService appSmsService;

    private final TencentCloudFaceTransport tencentCloudFaceTransport;

    protected final ConfigSystemService configSystemService;

    private final AppCensorService appCensorService;

    private final UserLoginService userLoginService;

    private final TencentMapTransport tencentMapTransport;

    public AppUserServiceImpl(UserService userService, WxMaService wxMaService, UserPreferencesService userPreferencesService, DictItemService dictItemService, ConfigBusinessService configBusinessService, MissionEventExecutor missionEventExecutor, AppSmsService appSmsService, TencentCloudFaceTransport tencentCloudFaceTransport, ConfigSystemService configSystemService, AppCensorService appCensorService, UserLoginService userLoginService, TencentMapTransport tencentMapTransport) {
        this.userService = userService;
        this.wxMaService = wxMaService;
        this.userPreferencesService = userPreferencesService;
        this.dictItemService = dictItemService;
        this.configBusinessService = configBusinessService;
        this.missionEventExecutor = missionEventExecutor;
        this.appSmsService = appSmsService;
        this.tencentCloudFaceTransport = tencentCloudFaceTransport;
        this.configSystemService = configSystemService;
        this.appCensorService = appCensorService;
        this.userLoginService = userLoginService;
        this.tencentMapTransport = tencentMapTransport;
    }

    @Override
    public UserResponse getLoginUserInfo(Integer uid) {
        UserEntity user = userService.getUserByUid(uid);
        UserResponse userResponse = ObjectMapperUtil.convert(user, UserResponse.class);
        buildResponse(user, userResponse);
        return userResponse;
    }

    @Override
    public UserResponse getUserResponseByOid(String oid) {
        UserEntity user = userService.getUserByOid(oid);
        UserResponse userResponse = ObjectMapperUtil.convert(user, UserResponse.class);
        buildResponse(user, userResponse);
        return userResponse;
    }

    @Override
    public void updateAppUserInfo(UserUpdateRequest userUpdateRequest, UserEntity appUser) {
        UserEntity user = userService.getById(appUser.getUid());

        if (!StringUtil.isEmpty(userUpdateRequest.getUserName())) {
            //文本审核
            appCensorService.censorText(userUpdateRequest.getUserName());
            user.setUserName(userUpdateRequest.getUserName());
        }
        if (!StringUtil.isEmpty(userUpdateRequest.getStature())) {
            user.setStature(userUpdateRequest.getStature());
        }
        if (!StringUtil.isEmpty(userUpdateRequest.getWeight())) {
            user.setWeight(userUpdateRequest.getWeight());
        }
        if (!StringUtil.isEmpty(userUpdateRequest.getLivingCity())) {
            user.setLivingProvince(userUpdateRequest.getLivingProvince());
            user.setLivingCity(userUpdateRequest.getLivingCity());
        }
        if (!StringUtil.isEmpty(userUpdateRequest.getHomeCity())) {
            user.setHomeProvince(userUpdateRequest.getHomeProvince());
            user.setHomeCity(userUpdateRequest.getHomeCity());
        }
        if (!StringUtil.isEmpty(userUpdateRequest.getMarriage())) {
            user.setMarriage(userUpdateRequest.getMarriage());
        }
        if (!StringUtil.isEmpty(userUpdateRequest.getSchool())) {
            user.setSchool(userUpdateRequest.getSchool());
        }
        //如果学历原来是空的，则视为首次填写个人信息
        if(StringUtil.isEmpty(user.getEducation())){
            //任务完成事件
            missionEventExecutor.updateUserMissionTarget(user.getUid(), MissionTarget.FIRST_EDIT_INFO, 1);
        }
        if (!StringUtil.isEmpty(userUpdateRequest.getEducation())) {
            user.setEducation(userUpdateRequest.getEducation());
        }
        if (!StringUtil.isEmpty(userUpdateRequest.getJob())) {
            user.setJob(userUpdateRequest.getJob());
        }
        if (!StringUtil.isEmpty(userUpdateRequest.getSalary())) {
            user.setSalary(userUpdateRequest.getSalary());
        }
        if (!StringUtil.isEmpty(userUpdateRequest.getSignature())) {
            user.setSignature(userUpdateRequest.getSignature());
        }
        userService.updateAndDeleteCache((user));
    }

    @Override
    @Transactional
    public void updateAppUserInfoFirst(UserUpdateRequest userUpdateRequest, UserEntity user) {

        if (!StringUtil.isEmpty(userUpdateRequest.getUserName())) {
            //文本审核
            appCensorService.censorText(userUpdateRequest.getUserName());
            user.setUserName(userUpdateRequest.getUserName());
        }

        //头像违规审核
        appCensorService.censorImage(userUpdateRequest.getAvatar());
        //头像人脸检测
        tencentCloudFaceTransport.detectFace(userUpdateRequest.getAvatar());
        boolean avatarCheckOpen = configSystemService.getValue(Constant.AVATAR_CHECK_OPEN).equals("1");
        if(avatarCheckOpen){
            user.setAvatarStatus(CommonStatus.CHECKING.getValue());
        }else{
            user.setAvatar(userUpdateRequest.getAvatar());
            user.setAvatarStatus(CommonStatus.CHECKED.getValue());
            missionEventExecutor.updateUserMissionTarget(user.getUid(), MissionTarget.AVATAR_CHECKED, 1);
        }

        if (!StringUtil.isEmpty(userUpdateRequest.getAvatar())) {
            user.setLastAvatar(userUpdateRequest.getAvatar());
        }
        if (!StringUtil.isEmpty(userUpdateRequest.getGender())) {
            user.setGender(userUpdateRequest.getGender());
        }
        if (!StringUtil.isEmpty(userUpdateRequest.getLivingCity())) {
            user.setLivingProvince(userUpdateRequest.getLivingProvince());
            user.setLivingCity(userUpdateRequest.getLivingCity());
        }
        if (!StringUtil.isEmpty(userUpdateRequest.getBirthday())) {
            user.setBirthday(userUpdateRequest.getBirthday());
            try{
                user.setConstellation(DateUtil.getConstellation(userUpdateRequest.getBirthday()));
                user.setAge(DateUtil.getAge(userUpdateRequest.getBirthday()));
            }catch (Exception e){
                throw new LinfengException("获取星座失败");
            }

        }
        //用手机号注册登录，实名状态直接未弱实名
        user.setRealNameStatus(RealNameStatus.WEAK.getValue());

        userService.updateAndDeleteCache((user));
        //设置默认用户偏好，用于推荐嘉宾给用户
        userPreferencesService.save(UserPreferencesEntity.buildDefault(user.getUid()));

        //任务完成事件
        missionEventExecutor.updateUserMissionTarget(user.getUid(), MissionTarget.FIRST_EDIT_INFO, 1);

    }

    @Override
    public void updateUserAvatar(UserUpdateRequest userUpdateRequest, UserEntity user) {

        //头像违规审核
        appCensorService.censorImage(userUpdateRequest.getAvatar());
        //头像人脸审核
        tencentCloudFaceTransport.detectFace(userUpdateRequest.getAvatar());
        if(user.getRealNameStatus() == RealNameStatus.STRONG.getValue()){
            //头像人脸对比
            tencentCloudFaceTransport.compareFace(userUpdateRequest.getAvatar(), user.getRealNameImage());
        }

        user.setLastAvatar(userUpdateRequest.getAvatar());
        boolean avatarCheckOpen = configSystemService.getValue(Constant.AVATAR_CHECK_OPEN).equals("1");
        if(avatarCheckOpen){
            user.setAvatarStatus(CommonStatus.CHECKING.getValue());
        }else{
            user.setAvatar(userUpdateRequest.getAvatar());
            user.setAvatarStatus(CommonStatus.CHECKED.getValue());
        }
        userService.updateAndDeleteCache(user);
    }

    @Override
    public void bindPhone(UserEntity user, BindPhoneRequest request) {
        appSmsService.verifySmsCode(request.getMobileNo(), request.getCode());
        user.setMobile(request.getMobileNo());
        //设置实名状态为弱实名
        user.setRealNameStatus(RealNameStatus.WEAK.getValue());
        user.setUpdateTime(DateUtil.nowDateTime());
        userService.updateAndDeleteCache((user));

    }

    @Override
    public void updateUserLocation(UserLocationRequest userLocationRequest, UserEntity user) {
        LambdaQueryWrapper<UserLoginEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserLoginEntity::getUid, user.getUid());
        UserLoginEntity userLogin = userLoginService.getOne(wrapper);
        userLogin.setLatitude(userLocationRequest.getLatitude());
        userLogin.setLongitude(userLocationRequest.getLongitude());
        userLogin.setUpdateTime(DateUtil.nowDateTime());
        String city = tencentMapTransport.getCityByGeocoder(userLocationRequest.getLatitude(), userLocationRequest.getLongitude()).get("city");
        userLogin.setCity(city);
        userLoginService.updateById(userLogin);
    }

    @Override
    public void updateUserLocation(String ip, UserEntity user) {
        LambdaQueryWrapper<UserLoginEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserLoginEntity::getUid, user.getUid());
        UserLoginEntity userLogin = userLoginService.getOne(wrapper);
        IpLocationResponse response = tencentMapTransport.getLocationByIp(ip);
        if(response.getCode() == Constant.COMMON_RESPONSE_SUCCESS_CODE){
            Map<String, String> cityMap = tencentMapTransport.getCityByGeocoder(response.getLatitude(), response.getLongitude());
            userLogin.setLatitude(response.getLatitude());
            userLogin.setLongitude(response.getLongitude());
            userLogin.setCity(response.getCity());
            userLogin.setUpdateTime(DateUtil.nowDateTime());
        }

        userLoginService.updateById(userLogin);
    }

    @Override
    public String searchUserOid(String mobile) {
        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserEntity::getMobile, mobile);
        UserEntity user = userService.getOne(wrapper);
        if(user == null){
            throw new LinfengException("用户不存在");
        }
        return user.getOid();
    }

    @Override
    public void updateUserVoice(UserUpdateRequest userUpdateRequest, UserEntity user) {
        user.setVoiceDuration(userUpdateRequest.getVoiceDuration());
        user.setVoiceUrl(userUpdateRequest.getVoiceUrl());
        user.setUpdateTime(DateUtil.nowDateTime());
        userService.updateAndDeleteCache(user);
    }

    @Override
    public void deleteVoice(UserEntity user) {
        user.setVoiceUrl(null);
        user.setVoiceDuration(null);
        user.setUpdateTime(DateUtil.nowDateTime());
        userService.updateAndDeleteCache(user);
    }

    @Override
    public void deleteUser(UserEntity user) {
        user.setMobile("0" + user.getMobile().substring(1, user.getMobile().length()));
        user.setOpenid("his-" + user.getUid() + user.getOpenid());
        user.setStatus(CommonStatus.UN_CHECKED.getValue());
        user.setUpdateTime(DateUtil.nowDateTime());
        userService.updateAndDeleteCache(user);
    }

    @Override
    public void updateBackImage(UserUpdateRequest userUpdateRequest, UserEntity user) {
        user.setBackImage(userUpdateRequest.getBackImage());
        user.setUpdateTime(DateUtil.nowDateTime());
        userService.updateAndDeleteCache(user);
    }

    private void buildResponse(UserEntity user, UserResponse response){
        response.setMobile(StringUtil.maskMobile(user.getMobile()));
        if (!StringUtil.isEmpty(user.getGender())) {
            response.setGenderText(dictItemService.getItemName(Constant.DICT_GENDER, user.getGender()));
        }
        if (!StringUtil.isEmpty(user.getBirthday())) {
            response.setBirthdayYear(user.getBirthday().substring(2,4));
        }
        if (!StringUtil.isEmpty(user.getMarriage())) {
            response.setMarriageText(dictItemService.getItemName(Constant.DICT_MARRIAGE, user.getMarriage()));
        }
        if (!StringUtil.isEmpty(user.getEducation())) {
            response.setEducationText(dictItemService.getItemName(Constant.DICT_EDUCATION, user.getEducation()));
        }
        if (!StringUtil.isEmpty(user.getJob())) {
            response.setJobText(dictItemService.getItemName(Constant.DICT_JOB, user.getJob()));
        }
        if (!StringUtil.isEmpty(user.getSalary())) {
            response.setSalaryText(dictItemService.getItemName(Constant.DICT_SALARY, user.getSalary()));
        }

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        Integer alreadyHelloNum = user.getFreeHelloNum();
        if(StringUtil.isEmpty(user.getFreeHelloTime()) || user.getFreeHelloTime().compareTo(calendar.getTime()) < 0){
            alreadyHelloNum = 0;
        }

        Integer configFreeHelloNum = user.getVip() == Constant.VIP_USER
                ? Integer.parseInt(configBusinessService.getValue(Constant.VIP_HI_FREE))
                : Integer.parseInt(configBusinessService.getValue(Constant.COMMON_HI_FREE));

        response.setFreeHelloNum(configFreeHelloNum - alreadyHelloNum);

        LambdaQueryWrapper<UserLoginEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserLoginEntity::getUid, user.getUid());
        UserLoginEntity userLogin = userLoginService.getOne(wrapper);
        if(userLogin != null){
            response.setOnlineStatus(userLogin.getStatus());
            response.setLastOfflineTime(userLogin.getLastOfflineTime());
        }else{
            response.setOnlineStatus(LoginStatus.OFFLINE.getValue());
        }
    }

}