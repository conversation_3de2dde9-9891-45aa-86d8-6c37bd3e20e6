package io.linfeng.business.user.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="用户标签响应对象")
public class UserTagResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 标签Id
	 */
	@ApiModelProperty(value = "标签Id")
	private Integer id;

	/**
	 * 标签图标
	 */
	@ApiModelProperty(value = "标签图标")
	private String icon;

	/**
	 * 标签名称
	 */
	@ApiModelProperty(value = "标签名称")
	private String name;

}
