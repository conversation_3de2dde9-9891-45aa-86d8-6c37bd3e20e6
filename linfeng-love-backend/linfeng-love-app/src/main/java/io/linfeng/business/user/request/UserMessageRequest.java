package io.linfeng.business.user.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(description="用户消息请求对象")
public class UserMessageRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 消息来源
	 */
	@ApiModelProperty(value = "消息来源")
	private String messageSource;
	/**
	 * 页数
	 */
	@ApiModelProperty(value = "页数")
	private Integer pageNum;
	/**
	 * 页码
	 */
	@ApiModelProperty(value = "页码")
	private Integer pageSize;

}
