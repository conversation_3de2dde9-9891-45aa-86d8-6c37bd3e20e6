package io.linfeng.business.guest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.business.guest.request.GuestOperatorRequest;
import io.linfeng.business.guest.service.AppGuestConcernService;
import io.linfeng.business.user.service.AppUserMessageService;
import io.linfeng.love.guest.enums.*;
import io.linfeng.love.user.entity.*;
import io.linfeng.love.user.service.*;
import io.linfeng.push.client.enums.InteractMessageDataType;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.PushMessageConstant;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service("appGuestConcernService")
public class AppGuestConcernServiceImpl implements AppGuestConcernService {

    private final UserConcernService userConcernService;

    private final UserService userService;

    private final UserMessageInteractService userMessageInteractService;

    private final AppUserMessageService appUserMessageService;

    public AppGuestConcernServiceImpl(UserService userService, UserConcernService userConcernService, UserMessageInteractService userMessageInteractService, AppUserMessageService appUserMessageService) {
        this.userService = userService;
        this.userConcernService = userConcernService;
        this.userMessageInteractService = userMessageInteractService;
        this.appUserMessageService = appUserMessageService;
    }


    @Override
    @Transactional
    public void concernOperator(UserEntity user, GuestOperatorRequest request) {

        UserEntity guestUser = userService.getUserByOid(request.getOid());

        if(request.getStatus() == ConcernStatus.CONCERN.getValue()){
            LambdaQueryWrapper<UserConcernEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserConcernEntity::getUid, user.getUid());
            wrapper.eq(UserConcernEntity::getGuestUid, guestUser.getUid());
            //之前有关注过就直接更新关注状态
            UserConcernEntity userConcernEntity = userConcernService.getOne(wrapper);
            if(userConcernEntity != null){
                userConcernEntity.setStatus(ConcernStatus.CONCERN.getValue());
                userConcernEntity.setUpdateTime(DateUtil.nowDateTime());
                userConcernService.updateById(userConcernEntity);
                return;
            }
            //第一次关注发送消息通知
            userConcernEntity = new UserConcernEntity();
            userConcernEntity.setUid(user.getUid());
            userConcernEntity.setGuestUid(guestUser.getUid());
            userConcernEntity.setStatus(ConcernStatus.CONCERN.getValue());
            userConcernEntity.setCreateTime(DateUtil.nowDateTime());
            userConcernService.save(userConcernEntity);
            sendConcernMessage(guestUser.getUid(), user);
        }else{
            //取消关注
            UserConcernEntity userConcernEntity = new UserConcernEntity();
            userConcernEntity.setStatus(ConcernStatus.CANCEL.getValue());
            userConcernEntity.setUpdateTime(DateUtil.nowDateTime());
            LambdaQueryWrapper<UserConcernEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserConcernEntity::getUid, user.getUid());
            wrapper.eq(UserConcernEntity::getGuestUid, guestUser.getUid());
            userConcernService.update(userConcernEntity, wrapper);
        }

    }

    private void sendConcernMessage(Integer guestUid, UserEntity user){
        //保存消息记录
        UserMessageInteractEntity userMessageInteractEntity = new UserMessageInteractEntity();
        userMessageInteractEntity.setUid(guestUid);
        userMessageInteractEntity.setGuestOid(user.getOid());
        userMessageInteractEntity.setGuestUserName(user.getUserName());
        userMessageInteractEntity.setGuestAvatar(user.getAvatar());
        userMessageInteractEntity.setTitle(PushMessageConstant.CONCERN_TITLE);
        userMessageInteractEntity.setContent(PushMessageConstant.CONCERN_TITLE);
        userMessageInteractEntity.setLinkUrl(PushMessageConstant.GUEST_LINK_URL + user.getOid());
        userMessageInteractEntity.setDataType(InteractMessageDataType.CONCERN.getValue());
        userMessageInteractEntity.setSendTime(DateUtil.nowDateTime());
        userMessageInteractService.save(userMessageInteractEntity);
        appUserMessageService.sendUserInteractMessage(userMessageInteractEntity);

    }
}