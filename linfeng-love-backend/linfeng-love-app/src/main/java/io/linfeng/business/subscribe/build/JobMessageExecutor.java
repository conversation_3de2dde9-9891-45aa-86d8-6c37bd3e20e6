package io.linfeng.business.subscribe.build;

import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import com.alibaba.fastjson.JSON;
import io.linfeng.business.subscribe.core.AbstractSubscribeMessageExecutor;
import io.linfeng.business.subscribe.dto.WxMaSubscribeMessageDTO;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.love.user.entity.UserEntity;
import me.chanjar.weixin.mp.bean.subscribe.WxMpSubscribeMessage;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service("jobMessageExecutor")
public class JobMessageExecutor extends AbstractSubscribeMessageExecutor {

    @Override
    public WxMaSubscribeMessageDTO buildWxMaSubscribeMessage(UserEntity user, String tmplId, String linkId) {
        WxMaSubscribeMessageDTO wxMaSubscribeMessageDTO = new WxMaSubscribeMessageDTO();
        WxMaSubscribeMessage wxMaSubscribeMessage = new WxMaSubscribeMessage();
        wxMaSubscribeMessage.setTemplateId(tmplId);
        wxMaSubscribeMessage.setToUser(user.getOpenid());
        wxMaSubscribeMessage.setPage(getPageUrl(linkId));
        List<WxMaSubscribeMessage.MsgData> msgDataList = new ArrayList<>();
        WxMaSubscribeMessage.MsgData msgData1 = new WxMaSubscribeMessage.MsgData();
        msgData1.setName("thing5");
        msgData1.setValue("工作审核");
        WxMaSubscribeMessage.MsgData msgData2 = new WxMaSubscribeMessage.MsgData();
        msgData2.setName("phrase1");
        msgData2.setValue("--phrase1--");
        WxMaSubscribeMessage.MsgData msgData3 = new WxMaSubscribeMessage.MsgData();
        msgData3.setName("time9");
        msgData3.setValue(DateUtil.nowDate("yyyy-MM-dd HH:mm"));
        msgDataList.add(msgData1);
        msgDataList.add(msgData2);
        msgDataList.add(msgData3);
        wxMaSubscribeMessage.setData(msgDataList);
        saveSubscribeMessage(user, tmplId, linkId, JSON.toJSONString(wxMaSubscribeMessage));
        wxMaSubscribeMessageDTO.setImmediately(false);
        wxMaSubscribeMessageDTO.setWxMaSubscribeMessage(wxMaSubscribeMessage);
        return wxMaSubscribeMessageDTO;
    }

    @Override
    public WxMpSubscribeMessage buildWxMpSubscribeMessage(UserEntity user, String tmplId, String linkId) {
        WxMpSubscribeMessage wxMpSubscribeMessage = new WxMpSubscribeMessage();
        return wxMpSubscribeMessage;
    }

    @Override
    public String getPageUrl(String linkId) {
        return "/pages/user/base/index";
    }

    @Override
    public String getMessageType() {
        return "job";
    }
}