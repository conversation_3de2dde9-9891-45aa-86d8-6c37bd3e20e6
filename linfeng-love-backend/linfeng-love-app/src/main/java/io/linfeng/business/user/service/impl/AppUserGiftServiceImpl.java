package io.linfeng.business.user.service.impl;


import io.linfeng.business.user.response.UserGiftResponse;
import io.linfeng.business.user.service.AppUserGiftService;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.user.dto.response.UserGiftResponseDTO;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.service.UserGiftService;
import io.linfeng.love.user.service.UserService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("appUserGiftService")
@AllArgsConstructor
public class AppUserGiftServiceImpl implements AppUserGiftService {

    private final UserGiftService userGiftService;

    @Override
    public List<UserGiftResponse> getUserGiftList(UserEntity user) {
        List<UserGiftResponseDTO> responseDTOList = userGiftService.getUserGiftList(user.getUid());
        return ObjectMapperUtil.convert(responseDTOList, UserGiftResponse.class);
    }

}