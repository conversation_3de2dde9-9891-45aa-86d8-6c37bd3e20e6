/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.guest.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.guest.request.GuestOperatorRequest;
import io.linfeng.business.guest.request.RecommendQueryRequest;
import io.linfeng.business.guest.request.VicinityQueryRequest;
import io.linfeng.business.guest.response.*;
import io.linfeng.love.user.entity.UserEntity;

import java.util.List;

/**
 * 嘉宾业务服务
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
public interface AppGuestService {

    /**
     * 校验并新增每日推荐数据
     * @param user 登录用户
     */
    void checkAndInitRecommend(UserEntity user);

    /**
     * 获取推荐嘉宾列表
     * @param user 登录用户
     * @param request
     * @return
     */
    List<GuestSimpleResponse> getRecommendGuestList(UserEntity user, RecommendQueryRequest request);

    /**
     * 获取最新的推荐嘉宾信息
     * @param user 登录用户
     * @return 最新推荐嘉宾信息
     */
    GuestResponse getLatestRecommend(UserEntity user);

    /**
     * 推荐嘉宾配对
     * @param user 登录用户
     * @param request 操作请求
     * @return 配对结果
     */
    GuestOperatorResponse recommendOperator(UserEntity user, GuestOperatorRequest request);

    /**
     * 查询历史推荐嘉宾列表
     * @param user 登录用户
     * @return 历史推荐嘉宾列表
     */
    GuestHistoryResponse getHistoryRecommend(UserEntity user);


    /**
     * 获取嘉宾主页信息
     * @param user 登录用户
     * @param oid 嘉宾oid
     * @return 嘉宾主页信息
     */
    GuestResponse getGuestDetail(UserEntity user, String oid);


    /**
     * 获取嘉宾汇总信息
     * @param user 登录用户
     * @return 嘉宾汇总信息
     */
    GuestSummaryResponse getGuestSummary(UserEntity user);

    /**
     * 查询我喜欢的列表
     * @param user 登录用户
     * @return 我喜欢的列表
     */
    List<GuestSimpleResponse> getLikeList(UserEntity user);

    /**
     * 查询喜欢我的列表
     * @param user 登录用户
     * @return 喜欢我的列表
     */
    List<GuestSimpleResponse> getLikeMeList(UserEntity user);

    /**
     * 查询看过我的列表
     * @param user 登录用户
     * @return 看过我的列表
     */
    List<GuestSimpleResponse> getLookMeList(UserEntity user);

    /**
     * 新增一个推荐
     * @param user 登录用户
     */
    void newRecommend(UserEntity user);

    /**
     * 查询附近嘉宾列表
     * @param user 登录用户
     * @param request 分页条件
     * @return
     */
    IPage<GuestSimpleResponse> getVicinityGuestList(UserEntity user, VicinityQueryRequest request);

    /**
     * 嘉宾礼物查询
     * @param oid 嘉宾oid
     * @return 嘉宾礼物
     */
    GuestGiftInfoResponse getGuestGiftInfo(String oid);

}

