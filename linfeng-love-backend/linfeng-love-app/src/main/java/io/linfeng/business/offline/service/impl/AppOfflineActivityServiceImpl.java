package io.linfeng.business.offline.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.linfeng.business.offline.request.ActivityExitRequest;
import io.linfeng.business.offline.request.ActivityQueryRequest;
import io.linfeng.business.offline.response.ActivityDetailResponse;
import io.linfeng.business.offline.response.ActivitySimpleResponse;
import io.linfeng.business.offline.service.AppOfflineActivityService;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.offline.dto.response.ActivityDetailResponseDTO;
import io.linfeng.love.offline.dto.response.ActivitySimpleResponseDTO;
import io.linfeng.love.offline.entity.OfflineActivityEntity;
import io.linfeng.love.offline.entity.OfflineActivityJoinEntity;
import io.linfeng.love.offline.enums.ActivityJoinStatus;
import io.linfeng.love.offline.service.OfflineActivityJoinService;
import io.linfeng.love.offline.service.OfflineActivityService;
import io.linfeng.love.pay.entity.PayOrderEntity;
import io.linfeng.love.pay.entity.PayRefundEntity;
import io.linfeng.love.pay.service.PayOrderService;
import io.linfeng.love.pay.service.PayRefundService;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.service.UserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service("appOfflineActivityService")
public class AppOfflineActivityServiceImpl implements AppOfflineActivityService {

    private final OfflineActivityService offlineActivityService;

    private final OfflineActivityJoinService offlineActivityJoinService;

    private final UserService userService;

    private final PayOrderService payOrderService;

    private final PayRefundService payRefundService;

    public AppOfflineActivityServiceImpl(OfflineActivityService offlineActivityService, OfflineActivityJoinService offlineActivityJoinService, UserService userService, PayOrderService payOrderService, PayRefundService payRefundService) {
        this.offlineActivityService = offlineActivityService;
        this.offlineActivityJoinService = offlineActivityJoinService;
        this.userService = userService;
        this.payOrderService = payOrderService;
        this.payRefundService = payRefundService;
    }

    @Override
    public IPage<ActivitySimpleResponse> getOfflineActivityList(UserEntity user, ActivityQueryRequest request) {
        IPage<ActivitySimpleResponseDTO> dtoPage = new Page<>(request.getPageNum(),request.getPageSize());
        dtoPage = offlineActivityService.getActivityList(dtoPage, user.getUid(), request.getCity(), request.getQueryType());
        IPage<ActivitySimpleResponse> page = ObjectMapperUtil.convert(dtoPage, ActivitySimpleResponse.class);
        return page;
    }

    @Override
    public ActivityDetailResponse getOfflineActivityDetail(UserEntity user, Integer id) {
        ActivityDetailResponseDTO responseDTO = offlineActivityService.getActivityDetail(id, user.getUid());
        ActivityDetailResponse response = ObjectMapperUtil.convert(responseDTO, ActivityDetailResponse.class);
        List<String> avatarList = offlineActivityJoinService.getJoinAvatarList(id);
        response.setAvatarList(avatarList);
        return response;
    }

    @Override
    public IPage<ActivitySimpleResponse> getMyActivityList(UserEntity user, ActivityQueryRequest request) {
        IPage<ActivitySimpleResponseDTO> dtoPage = new Page<>(request.getPageNum(),request.getPageSize());
        dtoPage = offlineActivityService.getMyActivityList(dtoPage, user.getUid());
        IPage<ActivitySimpleResponse> page = ObjectMapperUtil.convert(dtoPage, ActivitySimpleResponse.class);
        return page;
    }

    @Override
    @Transactional
    public void joinActivity(PayOrderEntity payOrder) {
        OfflineActivityJoinEntity offlineActivityJoin = new OfflineActivityJoinEntity();;
        offlineActivityJoin.setActivityId(Integer.parseInt(payOrder.getAttach()));
        offlineActivityJoin.setUid(payOrder.getUid());
        offlineActivityJoin.setAmount(payOrder.getAmount());
        offlineActivityJoin.setOrderNo(payOrder.getOrderNo());
        offlineActivityJoin.setJoinTime(DateUtil.nowDateTime());
        offlineActivityJoin.setStatus(ActivityJoinStatus.JOIN.getValue());
        offlineActivityJoin.setCreateTime(DateUtil.nowDateTime());
        offlineActivityJoinService.save(offlineActivityJoin);

        OfflineActivityEntity offlineActivity = offlineActivityService.getById(Integer.parseInt(payOrder.getAttach()));
        UserEntity user = userService.getUserByUid(payOrder.getUid());
        if(user.getGender() == Constant.MAN){
            offlineActivity.setManJoinNumber(offlineActivity.getManJoinNumber() + 1);
        }
        if(user.getGender() == Constant.WOMAN){
            offlineActivity.setWomanJoinNumber(offlineActivity.getWomanJoinNumber() + 1);
        }
        offlineActivity.setUpdateTime(DateUtil.nowDateTime());

        offlineActivityService.updateById(offlineActivity);
    }

    @Override
    @Transactional
    public void activityExitApply(UserEntity user, ActivityExitRequest request) {
        LambdaQueryWrapper<OfflineActivityJoinEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OfflineActivityJoinEntity::getActivityId, request.getActivityId());
        wrapper.eq(OfflineActivityJoinEntity::getUid, user.getUid());
        wrapper.eq(OfflineActivityJoinEntity::getStatus, ActivityJoinStatus.JOIN.getValue());
        OfflineActivityJoinEntity offlineActivityJoin = offlineActivityJoinService.getOne(wrapper);
        if(offlineActivityJoin == null){
            throw new LinfengException("退款参数错误");
        }

        //创建退款订单
        LambdaQueryWrapper<PayOrderEntity> payWrapper = new LambdaQueryWrapper<>();
        payWrapper.eq(PayOrderEntity::getOrderNo, offlineActivityJoin.getOrderNo());
        PayOrderEntity payOrder = payOrderService.getOne(payWrapper);
        PayRefundEntity payRefundEntity = new PayRefundEntity();
        payRefundEntity.setOrderNo(offlineActivityJoin.getOrderNo());
        payRefundEntity.setUid(user.getUid());
        payRefundEntity.setTotal(payOrder.getAmount());
        payRefundService.save(payRefundEntity);

        offlineActivityJoin.setRefundNo(payRefundEntity.getRefundNo());
        offlineActivityJoin.setStatus(ActivityJoinStatus.EXITING.getValue());
        offlineActivityJoin.setExitSubmitTime(DateUtil.nowDateTime());
        offlineActivityJoin.setUpdateTime(DateUtil.nowDateTime());
        offlineActivityJoinService.updateById(offlineActivityJoin);

    }

    @Override
    @Transactional
    public void activityExit(String refundNo) {
        LambdaQueryWrapper<OfflineActivityJoinEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OfflineActivityJoinEntity::getRefundNo, refundNo);
        OfflineActivityJoinEntity offlineActivityJoin = offlineActivityJoinService.getOne(wrapper);
        offlineActivityJoin.setStatus(ActivityJoinStatus.EXITED.getValue());
        offlineActivityJoin.setExitTime(DateUtil.nowDateTime());
        offlineActivityJoin.setUpdateTime(DateUtil.nowDateTime());
        offlineActivityJoinService.updateById(offlineActivityJoin);

        OfflineActivityEntity offlineActivity = offlineActivityService.getById(offlineActivityJoin.getActivityId());
        UserEntity user = userService.getUserByUid(offlineActivityJoin.getUid());
        if(user.getGender() == Constant.MAN){
            offlineActivity.setManJoinNumber(offlineActivity.getManJoinNumber() - 1);
        }
        if(user.getGender() == Constant.WOMAN){
            offlineActivity.setWomanJoinNumber(offlineActivity.getWomanJoinNumber() - 1);
        }
        offlineActivity.setUpdateTime(DateUtil.nowDateTime());

        offlineActivityService.updateById(offlineActivity);
    }
}