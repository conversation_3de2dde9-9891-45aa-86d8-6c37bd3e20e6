package io.linfeng.business.user.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="用户礼物响应实体")
public class UserGiftResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 礼物ID
	 */
	@ApiModelProperty(value = "礼物ID")
	private Integer giftId;
	/**
	 * 礼物名称
	 */
	@ApiModelProperty(value = "礼物名称")
	private String giftName;
	/**
	 * 静态图片地址
	 */
	@ApiModelProperty(value = "静态图片地址")
	private String staticUrl;

	/**
	 * 动态图片地址
	 */
	@ApiModelProperty(value = "动态图片地址")
	private String dynamicUrl;

	/**
	 * 数量
	 */
	@ApiModelProperty(value = "数量")
	private Integer number;

}
