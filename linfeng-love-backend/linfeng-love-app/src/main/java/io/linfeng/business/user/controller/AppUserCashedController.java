/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.user.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.user.request.ExchangeCashRequest;
import io.linfeng.business.user.request.UserCashedQueryRequest;
import io.linfeng.business.user.response.UserCashedResponse;
import io.linfeng.business.user.service.AppUserCashedService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 用户提现Api
 */
@RestController
@RequestMapping("/app/cash")
@Api(tags = "用户提现Api")
public class AppUserCashedController {

    private final AppUserCashedService appUserCashedService;

    public AppUserCashedController(AppUserCashedService appUserCashedService) {
        this.appUserCashedService = appUserCashedService;
    }


    /**
     * 用户提现明细列表（分页）
     * @param user 登录用户
     * @return 用户提现明细列表
     */
    @Login
    @GetMapping ("/list")
    @ApiOperation("用户提现明细列表")
    public Result<IPage<UserCashedResponse>> recordList(@ApiIgnore @LoginUser UserEntity user, UserCashedQueryRequest request){
        IPage<UserCashedResponse> userCashedPage = appUserCashedService.getUserCashedPage(user, request);
        return new Result<IPage<UserCashedResponse>>().ok(userCashedPage);
    }

    /**
     * 现金提取提交
     * @param user 登录用户
     * @param request 请求
     * @return 现金提取提交
     */
    @Login
    @PostMapping("/submit")
    @ApiOperation(" 现金提取")
    public R submit(@ApiIgnore @LoginUser UserEntity user, @RequestBody ExchangeCashRequest request){
        appUserCashedService.exchangeCashSubmit(user, request);
        return R.ok();
    }


}
