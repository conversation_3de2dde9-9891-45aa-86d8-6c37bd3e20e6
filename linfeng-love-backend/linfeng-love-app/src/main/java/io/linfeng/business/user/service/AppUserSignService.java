package io.linfeng.business.user.service;

import io.linfeng.love.user.entity.UserEntity;

import java.util.Map;

/**
 * 用户介绍信息业务服务
 *
 * <AUTHOR>
 * @date 2023-09-06 16:22:14
 */
public interface AppUserSignService {

    /**
     * 获取用户介绍列表
     * @param uid 用户id
     * @return 用户介绍列表
     */
    Map<String, Object> getUserSignDetail(UserEntity user);

    /**
     * 签到提交
     * @param user 登录用户
     */
    void signSubmit(UserEntity user);
}

