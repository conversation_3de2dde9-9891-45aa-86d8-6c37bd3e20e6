package io.linfeng.business.identity.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(description = "实名认证请求对象")
public class RealNameRequest {

    @ApiModelProperty(value = "真实姓名",required = true)
    @NotBlank(message="真实姓名不能为空")
    private String realName;

    @ApiModelProperty(value = "身份证号",required = true)
    @NotBlank(message="身份证号不能为空")
    private String idCard;

    @ApiModelProperty(value = "自拍照",required = true)
    @NotBlank(message="自拍照不能为空")
    private String imageUrl;

}
