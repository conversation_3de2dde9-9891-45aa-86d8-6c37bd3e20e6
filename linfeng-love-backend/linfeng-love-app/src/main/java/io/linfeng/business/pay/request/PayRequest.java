package io.linfeng.business.pay.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "支付请求对象")
public class PayRequest {

    @ApiModelProperty(value = "用户id")
    private Integer uid;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "终端来源")
    private String terminalType;

}
