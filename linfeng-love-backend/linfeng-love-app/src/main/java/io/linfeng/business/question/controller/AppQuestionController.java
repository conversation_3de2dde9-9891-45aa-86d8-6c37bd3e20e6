/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.question.controller;

import io.linfeng.business.question.response.QuestionGroupResponse;
import io.linfeng.business.question.response.QuestionResponse;
import io.linfeng.business.question.service.AppQuestionService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.api.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 帮助中心Api
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
@RestController
@RequestMapping("app/question")
@Api(tags = "帮助中心Api")
public class AppQuestionController {

    private final AppQuestionService appQuestionService;

    public AppQuestionController(AppQuestionService appQuestionService) {
        this.appQuestionService = appQuestionService;
    }

    /**
     * 查询问题列表
     * @return 问题列表
     */
    @Login
    @GetMapping("/list")
    @ApiOperation("问题列表")
    public Result<List<QuestionGroupResponse>> list(){
        List<QuestionGroupResponse> topicList = appQuestionService.getQuestionList();
        return new Result<List<QuestionGroupResponse>>().ok(topicList);
    }

    /**
     * 查询问题详情
     * @param id 问题id
     * @return 问题详情
     */
    @Login
    @GetMapping("/detail")
    @ApiOperation("问题详情")
    public Result<QuestionResponse> detail(Integer id){
        QuestionResponse topicResponse = appQuestionService.getQuestionById(id);
        return new Result<QuestionResponse>().ok(topicResponse);
    }

}
