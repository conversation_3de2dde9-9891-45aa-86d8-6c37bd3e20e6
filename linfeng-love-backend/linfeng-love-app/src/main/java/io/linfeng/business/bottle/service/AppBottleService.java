package io.linfeng.business.bottle.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.bottle.request.BottleHelloRequest;
import io.linfeng.business.bottle.request.BottleMessageRequest;
import io.linfeng.business.bottle.request.ReplyBottleRequest;
import io.linfeng.business.bottle.request.ThrowBottleRequest;
import io.linfeng.business.bottle.response.*;
import io.linfeng.love.user.entity.UserEntity;

import java.util.List;

/**
 * App漂流瓶服务
 *
 * <AUTHOR>
 * @date 2023-09-06 16:22:14
 */
public interface AppBottleService {

    List<UserBottleDetailResponse> getUserBottleDetailList(UserEntity user);

    void throwBottle(UserEntity user, ThrowBottleRequest request);

    void reply(UserEntity user, ReplyBottleRequest request);

    BottleResponse pick(UserEntity user);

    List<BottleSessionResponse> getBottleSessionList(UserEntity user);

    void deleteSession(UserEntity user, Integer bottleId);

    IPage<BottleMessageResponse> getMessageList(UserEntity user, BottleMessageRequest request);

    BottleMessageResponse sendMessage(UserEntity user, BottleMessageRequest request);

    void readMessage(UserEntity user, BottleMessageRequest request);

    UserBottleResponse getUserBottle(UserEntity user);

    BottleSessionResponse getBottleSession(UserEntity user, Integer bottleId);
}

