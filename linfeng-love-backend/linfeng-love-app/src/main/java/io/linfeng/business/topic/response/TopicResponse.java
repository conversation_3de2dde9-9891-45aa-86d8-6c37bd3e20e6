package io.linfeng.business.topic.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="话题响应对象")
public class TopicResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 话题id
	 */
	@ApiModelProperty(value = "话题id")
	private Integer topicId;
	/**
	 * 话题标题
	 */
	@ApiModelProperty(value = "话题标题")
	private String topicTitle;
	/**
	 * 话题标题
	 */
	@ApiModelProperty(value = "话题描述")
	private String introduce;
	/**
	 * 话题标题
	 */
	@ApiModelProperty(value = "背景图片")
	private String backgroundImage;
	/**
	 * 动态数量
	 */
	@ApiModelProperty(value = "动态数量")
	private Integer momentCount;

}
