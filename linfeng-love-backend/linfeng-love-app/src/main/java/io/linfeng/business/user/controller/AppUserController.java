/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.user.controller;


import io.linfeng.business.user.request.*;
import io.linfeng.business.user.response.UserResponse;
import io.linfeng.business.user.service.AppUserService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.common.utils.IPUtil;
import io.linfeng.common.utils.StringUtil;
import io.linfeng.common.utils.ValidatorUtils;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户Api
 *
 */
@RestController
@RequestMapping("/app/user")
@Api(tags = "用户Api")
public class AppUserController {

    private final AppUserService appUserService;

    public AppUserController(AppUserService appUserService) {
        this.appUserService = appUserService;
    }

    /**
     * 手机号绑定（微信公众号）
     * @param user 登录用户
     * @param request 请求对象
     * @return 绑定结果
     */
    @Login
    @PostMapping("/bindPhone")
    @ApiOperation("微信手机号绑定")
    public R bindPhone(@ApiIgnore @LoginUser UserEntity user, @RequestBody BindPhoneRequest request){
        ValidatorUtils.validateEntity(request);
        appUserService.bindPhone(user, request);
        return R.ok();
    }

    /**
     * 获取登录用户信息
     * @param user 登录用户
     * @return 登录用户
     */
    @Login
    @GetMapping("/userInfo")
    @ApiOperation("获取用户信息")
    public Result<UserResponse> userInfo(@ApiIgnore @LoginUser UserEntity user){
        UserResponse response= appUserService.getLoginUserInfo(user.getUid());
        return new Result<UserResponse>().ok(response);
    }

    /**
     * 获取用户信息（根据oid）
     * @param oid 用户oid
     * @return 用户信息
     */
    @Login
    @GetMapping("/getByOid")
    @ApiOperation("获取用户信息（根据oid）")
    public Result<UserResponse> userInfo(String oid){
        UserResponse response= appUserService.getUserResponseByOid(oid);
        return new Result<UserResponse>().ok(response);
    }

    /**
     * 修改用户信息
     * @param user 登录用户
     * @param userUpdateRequest 需要更新的信息
     * @return 修改结果
     */
    @Login
    @PostMapping("/userInfoEdit")
    @ApiOperation("用户修改个人信息")
    public R userInfoEdit(@ApiIgnore @LoginUser UserEntity user, @RequestBody UserUpdateRequest userUpdateRequest){
        ValidatorUtils.validateEntity(userUpdateRequest);
        appUserService.updateAppUserInfo(userUpdateRequest,user);
        return R.ok("修改成功");
    }

    /**
     * 新用户首次补充个人信息
     * @param user 登录用户
     * @param userUpdateRequest 需要更新的信息
     * @return 修改结果
     */
    @Login
    @PostMapping("/userInfoFirstEdit")
    @ApiOperation("用户首次填写个人信息")
    public R userInfoFirstEdit(@ApiIgnore @LoginUser UserEntity user, @RequestBody UserUpdateRequest userUpdateRequest){
        ValidatorUtils.validateEntity(userUpdateRequest);
        appUserService.updateAppUserInfoFirst(userUpdateRequest,user);
        return R.ok("设置成功");
    }

    /**
     * 修改语音介绍
     * @param user 登录用户
     * @param userUpdateRequest 更新请求
     * @return 修改结果
     */
    @Login
    @PostMapping("/voiceEdit")
    @ApiOperation("修改语音介绍")
    public R voiceEdit(@ApiIgnore @LoginUser UserEntity user, @RequestBody UserUpdateRequest userUpdateRequest){
        appUserService.updateUserVoice(userUpdateRequest,user);
        return R.ok("设置成功");
    }

    /**
     * 删除语音介绍
     * @param user 登录用户
     * @return 删除结果
     */
    @Login
    @PostMapping("/deleteVoice")
    @ApiOperation("删除语音介绍")
    public R deleteVoice(@ApiIgnore @LoginUser UserEntity user){
        appUserService.deleteVoice(user);
        return R.ok("删除成功");
    }

    /**
     * 修改头像
     * @param user 登录用户
     * @param userUpdateRequest 更新请求
     * @return 修改结果
     */
    @Login
    @PostMapping("/avatarEdit")
    @ApiOperation("修改头像")
    public R avatarEdit(@ApiIgnore @LoginUser UserEntity user, @RequestBody UserUpdateRequest userUpdateRequest){
        ValidatorUtils.validateEntity(userUpdateRequest);
        appUserService.updateUserAvatar(userUpdateRequest,user);
        return R.ok("设置成功");
    }

    /**
     * 修改背景
     * @param user 登录用户
     * @param userUpdateRequest 更新请求
     * @return 修改结果
     */
    @Login
    @PostMapping("/backImageEdit")
    @ApiOperation("修改背景")
    public R backImageEdit(@ApiIgnore @LoginUser UserEntity user, @RequestBody UserUpdateRequest userUpdateRequest){
        appUserService.updateBackImage(userUpdateRequest,user);
        return R.ok("设置成功");
    }

    /**
     * 是否有微信公众号openID
     * @return 是否有微信公众号openID
     */
    @Login
    @GetMapping("/hasMpOpenid")
    @ApiOperation("是否有微信公众号openid")
    public R hasMpOpenid(@LoginUser UserEntity user){
        return R.ok().put("result", !StringUtil.isEmpty(user.getMpOpenid()));
    }

    @Login
    @PostMapping("/locationUpdate")
    @ApiOperation("定位更新")
    public R locationUpdate(@ApiIgnore @LoginUser UserEntity user, @RequestBody UserLocationRequest userLocationRequest){
        appUserService.updateUserLocation(userLocationRequest,user);
        return R.ok("设置成功");
    }

    @Login
    @PostMapping("/locationUpdateByIp")
    @ApiOperation("定位更新(根据ip)")
    public R locationUpdateByIp(@ApiIgnore @LoginUser UserEntity user, HttpServletRequest httpServletRequest){
        appUserService.updateUserLocation(IPUtil.getIp(httpServletRequest),user);
        return R.ok("设置成功");
    }

    @Login
    @PostMapping("/search")
    @ApiOperation("查询用户")
    public R search(@ApiIgnore @LoginUser UserEntity user, @RequestBody UserSearchRequest userSearchRequest){
        String oid = appUserService.searchUserOid(userSearchRequest.getMobile());
        return R.ok("设置成功").put("result", oid);
    }

    @Login
    @PostMapping("/delete")
    @ApiOperation("删除用户")
    public R search(@ApiIgnore @LoginUser UserEntity user){
        appUserService.deleteUser(user);
        return R.ok();
    }

}
