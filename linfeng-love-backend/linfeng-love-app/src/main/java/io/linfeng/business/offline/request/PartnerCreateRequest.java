
package io.linfeng.business.offline.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
@ApiModel(description="搭子创建请求对象")
public class PartnerCreateRequest {
	/**
	 * 搭子ID
	 */
	@ApiModelProperty(value = "搭子ID")
	private Integer id;

	/**
	 * 类型ID
	 */
	@ApiModelProperty(value = "类型ID")
	private Integer typeId;

	/**
	 * 活动名称
	 */
	@ApiModelProperty(value = "活动名称")
	private String activityName;
	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "开始时间")
	private Date startTime;

	/**
	 * 报名结束时间
	 */
	@ApiModelProperty(value = "报名结束时间")
	private Date joinEndTime;

	/**
	 * 维度
	 */
	@ApiModelProperty(value = "维度")
	private String latitude;

	/**
	 * 精度
	 */
	@ApiModelProperty(value = "精度")
	private String longitude;

	/**
	 * 省份
	 */
	@ApiModelProperty(value = "省份")
	private String province;

	/**
	 * 城市
	 */
	@ApiModelProperty(value = "城市")
	private String city;

	/**
	 * 地址标题
	 */
	@ApiModelProperty(value = "地址标题")
	private String addressTitle;

	/**
	 * 地址详情
	 */
	@ApiModelProperty(value = "地址详情")
	private String addressDetail;

	/**
	 * 男生参加人数
	 */
	@ApiModelProperty(value = "男生参加人数")
	private Integer manJoinNumber;

	/**
	 * 女生参加人数
	 */
	@ApiModelProperty(value = "女生参加人数")
	private Integer womanJoinNumber;

	/**
	 * 总人数
	 */
	@ApiModelProperty(value = "总人数")
	private Integer totalNumber;

	/**
	 * 男士人数
	 */
	@ApiModelProperty(value = "男士人数")
	private Integer manNumber;

	/**
	 * 女士人数
	 */
	@ApiModelProperty(value = "女士人数")
	private Integer womanNumber;

	/**
	 * 男士费用
	 */
	@ApiModelProperty(value = "男士费用")
	private BigDecimal manAmount;

	/**
	 * 女士费用
	 */
	@ApiModelProperty(value = "女士费用")
	private BigDecimal womanAmount;

	/**
	 * 手机号
	 */
	@ApiModelProperty(value = "手机号")
	private String mobile;

	/**
	 * 活动照片列表
	 */
	@ApiModelProperty(value = "活动照片列表")
	private String mediaList;

	/**
	 * 活动详情
	 */
	@ApiModelProperty(value = "活动详情")
	private String remark;

}
