package io.linfeng.business.accusation.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description="举报请求对象")
public class AccusationRequest {

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "标签")
    private Integer tag;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "关联id")
    private String linkId;

    @ApiModelProperty(value = "附件列表")
    private List<AccusationMediaRequest> mediaList;

}
