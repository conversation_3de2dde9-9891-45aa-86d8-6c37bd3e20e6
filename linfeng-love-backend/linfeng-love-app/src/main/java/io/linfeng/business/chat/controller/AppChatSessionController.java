/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.chat.controller;


import io.linfeng.business.chat.request.ChatSessionRequest;
import io.linfeng.business.chat.response.ChatSessionResponse;
import io.linfeng.business.chat.service.AppChatSessionService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


/**
 *  聊天会话Api
 */
@RestController
@RequestMapping("/app/chat/session")
@Api(tags = "聊天会话Api")
public class AppChatSessionController {

    private final AppChatSessionService appChatSessionService;


    public AppChatSessionController(AppChatSessionService appChatSessionService) {
        this.appChatSessionService = appChatSessionService;
    }

    /**
     * 聊天会话查询
     * @param user 登录用户
     * @return 聊天会话列表
     */
    @Login
    @GetMapping("/list")
    @ApiOperation("查询所有会话")
    public Result<List<ChatSessionResponse>> list(@ApiIgnore @LoginUser UserEntity user){
        List<ChatSessionResponse> chatSessionList = appChatSessionService.getAllSessionList(user);
        return new Result<List<ChatSessionResponse>>().ok(chatSessionList);
    }

    /**
     * 聊天会话详情
     * @param user 登录用户
     * @param friendOid 朋友oid
     * @return 聊天会话详情
     */
    @Login
    @GetMapping("/detail")
    @ApiOperation("查询会话详情")
    public Result<ChatSessionResponse> detail(@ApiIgnore @LoginUser UserEntity user, String friendOid){
        ChatSessionResponse chatSession = appChatSessionService.getSessionResponse(user.getUid(), friendOid);
        return new Result<ChatSessionResponse>().ok(chatSession);
    }

    /**
     * 删除聊天会话
     * @param user 登录用户
     * @param request 删除请求
     * @return 删除结果
     */
    @Login
    @PostMapping("/delete")
    @ApiOperation("删除会话")
    public R delete(@ApiIgnore @LoginUser UserEntity user, @RequestBody ChatSessionRequest request){
        appChatSessionService.deleteSession(user, request.getSessionId());
        return R.ok();
    }

}
