package io.linfeng.business.subscribe.service.impl;

import io.linfeng.business.subscribe.core.SubscribeMessageExecutor;
import io.linfeng.business.subscribe.core.SubscribeMessageExecutorFactory;
import io.linfeng.business.subscribe.request.SubscribeMessageRequest;
import io.linfeng.business.subscribe.service.AppSubscribeMessageService;
import io.linfeng.love.user.entity.UserEntity;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Service("appSubscribeMessageService")
@AllArgsConstructor
@Slf4j
public class AppSubscribeMessageServiceImpl implements AppSubscribeMessageService {

    private SubscribeMessageExecutorFactory subscribeMessageExecutorFactory;

    @Override
    public void executeSubscribeMessageTask(UserEntity user, SubscribeMessageRequest request) {
        SubscribeMessageExecutor subscribeMessageExecutor = subscribeMessageExecutorFactory.createSubscribeMessageBuildService(request.getType());
        if(request.getChannel().equals("mp")){
            subscribeMessageExecutor.executeWxMpSubscribeMessage(user, request.getTmplId(), request.getLinkId());

        }
        if(request.getChannel().equals("ma")){
            subscribeMessageExecutor.executeWxMaSubscribeMessage(user, request.getTmplId(), request.getLinkId());
        }
    }
}