/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.auth.controller;


import io.linfeng.business.auth.request.SmsLoginRequest;
import io.linfeng.business.auth.request.WxLoginRequest;
import io.linfeng.business.auth.response.LoginResponse;
import io.linfeng.business.auth.service.AppAuthService;
import io.linfeng.business.auth.request.BindWxPhoneRequest;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.common.utils.ValidatorUtils;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 用户登录授权Api
 */
@RestController
@RequestMapping("/app/auth")
@Api(tags = "用户登录授权Api")
public class AppAuthController {

    private final AppAuthService appAuthService;

    public AppAuthController(AppAuthService appAuthService) {
        this.appAuthService = appAuthService;
    }

    /**
     * 微信小程序登录
     * @param request 登录请求
     * @return 登录结果
     */
    @PostMapping("/miniWxLogin")
    @ApiOperation("微信小程序登录")
    public Result<LoginResponse> miniWxLogin(@RequestBody WxLoginRequest request){

        ValidatorUtils.validateEntity(request);
        LoginResponse loginResponse = appAuthService.miniWxLogin(request);

        return new Result<LoginResponse>().ok(loginResponse);
    }

    /**
     * 微信公众号openid绑定
     * @param request 登录请求
     * @return 绑定结果
     */
    @Login
    @PostMapping("/bindMpOpenid")
    @ApiOperation("微信公众号openid绑定")
    public R mpWxLogin(@LoginUser UserEntity user,  @RequestBody WxLoginRequest request){

        appAuthService.bindMpOpenid(user, request);

        return R.ok();
    }

    /**
     * 手机验证码登录
     * @param request 登录请求
     * @return 登录结果
     */
    @PostMapping("/smsLogin")
    @ApiOperation("手机验证码登录")
    public Result<LoginResponse> smsLogin(@RequestBody SmsLoginRequest request){

        LoginResponse loginResponse = appAuthService.smsLogin(request);

        return new Result<LoginResponse>().ok(loginResponse);
    }

    /**
     * 微信手机号绑定（如果其他终端有账号，调用该接口会同步数据并重新生成token给前端）
     * @param user 登录用户
     * @param request 请求对象
     * @return 绑定结果
     */
    @Login
    @PostMapping("/bindWxPhone")
    @ApiOperation("微信手机号绑定")
    public Result<LoginResponse> bindWxPhone(@ApiIgnore @LoginUser UserEntity user, @RequestBody BindWxPhoneRequest request){
        LoginResponse loginResponse = appAuthService.bindWxPhone(user, request);
        return new Result<LoginResponse>().ok(loginResponse);
    }

}
