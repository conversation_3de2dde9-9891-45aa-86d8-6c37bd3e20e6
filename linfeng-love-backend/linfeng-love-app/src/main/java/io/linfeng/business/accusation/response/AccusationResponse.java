package io.linfeng.business.accusation.response;

import io.linfeng.business.accusation.request.AccusationMediaRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description="举报响应对象")
public class AccusationResponse {

    @ApiModelProperty(value = "举报id")
    private Integer id;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "标签")
    private Integer tag;

    @ApiModelProperty(value = "标签名称")
    private String tagName;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态")
    private String linkId;

    @ApiModelProperty(value = "平台响应")
    private String feedback;

    @ApiModelProperty(value = "举报时间")
    private String createTime;

    @ApiModelProperty(value = "附件列表")
    List<String> mediaList;

}
