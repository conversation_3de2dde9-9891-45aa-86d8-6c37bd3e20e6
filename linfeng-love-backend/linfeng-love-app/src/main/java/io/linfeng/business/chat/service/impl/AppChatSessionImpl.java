package io.linfeng.business.chat.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.linfeng.love.user.entity.UserLoginEntity;
import io.linfeng.love.user.enums.LoginStatus;
import io.linfeng.love.user.service.UserLoginService;
import io.linfeng.push.client.dto.SessionMessageData;
import io.linfeng.business.chat.response.ChatSessionResponse;
import io.linfeng.business.chat.service.AppChatSessionService;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.chat.enums.MessageType;
import io.linfeng.love.chat.dto.response.ChatSessionResponseDTO;
import io.linfeng.love.chat.entity.ChatSessionEntity;
import io.linfeng.love.chat.service.ChatSessionService;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.service.UserService;
import io.linfeng.push.client.dto.PushMessage;
import io.linfeng.push.client.enums.PushMessageType;
import io.linfeng.push.client.producer.MessageProducer;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("appChatSessionService")
public class AppChatSessionImpl implements AppChatSessionService {

    private final ChatSessionService chatSessionService;

    private final MessageProducer messageProducer;

    private final UserService userService;

    private final UserLoginService userLoginService;

    public AppChatSessionImpl(ChatSessionService chatSessionService, MessageProducer messageProducer, UserService userService, UserLoginService userLoginService) {
        this.chatSessionService = chatSessionService;
        this.messageProducer = messageProducer;
        this.userService = userService;
        this.userLoginService = userLoginService;
    }

    @Override
    public List<ChatSessionResponse> getAllSessionList(UserEntity user) {
        List<ChatSessionResponseDTO> responseDTOList = chatSessionService.getAllSessionList(user.getUid());
        List<ChatSessionResponse> responseList = ObjectMapperUtil.convert(responseDTOList, ChatSessionResponse.class);

        return responseList;
    }

    @Override
    public String createOrUpdateSession(ChatSessionEntity newChatSession) {

        //会话id存在直接更新
        if(newChatSession.getSessionId() != null){
            updateSession(newChatSession);
            return newChatSession.getSessionId();
        }

        //会话id不存在，去数据库里查是否存在
        LambdaQueryWrapper<ChatSessionEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ChatSessionEntity::getUid, newChatSession.getUid());
        lambdaQueryWrapper.eq(ChatSessionEntity::getFriendUid, newChatSession.getFriendUid());
        ChatSessionEntity oldChatSession = chatSessionService.getOne(lambdaQueryWrapper);
        if(oldChatSession != null){
            newChatSession.setSessionId(oldChatSession.getSessionId());
            updateSession(newChatSession);
            return newChatSession.getSessionId();
        }

        //数据库里不存在就需要新建，查看对方的会话还存不存在，存在就用对方的sessionId
        //在查询聊天记录时会筛选大于session创建时间的记录，所以新建session不会同步以前的聊天记录
        LambdaQueryWrapper<ChatSessionEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ChatSessionEntity::getFriendUid, newChatSession.getUid());
        wrapper.eq(ChatSessionEntity::getUid, newChatSession.getFriendUid());
        ChatSessionEntity friendChatSession = chatSessionService.getOne(wrapper);
        if(friendChatSession  == null){
            newChatSession.setSessionId(IdUtil.fastSimpleUUID());
        }else{
            newChatSession.setSessionId(friendChatSession.getSessionId());
        }
        createSession(newChatSession);
        return newChatSession.getSessionId();
    }

    private void createSession(ChatSessionEntity chatSession) {

        //创建我的会话
        chatSessionService.saveAndRefreshCache(chatSession);

        ///更新或创建朋友会话
        ChatSessionEntity friendChatSession = createOrUpdateFriendSession(chatSession);

        //推送朋友会话mq消息
        pushSessionMessage(friendChatSession);
    }

    private void updateSession(ChatSessionEntity chatSession) {
        //更新会话
        chatSessionService.updateAndRefreshCache(chatSession);

        //更新或创建朋友会话
        ChatSessionEntity friendChatSession = createOrUpdateFriendSession(chatSession);

        //推送朋友会话mq消息
        pushSessionMessage(friendChatSession);
    }

    @Override
    public ChatSessionResponse getSessionResponse(Integer uid, String friendOid) {
        UserEntity friendUserInfo = userService.getUserByOid(friendOid);
        ChatSessionResponseDTO chatSessionResponseDTO = chatSessionService.getSessionResponseDTO(uid, friendUserInfo.getUid());
        ChatSessionResponse chatSessionResponse = ObjectMapperUtil.convert(chatSessionResponseDTO, ChatSessionResponse.class);
        return chatSessionResponse;
    }

    @Override
    public void deleteSession(UserEntity user, String sessionId) {
        chatSessionService.deleteAndRefreshCache(user.getUid(), sessionId);
    }

    private void pushSessionMessage(ChatSessionEntity friendChatSession){
        UserEntity senderUserInfo = userService.getUserByUid(friendChatSession.getFriendUid());
        UserEntity receiverUserInfo = userService.getUserByUid(friendChatSession.getUid());
        PushMessage pushMessage = new PushMessage();
        pushMessage.setSenderUid(friendChatSession.getFriendUid());
        pushMessage.setReceiverUid(friendChatSession.getUid());
        pushMessage.setType(PushMessageType.SESSION.getValue());
        SessionMessageData sessionMessageData = ObjectMapperUtil.convert(friendChatSession, SessionMessageData.class);
        sessionMessageData.setFriendOid(senderUserInfo.getOid());
        sessionMessageData.setFriendAvatar(senderUserInfo.getAvatar());
        sessionMessageData.setFriendUserName(senderUserInfo.getUserName());
        sessionMessageData.setLastMessageTime(DateUtil.dateToStr(friendChatSession.getLastMessageTime(), DateUtil.DATE_FORMAT));

        if(friendChatSession.getUid() == friendChatSession.getLastMessageUid()){
            sessionMessageData.setLastMessageOid(receiverUserInfo.getOid());
        }else{
            sessionMessageData.setLastMessageOid(senderUserInfo.getOid());
        }

        LambdaQueryWrapper<UserLoginEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserLoginEntity::getUid, senderUserInfo.getUid());
        UserLoginEntity userLogin = userLoginService.getOne(wrapper);
        if(userLogin != null){
            sessionMessageData.setOnlineStatus(userLogin.getStatus());
            sessionMessageData.setLastOfflineTime(userLogin.getLastOfflineTime());
        }else{
            sessionMessageData.setOnlineStatus(LoginStatus.OFFLINE.getValue());
        }

        pushMessage.setData(sessionMessageData);

        //推送朋友会话mq消息
        messageProducer.sendMessage(Constant.DIRECT_EXCHANGE_NAME, Constant.PUSH_ROUTING_NAME, pushMessage);
    }

    private ChatSessionEntity createOrUpdateFriendSession(ChatSessionEntity chatSession){
        LambdaQueryWrapper<ChatSessionEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ChatSessionEntity::getUid, chatSession.getFriendUid());
        lambdaQueryWrapper.eq(ChatSessionEntity::getFriendUid, chatSession.getUid());
        ChatSessionEntity friendSession = chatSessionService.getOne(lambdaQueryWrapper);

        if(friendSession == null){
            friendSession = new ChatSessionEntity();
            friendSession.setSessionId(chatSession.getSessionId());
            friendSession.setUid(chatSession.getFriendUid());
            friendSession.setFriendUid(chatSession.getUid());
            friendSession.setLastMessageType(chatSession.getLastMessageType());
            friendSession.setLastMessageContent(chatSession.getLastMessageContent());
            friendSession.setLastMessageTime(chatSession.getLastMessageTime());
            friendSession.setLastMessageUid(chatSession.getLastMessageUid());
            friendSession.setUnRead(1);
            chatSessionService.saveAndRefreshCache(friendSession);
            return friendSession;
        }

        friendSession.setLastMessageType(chatSession.getLastMessageType());
        friendSession.setLastMessageContent(chatSession.getLastMessageContent());
        friendSession.setLastMessageTime(chatSession.getLastMessageTime());
        friendSession.setLastMessageUid(chatSession.getLastMessageUid());
        if(chatSession.getLastMessageType() == MessageType.RECALL.getValue()){
            if(friendSession.getUnRead() != 0){
                friendSession.setUnRead(friendSession.getUnRead() - 1);
            }
        }else{
            friendSession.setUnRead(friendSession.getUnRead() + 1);
        }
        friendSession.setUpdateTime(DateUtil.nowDateTime());
        chatSessionService.updateAndRefreshCache(friendSession);
        return friendSession;
    }

}