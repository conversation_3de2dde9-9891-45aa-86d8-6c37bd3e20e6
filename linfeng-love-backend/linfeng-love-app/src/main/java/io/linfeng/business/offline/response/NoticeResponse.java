package io.linfeng.business.offline.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="线下活动公告响应对象")
public class NoticeResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 公告id
	 */
	@ApiModelProperty(value = "公告id")
	private Integer id;

	/**
	 * 封面图片
	 */
	@ApiModelProperty(value = "封面图片")
	private String coverUrl;

	/**
	 * 正文
	 */
	@ApiModelProperty(value = "正文")
	private String content;

}
