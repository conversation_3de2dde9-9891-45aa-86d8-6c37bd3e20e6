package io.linfeng.business.identity.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(description = "工作认证请求对象")
public class JobCheckRequest {

    @ApiModelProperty(value = "证明类型",required = true)
    @NotBlank(message="证明类型不能为空")
    private String type;

    @ApiModelProperty(value = "工作证明照片",required = true)
    @NotBlank(message="工作证明照片不能为空")
    private String image;


}
