/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.petal.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.petal.request.ExchangePetalRequest;
import io.linfeng.business.petal.request.PetalRecordRequest;
import io.linfeng.business.petal.request.PetalUnlockRequest;
import io.linfeng.business.petal.response.PetalRecordResponse;
import io.linfeng.love.user.entity.UserEntity;

import java.util.List;

/**
 * APP花瓣服务
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
public interface AppPetalService {

    /**
     * 花瓣充值
     * @param uid 客户id
     * @param optionId 方案id
     */
    void petalRecharge(Integer uid, Integer optionId);

    /**
     * 花瓣解锁嘉宾
     * @param user 登录用户
     * @param request 解锁请求
     * @return 解锁结果
     */
    void unlock(UserEntity user, PetalUnlockRequest request);

    /**
     * 增加花瓣
     * @param user 用户
     * @param petalForever 永久花瓣数量
     * @param petalLimit 临时花瓣数量
     * @param subType 增加子类型
     */
    void increasePetal(UserEntity user, Integer petalForever, Integer petalLimit, Integer subType);

    /**
     * 扣除花瓣
     * @param user 用户
     * @param petalNum 花瓣数量
     * @param subType 扣除子类型
     * @return 临时花瓣数量
     */
    Integer deductPetal(UserEntity user, Integer petalNum, Integer subType);

    /**
     * 扣除花瓣(永久)
     * @param user 用户
     * @param petalNum 花瓣数量
     * @param subType 扣除子类型
     */
    void deductPetalForever(UserEntity user, Integer petalNum, Integer subType);


    /**
     * 兑换花瓣
     * @param user 登录用户
     * @param request 兑换请求
     */
    void exchangePetal(UserEntity user, ExchangePetalRequest request);

    /**
     * 查询用户花瓣记录
     * @param user 登录用户
     * @param request 查询请求
     * @return 花瓣记录
     */
    IPage<PetalRecordResponse> getRecordList(UserEntity user, PetalRecordRequest request);
}

