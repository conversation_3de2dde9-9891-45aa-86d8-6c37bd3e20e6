package io.linfeng.business.accusation.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.business.accusation.request.AccusationRequest;
import io.linfeng.business.accusation.response.AccusationResponse;
import io.linfeng.business.accusation.service.AppAccusationService;
import io.linfeng.common.enums.MediaType;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.accusation.dto.response.AccusationResponseDTO;
import io.linfeng.love.accusation.entity.AccusationEntity;
import io.linfeng.love.accusation.entity.AccusationMediaEntity;
import io.linfeng.love.accusation.service.AccusationMediaService;
import io.linfeng.love.accusation.service.AccusationService;
import io.linfeng.love.common.enums.CommonStatus;
import io.linfeng.love.config.service.DictItemService;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.service.UserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service("appAccusationService")
public class AppAccusationServiceImpl implements AppAccusationService {

    private final AccusationService accusationService;

    private final AccusationMediaService accusationMediaService;

    private final UserService userService;

    private final DictItemService dictItemService;

    public AppAccusationServiceImpl(AccusationService accusationService, AccusationMediaService accusationMediaService, UserService userService, DictItemService dictItemService) {
        this.accusationService = accusationService;
        this.accusationMediaService = accusationMediaService;
        this.userService = userService;
        this.dictItemService = dictItemService;
    }

    @Override
    @Transactional
    public Integer submit(UserEntity user, AccusationRequest request) {

        AccusationEntity accusation = new AccusationEntity();
        accusation.setType(request.getType());
        accusation.setUid(user.getUid());
        accusation.setContent(request.getContent());
        accusation.setStatus(CommonStatus.CHECKING.getValue());
        accusation.setCreateTime(DateUtil.nowDateTime());
        accusation.setTag(request.getTag());

        accusation.setLinkId(request.getLinkId());
        accusationService.save(accusation);

        if(request.getMediaList() != null && request.getMediaList().size() != 0){
            List<AccusationMediaEntity> accusationMediaList = new ArrayList<>();

            request.getMediaList().forEach(mediaRequest -> {
                AccusationMediaEntity accusationMedia  = new AccusationMediaEntity();
                accusationMedia.setAccusationId(accusation.getId());
                accusationMedia.setMediaName(IdUtil.fastSimpleUUID());
                accusationMedia.setMediaType(MediaType.IMAGE.getValue());
                accusationMedia.setUrl(mediaRequest.getUrl());
                accusationMedia.setCreateTime(DateUtil.nowDateTime());
                accusationMediaList.add(accusationMedia);
            });
            accusationMediaService.saveBatch(accusationMediaList);
        }
        return accusation.getId();

    }

    @Override
    public List<AccusationResponse> getAccusationList(UserEntity user, Integer status) {
        List<AccusationResponseDTO> responseDTOList = accusationService.getAccusationList(user.getUid(), status);
        List<AccusationResponse> responseList = ObjectMapperUtil.convert(responseDTOList, AccusationResponse.class);
        responseList.forEach(response -> {
            response.setTagName(dictItemService.getItemName(Constant.DT_ACCUSATION_TAG, response.getTag()));
        });
        return responseList;
    }

    @Override
    public AccusationResponse getAccusationDetail(UserEntity user, Integer id) {
        AccusationEntity accusation = accusationService.getById(id);
        AccusationResponse response = ObjectMapperUtil.convert(accusation, AccusationResponse.class);

        LambdaQueryWrapper<AccusationMediaEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccusationMediaEntity::getAccusationId, id);
        List<AccusationMediaEntity> mediaList = accusationMediaService.list(wrapper);
        List<String> urlList = new ArrayList<>();
        mediaList.forEach(media -> {
            urlList.add(media.getUrl());
        });
        response.setTagName(dictItemService.getItemName(Constant.DT_ACCUSATION_TAG, response.getTag()));
        response.setCreateTime(DateUtil.dateToStr(accusation.getCreateTime(), DateUtil.DATE_FORMAT));
        response.setMediaList(urlList);
        return response;
    }
}