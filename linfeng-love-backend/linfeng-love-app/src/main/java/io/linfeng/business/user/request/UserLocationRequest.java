
package io.linfeng.business.user.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 用户地理位置更新请求对象
 *
 */
@Data
@ApiModel(description = "用户地理位置更新请求对象")
public class UserLocationRequest {

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private String latitude;
    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private String longitude;

}
