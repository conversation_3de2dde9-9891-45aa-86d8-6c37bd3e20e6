package io.linfeng.business.moment.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="话题标题请求对象")
public class MomentTopicRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 话题id
	 */
	@ApiModelProperty(value = "话题id")
	private Integer id;
	/**
	 * 话题标题
	 */
	@ApiModelProperty(value = "话题标题")
	private String title;

}
