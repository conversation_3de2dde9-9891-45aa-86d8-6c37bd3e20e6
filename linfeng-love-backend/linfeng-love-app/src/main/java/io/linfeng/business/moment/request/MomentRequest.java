package io.linfeng.business.moment.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(description="动态发布请求对象")
public class MomentRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * momentId
	 */
	@ApiModelProperty(value = "momentId")
	private Integer momentId;

	/**
	 * 内容
	 */
	@ApiModelProperty(value = "内容")
	private String content;
	/**
	 * 附件类型
	 */
	@ApiModelProperty(value = "附件类型")
	private Integer mediaType;

	/**
	 * 附件列表
	 */
	@ApiModelProperty(value = "附件列表")
	private List<MomentMediaRequest> mediaList;

	/**
	 * 纬度
	 */
	@ApiModelProperty(value = "纬度")
	private String latitude;

	/**
	 * 经度
	 */
	@ApiModelProperty(value = "经度")
	private String longitude;

	/**
	 * 所在地址
	 */
	@ApiModelProperty(value = "所在地址")
	private String address;
	/**
	 * 地址详情
	 */
	@ApiModelProperty(value = "地址详情")
	private String addressDetail;

	/**
	 * 是否匿名
	 */
	@ApiModelProperty(value = "是否匿名")
	private Boolean anonymous;

	/**
	 * 是否私密
	 */
	@ApiModelProperty(value = "是否私密")
	private Boolean privacy;

	/**
	 * 话题列表
	 */
	@ApiModelProperty(value = "话题列表")
	private List<MomentTopicRequest> topicList;

	/**
	 * 操作类型
	 */
	@ApiModelProperty(value = "操作类型")
	private Integer operatorType;

}
