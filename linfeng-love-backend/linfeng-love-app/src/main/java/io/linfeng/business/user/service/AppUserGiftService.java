package io.linfeng.business.user.service;

import io.linfeng.business.user.request.UserIntroRequest;
import io.linfeng.business.user.response.UserGiftResponse;
import io.linfeng.business.user.response.UserIntroResponse;
import io.linfeng.love.user.entity.UserEntity;

import java.util.List;

/**
 * 用户礼物业务服务
 *
 * <AUTHOR>
 * @date 2023-09-06 16:22:14
 */
public interface AppUserGiftService {

    /**
     * 获取用户礼物列表
     * @param user 登录用户
     * @return 用户介绍详情
     */
    List<UserGiftResponse> getUserGiftList(UserEntity user);

}

