package io.linfeng.business.petal.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.linfeng.business.petal.request.ExchangePetalRequest;
import io.linfeng.business.petal.request.PetalRecordRequest;
import io.linfeng.business.petal.request.PetalUnlockRequest;
import io.linfeng.business.petal.response.PetalRecordResponse;
import io.linfeng.business.petal.service.AppPetalService;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.ErrorMessage;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.config.entity.ConfigExchangePetalEntity;
import io.linfeng.love.config.service.ConfigBusinessService;
import io.linfeng.love.config.service.ConfigExchangePetalService;
import io.linfeng.love.petal.entity.PetalOptionEntity;
import io.linfeng.love.petal.entity.PetalRecordEntity;
import io.linfeng.love.petal.entity.PetalUnlockEntity;
import io.linfeng.love.petal.enums.PetalRecordSubType;
import io.linfeng.love.petal.enums.PetalRecordType;
import io.linfeng.love.petal.service.PetalOptionService;
import io.linfeng.love.petal.service.PetalRecordService;
import io.linfeng.love.petal.service.PetalUnlockService;
import io.linfeng.love.user.entity.UserAccountEntity;
import io.linfeng.love.user.entity.UserAccountRecordEntity;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.enums.AccountRecordSubType;
import io.linfeng.love.user.enums.AccountRecordType;
import io.linfeng.love.user.service.UserAccountRecordService;
import io.linfeng.love.user.service.UserAccountService;
import io.linfeng.love.user.service.UserService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service("appPetalService")
@AllArgsConstructor
public class AppPetalServiceImpl implements AppPetalService {

    private final PetalOptionService petalOptionService;

    private final UserService userService;

    private final ConfigBusinessService configBusinessService;

    private final PetalUnlockService petalUnlockService;

    private final PetalRecordService petalRecordService;

    private final UserAccountService userAccountService;

    private final ConfigExchangePetalService configExchangePetalService;

    private final UserAccountRecordService userAccountRecordService;


    @Override
    @Transactional
    public void petalRecharge(Integer uid, Integer optionId) {

        UserEntity user = userService.getUserByUid(uid);
        PetalOptionEntity petalOption = petalOptionService.getById(optionId);
        increasePetal(user, petalOption.getAmount(), 0, PetalRecordSubType.CASH.getValue());

    }

    @Override
    @Transactional
    public void unlock(UserEntity user, PetalUnlockRequest request) {

        UserEntity guestUser = userService.getUserByOid(request.getOid());

        LambdaQueryWrapper<PetalUnlockEntity> wrapper =new LambdaQueryWrapper<>();
        wrapper.eq(PetalUnlockEntity::getUid,user.getUid());
        wrapper.eq(PetalUnlockEntity::getGuestUid,guestUser.getUid());
        PetalUnlockEntity petalUnlockEntity = petalUnlockService.getOne(wrapper);
        if(petalUnlockEntity != null){
            throw new LinfengException(ErrorMessage.BUSINESS_PETAL_UNLOCK_REPEAT);
        }

        int petalUnlock = Integer.parseInt(configBusinessService.getValue(Constant.PETAL_UNLOCK));
        //更新用户额度
        deductPetal(user, petalUnlock, PetalRecordSubType.UNLOCK.getValue());

        //保存解锁记录
        petalUnlockEntity = new PetalUnlockEntity();
        petalUnlockEntity.setUid(user.getUid());
        petalUnlockEntity.setGuestUid(guestUser.getUid());
        petalUnlockEntity.setPetalAmount(petalUnlock);
        petalUnlockEntity.setCreateTime(DateUtil.nowDateTime());
        petalUnlockService.save(petalUnlockEntity);

    }

    @Override
    public void increasePetal(UserEntity user, Integer petalForever, Integer petalLimit, Integer subType) {

        user.setPetalForever(user.getPetalForever() + petalForever);
        user.setPetalLimit(user.getPetalLimit() + petalLimit);
        user.setUpdateTime(DateUtil.nowDateTime());
        userService.updateAndDeleteCache(user);

        //保存花瓣记录
        PetalRecordEntity petalRecordEntity = new PetalRecordEntity();
        petalRecordEntity.setUid(user.getUid());
        petalRecordEntity.setType(PetalRecordType.RECHARGE.getValue());
        petalRecordEntity.setSubType(subType);
        petalRecordEntity.setAmountForever(petalForever);
        petalRecordEntity.setAmountLimit(petalLimit);
        petalRecordEntity.setCreateTime(DateUtil.nowDateTime());
        petalRecordService.save(petalRecordEntity);
    }

    @Override
    public Integer deductPetal(UserEntity user, Integer petalNum, Integer subType) {
        int petalLimit = user.getPetalLimit();
        int petalForever = user.getPetalForever();
        int petalBalance = petalLimit + petalForever;
        int foreverAmount = 0;
        int limitAmount = petalNum;

        if(petalBalance - petalNum < 0){
            //总余额不够就引导至充值页面，返回引导错误码
            throw new LinfengException(ErrorMessage.BUSINESS_PETAL_NOT_ENOUGH, ErrorMessage.COMMON_GUIDE_CODE);
        }

        //先扣临时花瓣，再扣永久的
        if(petalLimit - petalNum < 0){
            foreverAmount = petalNum - petalLimit;
            limitAmount = petalLimit;
        }
        user.setPetalForever(petalForever - foreverAmount);
        user.setPetalLimit(petalLimit - limitAmount);
        user.setUpdateTime(DateUtil.nowDateTime());
        userService.updateAndDeleteCache(user);

        //保存花瓣记录
        PetalRecordEntity petalRecordEntity = new PetalRecordEntity();
        petalRecordEntity.setUid(user.getUid());
        petalRecordEntity.setType(PetalRecordType.CONSUME.getValue());
        petalRecordEntity.setSubType(subType);
        petalRecordEntity.setAmountForever(foreverAmount);
        petalRecordEntity.setAmountLimit(limitAmount);
        petalRecordEntity.setCreateTime(DateUtil.nowDateTime());
        petalRecordService.save(petalRecordEntity);

        return limitAmount;

    }

    @Override
    public void deductPetalForever(UserEntity user, Integer petalNum, Integer subType) {
        int petalForever = user.getPetalForever();

        if(petalForever - petalNum < 0){
            //总余额不够就引导至充值页面，返回引导错误码
            throw new LinfengException(ErrorMessage.BUSINESS_PETAL_NOT_ENOUGH, ErrorMessage.COMMON_GUIDE_CODE);
        }

        user.setPetalForever(petalForever - petalNum);
        user.setUpdateTime(DateUtil.nowDateTime());
        userService.updateAndDeleteCache(user);

        //保存花瓣记录
        PetalRecordEntity petalRecordEntity = new PetalRecordEntity();
        petalRecordEntity.setUid(user.getUid());
        petalRecordEntity.setType(PetalRecordType.CONSUME.getValue());
        petalRecordEntity.setSubType(subType);
        petalRecordEntity.setAmountForever(petalNum);
        petalRecordEntity.setAmountLimit(0);
        petalRecordEntity.setCreateTime(DateUtil.nowDateTime());
        petalRecordService.save(petalRecordEntity);
    }

    @Override
    public void exchangePetal(UserEntity user, ExchangePetalRequest request) {

        ConfigExchangePetalEntity configExchangePetal = configExchangePetalService.getById(request.getId());

        LambdaQueryWrapper<UserAccountEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserAccountEntity::getUid, user.getUid());
        UserAccountEntity userAccount = userAccountService.getOne(wrapper);
        if(userAccount.getUnCashedAmount().compareTo(configExchangePetal.getAmount()) < 0){
            throw new LinfengException("您的账户余额不足，无法兑换");
        }

        //更新账户信息
        userAccount.setUnCashedAmount(userAccount.getUnCashedAmount().subtract(configExchangePetal.getAmount()));
        userAccount.setPetalAmount(userAccount.getPetalAmount().add(configExchangePetal.getAmount()));
        userAccount.setLastPetalTime(DateUtil.nowDateTime());
        userAccount.setUpdateTime(DateUtil.nowDateTime());
        userAccountService.updateById(userAccount);

        //更新用户信息
        user.setPetalForever(user.getPetalForever() + configExchangePetal.getPetal());
        user.setUpdateTime(DateUtil.nowDateTime());
        userService.updateAndDeleteCache(user);

        //保存账户记录
        UserAccountRecordEntity userAccountRecord = new UserAccountRecordEntity();
        userAccountRecord.setUid(user.getUid());
        userAccountRecord.setAmount(configExchangePetal.getAmount());
        userAccountRecord.setType(AccountRecordType.OUT.getValue());
        userAccountRecord.setSubType(AccountRecordSubType.PETAL.getValue());
        userAccountRecord.setRemark(configExchangePetal.getPetal() + "");
        userAccountRecord.setCreateTime(DateUtil.nowDateTime());
        userAccountRecordService.save(userAccountRecord);

        //保存花瓣记录
        PetalRecordEntity petalRecordEntity = new PetalRecordEntity();
        petalRecordEntity.setUid(user.getUid());
        petalRecordEntity.setType(PetalRecordType.RECHARGE.getValue());
        petalRecordEntity.setSubType(PetalRecordSubType.ACCOUNT_EXCHANGE.getValue());
        petalRecordEntity.setAmountForever(configExchangePetal.getPetal());
        petalRecordEntity.setAmountLimit(0);
        petalRecordEntity.setCreateTime(DateUtil.nowDateTime());
        petalRecordService.save(petalRecordEntity);

    }

    @Override
    public IPage<PetalRecordResponse> getRecordList(UserEntity user, PetalRecordRequest request) {
        IPage<PetalRecordEntity> dtoPage = new Page<>(request.getPageNum(),request.getPageSize());
        LambdaQueryWrapper<PetalRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PetalRecordEntity::getUid, user.getUid());
        wrapper.orderByDesc(PetalRecordEntity::getCreateTime);
        if(request.getType() != 0){
            wrapper.eq(PetalRecordEntity::getType, request.getType());
        }
        if(request.getSubType() != 0){
            wrapper.eq(PetalRecordEntity::getSubType, request.getSubType());
        }
        IPage<PetalRecordEntity> petalRecordEntityList = petalRecordService.page(dtoPage, wrapper);
        IPage<PetalRecordResponse> responseList = ObjectMapperUtil.convert(petalRecordEntityList, PetalRecordResponse.class);
        return responseList;
    }

}