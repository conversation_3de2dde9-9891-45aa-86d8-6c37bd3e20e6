package io.linfeng.business.subscribe.core;

import io.linfeng.love.user.entity.UserEntity;

/**
 * 
 * 订阅消息构造服务
 * 
 * <AUTHOR>
 *
 */
public interface SubscribeMessageExecutor {

    /**
     * 执行微信小程序订阅消息
     */
    void executeWxMaSubscribeMessage(UserEntity user, String tmplId, String linkId);

    /**
     * 执行微信公众号订阅消息
     */
    void executeWxMpSubscribeMessage(UserEntity user, String tmplId, String linkId);

}
