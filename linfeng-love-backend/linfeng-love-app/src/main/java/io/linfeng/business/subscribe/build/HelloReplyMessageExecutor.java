package io.linfeng.business.subscribe.build;

import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.business.subscribe.core.AbstractSubscribeMessageExecutor;
import io.linfeng.business.subscribe.dto.WxMaSubscribeMessageDTO;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.love.message.entity.SubscribeMessageEntity;
import io.linfeng.love.message.enums.SendStatus;
import io.linfeng.love.message.enums.SubscribeChannel;
import io.linfeng.love.message.enums.SubscribeStatus;
import io.linfeng.love.message.enums.SubscribeType;
import io.linfeng.love.message.service.SubscribeMessageService;
import io.linfeng.love.offline.entity.OfflinePartnerEntity;
import io.linfeng.love.offline.service.OfflinePartnerService;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.service.UserService;
import me.chanjar.weixin.mp.bean.subscribe.WxMpSubscribeMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;


@Service("helloReplyMessageExecutor")
public class HelloReplyMessageExecutor extends AbstractSubscribeMessageExecutor {

    @Autowired
    private UserService userService;

    @Override
    public WxMaSubscribeMessageDTO buildWxMaSubscribeMessage(UserEntity user, String tmplId, String linkId) {
        UserEntity guestUser = userService.getUserByOid(linkId);
        WxMaSubscribeMessageDTO wxMaSubscribeMessageDTO = new WxMaSubscribeMessageDTO();
        WxMaSubscribeMessage wxMaSubscribeMessage = new WxMaSubscribeMessage();
        wxMaSubscribeMessage.setTemplateId(tmplId);
        wxMaSubscribeMessage.setToUser(user.getOpenid());
        wxMaSubscribeMessage.setPage(getPageUrl(linkId));
        List<WxMaSubscribeMessage.MsgData> msgDataList = new ArrayList<>();
        WxMaSubscribeMessage.MsgData msgData1 = new WxMaSubscribeMessage.MsgData();
        msgData1.setName("thing7");
        if(guestUser.getUserName().length() > 6){
            msgData1.setValue(guestUser.getUserName().substring(0, 6) + "*");
        }else{
            msgData1.setValue(guestUser.getUserName());
        }
        WxMaSubscribeMessage.MsgData msgData2 = new WxMaSubscribeMessage.MsgData();
        msgData2.setName("thing15");
        msgData2.setValue("通过了你的打招呼请求");
        msgDataList.add(msgData1);
        msgDataList.add(msgData2);
        wxMaSubscribeMessage.setData(msgDataList);

        saveSubscribeMessage(user, tmplId, linkId, JSON.toJSONString(wxMaSubscribeMessage));

        wxMaSubscribeMessageDTO.setImmediately(false);
        wxMaSubscribeMessageDTO.setWxMaSubscribeMessage(wxMaSubscribeMessage);
        return wxMaSubscribeMessageDTO;
    }

    @Override
    public WxMpSubscribeMessage buildWxMpSubscribeMessage(UserEntity user, String tmplId, String linkId) {
        WxMpSubscribeMessage wxMpSubscribeMessage = new WxMpSubscribeMessage();
        return wxMpSubscribeMessage;
    }

    @Override
    public String getPageUrl(String linkId) {
        return "/pages/im/hello-list";
    }

    @Override
    public String getMessageType() {
        return "helloReply";
    }
}