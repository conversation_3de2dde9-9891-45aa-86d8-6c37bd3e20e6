/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.offline.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.offline.request.*;
import io.linfeng.business.offline.response.MemberResponse;
import io.linfeng.business.offline.response.PartnerDetailResponse;
import io.linfeng.business.offline.response.PartnerSimpleResponse;
import io.linfeng.business.pay.response.PrePayResultResponse;
import io.linfeng.love.user.entity.UserEntity;

import java.util.List;

/**
 * 线下找搭子
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
public interface AppOfflinePartnerService {

    /**
     * 创建搭子活动
     * @param user
     * @param request
     */
    void createPartner(UserEntity user, PartnerCreateRequest request);

    /**
     * 搭子活动列表
     * @param user 登录用户
     * @param request 查询请求
     * @return 搭子活动列表
     */
    IPage<PartnerSimpleResponse> getOfflinePartnerList(UserEntity user, PartnerQueryRequest request);

    /**
     * 获取我的搭子列表
     * @param user 登录用户
     * @param request 查询请求
     * @return 我的搭子列表
     */
    IPage<PartnerSimpleResponse> getMyPartnerList(UserEntity user, PartnerQueryRequest request);

    /**
     * 获取搭子详情
     * @param user 登录请求
     * @param id 搭子ID
     * @return 搭子详情
     */
    PartnerDetailResponse getOfflinePartnerDetail(UserEntity user, Integer id);

    /**
     * 加入搭子活动
     * @param user 登录用户
     * @param request 加入请求
     */
    void joinPartner(UserEntity user, PartnerJoinRequest request);

    /**
     * 退出搭子活动
     * @param user 登录用户
     * @param request 退出请求
     */
    void exitPartner(UserEntity user, PartnerExitRequest request);

    /**
     * 获取搭子成员列表
     * @param user 登录用户
     * @param partnerId 搭子id
     * @return 搭子成员列表
     */
    List<MemberResponse> getMmemberList(UserEntity user, Integer partnerId);

    /**
     * 结束搭子活动
     * @param user 登录用户
     * @param request 结束搭子请求
     */
    void overPartner(UserEntity user, PartnerOverRequest request);

    /**
     * 删除搭子活动
     * @param user 登录用户
     * @param request 删除搭子请求
     */
    void deletePartner(UserEntity user, PartnerDeleteRequest request);
}

