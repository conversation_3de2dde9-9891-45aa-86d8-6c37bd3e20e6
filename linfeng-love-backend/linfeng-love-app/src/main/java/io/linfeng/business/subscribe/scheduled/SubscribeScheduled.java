package io.linfeng.business.subscribe.scheduled;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.love.message.entity.SubscribeMessageEntity;
import io.linfeng.love.message.enums.SendStatus;
import io.linfeng.love.message.enums.SubscribeChannel;
import io.linfeng.love.message.enums.SubscribeStatus;
import io.linfeng.love.message.service.SubscribeMessageService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.List;


/**
 * 消息订阅定时任务
 */
@Component
@Slf4j
public class SubscribeScheduled {

    @Autowired
    private SubscribeMessageService subscribeMessageService;

    @Autowired
    private WxMpService wxMpService;

    @Autowired
    private WxMaService wxMaService;

    private static final int DELAY_IN_MILLISECONDS = 1000 * 60;

    @Scheduled(fixedRate = DELAY_IN_MILLISECONDS)
    public void checkAndSendMessage() {
        LambdaQueryWrapper<SubscribeMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SubscribeMessageEntity::getSubscribeStatus, SubscribeStatus.YES.getValue());
        wrapper.eq(SubscribeMessageEntity::getSendStatus, SendStatus.NO.getValue());
        wrapper.lt(SubscribeMessageEntity::getSendTime, DateUtil.nowDateTime());
        List<SubscribeMessageEntity> subscribeMessageEntityList = subscribeMessageService.list(wrapper);
        log.info("消息订阅推送开始，本次推送[{}]条数据", subscribeMessageEntityList.size());
        subscribeMessageEntityList.forEach(subscribeMessageEntity -> {
            if(subscribeMessageEntity.getChannel().equals(SubscribeChannel.MA.getValue())){
                String jsonMessage = subscribeMessageEntity.getContent();
                WxMaSubscribeMessage wxMaSubscribeMessage = JSON.parseObject(jsonMessage, WxMaSubscribeMessage.class);
                try {
                    wxMaService.getSubscribeService().sendSubscribeMsg(wxMaSubscribeMessage);
                    subscribeMessageEntity.setSendStatus(SendStatus.YES.getValue());
                    subscribeMessageEntity.setUpdateTime(DateUtil.nowDateTime());
                    subscribeMessageService.updateById(subscribeMessageEntity);
                } catch (WxErrorException e) {
                    log.error("微信小程序消息订阅异常", e);
                    throw new LinfengException("消息订阅异常");
                }
            }
        });
    }

}
