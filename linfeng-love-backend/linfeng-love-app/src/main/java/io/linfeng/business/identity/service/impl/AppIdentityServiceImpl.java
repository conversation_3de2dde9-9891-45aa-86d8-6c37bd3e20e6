package io.linfeng.business.identity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.gson.Gson;
import io.linfeng.business.identity.request.EducationCheckRequest;
import io.linfeng.business.identity.request.JobCheckRequest;
import io.linfeng.business.identity.request.RealNameRequest;
import io.linfeng.business.identity.service.AppIdentityService;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.FileUtil;
import io.linfeng.love.common.enums.CommonStatus;
import io.linfeng.love.config.service.ConfigSystemService;
import io.linfeng.love.executor.MissionEventExecutor;
import io.linfeng.love.identity.entity.IdentityEducationEntity;
import io.linfeng.love.identity.entity.IdentityJobEntity;
import io.linfeng.love.identity.service.IdentityEducationService;
import io.linfeng.love.identity.service.IdentityJobService;
import io.linfeng.love.mission.enums.MissionTarget;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.enums.RealNameStatus;
import io.linfeng.love.user.service.UserService;
import io.linfeng.transport.cloud.tencent.TencentCloudFaceTransport;
import io.linfeng.transport.cloud.tencent.TencentCloudRealNameTransport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;


@Service("appIdentityService")
@Slf4j
public class AppIdentityServiceImpl implements AppIdentityService {

    private final static Gson gson = new Gson();

    private final ConfigSystemService configSystemService;

    private final UserService userService;

    private final IdentityEducationService identityEducationService;

    private final IdentityJobService identityJobService;

    private final MissionEventExecutor missionEventExecutor;

    private final TencentCloudRealNameTransport tencentCloudRealNameTransport;

    private final TencentCloudFaceTransport tencentCloudFaceTransport;


    public AppIdentityServiceImpl(ConfigSystemService configSystemService, UserService userService, IdentityEducationService identityEducationService, IdentityJobService identityJobService, MissionEventExecutor missionEventExecutor, TencentCloudRealNameTransport tencentCloudRealNameTransport, TencentCloudFaceTransport tencentCloudFaceTransport) {
        this.configSystemService = configSystemService;
        this.userService = userService;
        this.identityEducationService = identityEducationService;
        this.identityJobService = identityJobService;
        this.missionEventExecutor = missionEventExecutor;
        this.tencentCloudRealNameTransport = tencentCloudRealNameTransport;
        this.tencentCloudFaceTransport = tencentCloudFaceTransport;
    }


    @Override
    public void realNameCheck(UserEntity user, RealNameRequest request) {

        //人脸对比
        tencentCloudFaceTransport.compareFace(request.getImageUrl(), user.getAvatar());

        //三要素验证
        LambdaQueryWrapper<UserEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(UserEntity::getIdCard, request.getIdCard());
        UserEntity userEntity = userService.getOne(lambdaQueryWrapper);
        if(userEntity != null){
            throw new LinfengException("您的身份证已绑定其他账号");
        }
        tencentCloudRealNameTransport.phoneVerification(request.getRealName(), request.getIdCard(), user.getMobile());
        //人脸核身
        try{
            tencentCloudRealNameTransport.imageRecognition(request.getRealName(), request.getIdCard(), FileUtil.urlToBase64(request.getImageUrl()));
        }catch (IOException e){
            log.error("人脸核身异常", e);
            throw  new LinfengException("人脸核身异常");
        }

        user.setRealName(request.getRealName());
        user.setIdCard(request.getIdCard());
        user.setRealNameStatus(RealNameStatus.STRONG.getValue());
        user.setRealNameImage(request.getImageUrl());
        user.setUpdateTime(DateUtil.nowDateTime());
        userService.updateAndDeleteCache(user);
        missionEventExecutor.updateUserMissionTarget(user.getUid(), MissionTarget.IDENTITY_REAL_NAME, 1);


    }

    @Override
    public void educationCheck(UserEntity user, EducationCheckRequest request) {
        LambdaQueryWrapper<IdentityEducationEntity> wrapper =new LambdaQueryWrapper<>();
        wrapper.eq(IdentityEducationEntity::getUid, user.getUid());
        IdentityEducationEntity identityEducation = identityEducationService.getOne(wrapper);
        if(identityEducation == null){
            identityEducation = new IdentityEducationEntity();
            identityEducation.setUid(user.getUid());
            identityEducation.setIdCardImage(request.getIdCardImage());
            identityEducation.setEducationImageOne(request.getEducationImageOne());
            identityEducation.setEducationImageTwo(request.getEducationImageTwo());
            identityEducation.setCreateTime(DateUtil.nowDateTime());
            identityEducationService.save(identityEducation);
        }else{
            identityEducation.setIdCardImage(request.getIdCardImage());
            identityEducation.setEducationImageOne(request.getEducationImageOne());
            identityEducation.setEducationImageTwo(request.getEducationImageTwo());
            identityEducation.setUpdateTime(DateUtil.nowDateTime());
            identityEducationService.updateById(identityEducation);
        }

        user.setEducationStatus(CommonStatus.CHECKING.getValue());
        userService.updateAndDeleteCache(user);

    }

    @Override
    public void jobCheck(UserEntity user, JobCheckRequest request) {
        LambdaQueryWrapper<IdentityJobEntity> wrapper =new LambdaQueryWrapper<>();
        wrapper.eq(IdentityJobEntity::getUid, user.getUid());
        IdentityJobEntity identityJobEntity = identityJobService.getOne(wrapper);
        if(identityJobEntity == null){
            identityJobEntity = new IdentityJobEntity();
            identityJobEntity.setUid(user.getUid());
            identityJobEntity.setType(request.getType());
            identityJobEntity.setImage(request.getImage());
            identityJobEntity.setCreateTime(DateUtil.nowDateTime());
            identityJobService.save(identityJobEntity);
        }else{
            identityJobEntity.setType(request.getType());
            identityJobEntity.setImage(request.getImage());
            identityJobEntity.setUpdateTime(DateUtil.nowDateTime());
            identityJobService.updateById(identityJobEntity);
        }
        user.setJobStatus(CommonStatus.CHECKING.getValue());
        userService.updateAndDeleteCache(user);
    }

}