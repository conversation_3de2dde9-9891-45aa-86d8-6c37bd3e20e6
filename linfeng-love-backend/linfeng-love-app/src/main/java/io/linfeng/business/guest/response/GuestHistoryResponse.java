package io.linfeng.business.guest.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(description="历史嘉宾信息响应对象")
public class GuestHistoryResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 查看数量
	 */
	@ApiModelProperty(value = "查看数量")
	private int recommendCount;
	/**
	 * 喜欢数量
	 */
	@ApiModelProperty(value = "喜欢数量")
	private int likeCount;
	/**
	 * 不喜欢数量
	 */
	@ApiModelProperty(value = "不喜欢数量")
	private int dislikeCount;

	/**
	 * 推荐嘉宾列表
	 */
	@ApiModelProperty(value = "推荐嘉宾列表")
	private List<GuestSimpleResponse> guestUserList;


}
