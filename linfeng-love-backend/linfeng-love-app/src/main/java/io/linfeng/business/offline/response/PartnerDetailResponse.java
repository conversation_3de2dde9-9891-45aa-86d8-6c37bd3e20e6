package io.linfeng.business.offline.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
@ApiModel(description="搭子活动详情响应对象")
public class PartnerDetailResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 活动id
	 */
	@ApiModelProperty(value = "活动id")
	private Integer id;
	/**
	 * 活动发起者oid
	 */
	@ApiModelProperty(value = "活动发起者oid")
	private String oid;

	/**
	 * 活动名称
	 */
	@ApiModelProperty(value = "活动名称")
	private String activityName;
	/**
	 * 省份
	 */
	@ApiModelProperty(value = "省份")
	private String province;
	/**
	 * 城市
	 */
	@ApiModelProperty(value = "城市")
	private String city;
	/**
	 * 地址标题
	 */
	@ApiModelProperty(value = "地址标题")
	private String addressTitle;
	/**
	 * 精度
	 */
	@ApiModelProperty(value = "精度")
	private String longitude;
	/**
	 * 纬度
	 */
	@ApiModelProperty(value = "纬度")
	private String latitude;
	/**
	 * 男生参加人数
	 */
	@ApiModelProperty(value = "男生参加人数")
	private Integer manJoinNumber;
	/**
	 * 女生参加人数
	 */
	@ApiModelProperty(value = "女生参加人数")
	private Integer womanJoinNumber;
	/**
	 * 男生人数
	 */
	@ApiModelProperty(value = "男生人数")
	private Integer manNumber;
	/**
	 * 女生人数
	 */
	@ApiModelProperty(value = "女生人数")
	private Integer womanNumber;
	/**
	 * 总人数
	 */
	@ApiModelProperty(value = "总人数")
	private Integer totalNumber;
	/**
	 * 男生费用
	 */
	@ApiModelProperty(value = "男生费用")
	private BigDecimal manAmount;
	/**
	 * 女生费用
	 */
	@ApiModelProperty(value = "女生费用")
	private BigDecimal womanAmount;
	/**
	 * 活动开始时间
	 */
	@ApiModelProperty(value = "活动开始时间")
	private String startTime;
	/**
	 * 活动结束时间
	 */
	@ApiModelProperty(value = "活动结束时间")
	private String endTime;
	/**
	 * 活动报名结束时间
	 */
	@ApiModelProperty(value = "活动报名结束时间")
	private String joinEndTime;
	/**
	 * 活动状态
	 */
	@ApiModelProperty(value = "活动状态")
	private Integer partnerStatus;
	/**
	 * 报名状态
	 */
	@ApiModelProperty(value = "报名状态")
	private Integer joinStatus;
	/**
	 * 轮播图
	 */
	@ApiModelProperty(value = "轮播图")
	private String mediaList;
	/**
	 * 活动详情
	 */
	@ApiModelProperty(value = "活动详情")
	private String remark;
	/**
	 * 参与人头像列表
	 */
	@ApiModelProperty(value = "参与人头像列表")
	private List<String> avatarList;

}
