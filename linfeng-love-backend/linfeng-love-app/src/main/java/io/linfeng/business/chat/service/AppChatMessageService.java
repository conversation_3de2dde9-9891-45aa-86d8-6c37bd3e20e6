/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.chat.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.chat.request.ChatMessageRequest;
import io.linfeng.business.chat.response.ChatMessageResponse;
import io.linfeng.love.user.entity.UserEntity;

/**
 * 聊天服务
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
public interface AppChatMessageService {

    /**
     * 发送聊天消息
     * @param user 用户
     * @param request 聊天请求
     * @return 聊天消息
     */
    ChatMessageResponse sendMessage(UserEntity user, ChatMessageRequest request);

    /**
     * 查询聊天消息列表
     * @param user 用户
     * @param request 查询请求
     * @return 聊天消息列表
     */
    IPage<ChatMessageResponse> getMessageList(UserEntity user, ChatMessageRequest request);

    /**
     * 消息撤回
     * @param user 用户
     * @param request 撤回请求
     */
    void recallMessage(UserEntity user, ChatMessageRequest request);

    /**
     * 消息已读
     * @param user 用户
     * @param request 请求
     */
    void readMessage(UserEntity user, ChatMessageRequest request);
}

