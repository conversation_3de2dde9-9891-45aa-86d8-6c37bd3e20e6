package io.linfeng.business.offline.request;

import io.linfeng.business.pay.request.PayRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="退出活动请求对象")
public class PartnerExitRequest extends PayRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 搭子ID
	 */
	@ApiModelProperty(value = "搭子ID")
	private Integer partnerId;

}
