package io.linfeng.business.bottle.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="漂流瓶消息")
public class BottleMessageResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 漂流瓶id
	 */
	@ApiModelProperty(value = "漂流瓶id")
	private Integer bottleId;
	/**
	 * 消息id
	 */
	@ApiModelProperty(value = "消息id")
	private String messageId;
	/**
	 * 发送者oid
	 */
	@ApiModelProperty(value = "发送者oid")
	private String senderOid;

	/**
	 * 接收者oid
	 */
	@ApiModelProperty(value = "接收者oid")
	private String receiverOid;
	/**
	 * 发送时间
	 */
	@ApiModelProperty(value = "发送时间")
	private String sendTime;
	/**
	 * 内容
	 */
	@ApiModelProperty(value = "内容")
	private String content;
	/**
	 * 类型
	 */
	@ApiModelProperty(value = "类型")
	private Integer messageType;
	/**
	 * 持续时间
	 */
	@ApiModelProperty(value = "持续时间")
	private Integer duration;

}
