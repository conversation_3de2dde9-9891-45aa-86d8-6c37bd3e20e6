package io.linfeng.business.bottle.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="扔漂流瓶请求")
public class ThrowBottleRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 漂流瓶id
	 */
	@ApiModelProperty(value = "漂流瓶id")
	private Integer bottleId;

	/**
	 * 内容
	 */
	@ApiModelProperty(value = "内容")
	private String content;

}
