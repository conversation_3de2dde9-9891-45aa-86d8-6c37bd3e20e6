package io.linfeng.business.guest.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="嘉宾访问信息请求对象")
public class GuestVisitRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 用户oid
	 */
	@ApiModelProperty(value = "用户oid")
	private String oid;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private Integer status;

}
