package io.linfeng.business.guest.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="嘉宾简单信息响应对象")
public class GuestSimpleResponse implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 嘉宾oid
	 */
	@ApiModelProperty(value = "嘉宾oid")
	private String oid;
	/**
	 * 用户名
	 */
	@ApiModelProperty(value = "用户名")
	private String userName;
	/**
	 * 头像
	 */
	@ApiModelProperty(value = "头像")
	private String avatar;
	/**
	 * 性别
	 */
	@ApiModelProperty(value = "性别")
	private Integer gender;
	/**
	 * 年龄
	 */
	@ApiModelProperty(value = "年龄")
	private Integer age;
	/**
	 * 身高
	 */
	@ApiModelProperty(value = "身高")
	private Integer stature;
	/**
	 * 工作
	 */
	@ApiModelProperty(value = "工作")
	private Integer job;
	/**
	 * 工作（字典翻译）
	 */
	@ApiModelProperty(value = "工作（字典翻译）")
	private String jobText;
	/**
	 * 学历
	 */
	@ApiModelProperty(value = "学历")
	private Integer education;
	/**
	 * 学历（字典翻译）
	 */
	@ApiModelProperty(value = "学历（字典翻译）")
	private String educationText;
	/**
	 * 学校
	 */
	@ApiModelProperty(value = "学校")
	private String school;
	/**
	 * 年薪
	 */
	@ApiModelProperty(value = "年薪")
	private Integer salary;
	/**
	 * 年薪（字典翻译）
	 */
	@ApiModelProperty(value = "年薪（字典翻译）")
	private String salaryText;
	/**
	 * 个性签名
	 */
	@ApiModelProperty(value = "个性签名")
	private String signature;
	/**
	 * 锁定标识
	 */
	@ApiModelProperty(value = "锁定标识")
	private Integer lockFlag;
	/**
	 * 操作状态
	 */
	@ApiModelProperty(value = "操作状态")
	private Integer operatorStatus;
	/**
	 * 配对状态
	 */
	@ApiModelProperty(value = "配对状态")
	private Integer coupleStatus;
	/**
	 * 朋友状态
	 */
	@ApiModelProperty(value = "朋友状态")
	private Integer friendStatus;
	/**
	 * 在线状态
	 */
	@ApiModelProperty(value = "在线状态")
	private Integer onlineStatus;
	/**
	 * 是否为会员 0普通用户 1会员
	 */
	@ApiModelProperty(value = "是否为会员")
	private Integer vip;
	/**
	 * 实名状态
	 */
	@ApiModelProperty(value = "实名状态")
	private Integer realNameStatus;
	/**
	 * 距离
	 */
	@ApiModelProperty(value = "距离")
	private Double distance;

}
