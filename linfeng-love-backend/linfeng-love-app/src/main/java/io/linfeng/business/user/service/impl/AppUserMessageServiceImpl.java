package io.linfeng.business.user.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.linfeng.business.user.request.UserMessageRequest;
import io.linfeng.business.user.response.UserMessageDetailResponse;
import io.linfeng.business.user.response.UserMessageInteractResponse;
import io.linfeng.business.user.response.UserMessageResponse;
import io.linfeng.business.user.service.AppUserMessageService;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.bottle.entity.BottleMessageEntity;
import io.linfeng.love.chat.enums.MessageType;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.entity.UserMessageDetailEntity;
import io.linfeng.love.user.entity.UserMessageEntity;
import io.linfeng.love.user.entity.UserMessageInteractEntity;
import io.linfeng.love.user.enums.MessageSource;
import io.linfeng.love.user.service.UserMessageDetailService;
import io.linfeng.love.user.service.UserMessageInteractService;
import io.linfeng.love.user.service.UserMessageService;
import io.linfeng.love.user.service.UserService;
import io.linfeng.push.client.dto.CommonSessionData;
import io.linfeng.push.client.dto.PushMessage;
import io.linfeng.push.client.enums.PushMessageType;
import io.linfeng.push.client.producer.MessageProducer;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("appUserMessageService")
public class AppUserMessageServiceImpl implements AppUserMessageService {

    private static Map<String, String> messageSourceMap = new HashMap<>();

    static{
        messageSourceMap.put("official", "官方助手");
        messageSourceMap.put("system", "系统通知");
        messageSourceMap.put("interact", "互动消息");
        messageSourceMap.put("bottle", "漂流瓶");
    }

    private final UserMessageService userMessageService;

    private final UserMessageDetailService userMessageDetailService;

    private final UserMessageInteractService userMessageInteractService;

    private final MessageProducer messageProducer;

    private final UserService userService;

    public AppUserMessageServiceImpl(UserMessageService userMessageService, UserMessageDetailService userMessageDetailService, UserMessageInteractService userMessageInteractService, MessageProducer messageProducer, UserService userService) {
        this.userMessageService = userMessageService;
        this.userMessageDetailService = userMessageDetailService;
        this.userMessageInteractService = userMessageInteractService;
        this.messageProducer = messageProducer;
        this.userService = userService;
    }


    @Override
    public List<UserMessageResponse> getUserMessageList(Integer uid) {
        LambdaQueryWrapper<UserMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserMessageEntity::getUid, uid);
        wrapper.orderByDesc(UserMessageEntity::getLastMessageTime);
        List<UserMessageEntity> userMessageList = userMessageService.list(wrapper);
        List<UserMessageResponse> responseList = new ArrayList<>();
        userMessageList.forEach(userMessageEntity -> {
            UserMessageResponse userMessageResponse = ObjectMapperUtil.convert(userMessageEntity, UserMessageResponse.class);
            userMessageResponse.setLastMessageTime(DateUtil.dateToStr(userMessageEntity.getLastMessageTime(), DateUtil.DATE_FORMAT));
            userMessageResponse.setSenderName(messageSourceMap.get(userMessageEntity.getMessageSource()));

            responseList.add(userMessageResponse);
        });
        return responseList;
    }

    @Override
    public IPage<UserMessageDetailResponse> getUserMessageDetailList(Integer uid, UserMessageRequest request) {
        LambdaQueryWrapper<UserMessageDetailEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserMessageDetailEntity::getUid, uid);
        wrapper.eq(UserMessageDetailEntity::getMessageSource, request.getMessageSource());
        wrapper.orderByDesc(UserMessageDetailEntity::getSendTime);
        IPage<UserMessageDetailEntity> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<UserMessageDetailEntity> userMessageDetailEntityPage = userMessageDetailService.page(page, wrapper);
        IPage<UserMessageDetailResponse> responsePage = new Page<>(request.getPageNum(), request.getPageSize());
        List<UserMessageDetailResponse> responseList = new ArrayList<>();
        userMessageDetailEntityPage.getRecords().forEach(userMessageDetail -> {
            UserMessageDetailResponse userMessageDetailResponse = ObjectMapperUtil.convert(userMessageDetail, UserMessageDetailResponse.class);
            userMessageDetailResponse.setSendTime(DateUtil.dateToStr(userMessageDetail.getSendTime(), DateUtil.DATE_FORMAT));
            responseList.add(userMessageDetailResponse);
        });
        responsePage.setSize(userMessageDetailEntityPage.getSize());
        responsePage.setCurrent(userMessageDetailEntityPage.getCurrent());
        responsePage.setTotal(userMessageDetailEntityPage.getTotal());
        responsePage.setPages(userMessageDetailEntityPage.getPages());
        responsePage.setRecords(responseList);
        return responsePage;
    }

    @Override
    public void readMessage(UserEntity user, UserMessageRequest request) {
        LambdaQueryWrapper<UserMessageEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(UserMessageEntity::getMessageSource, request.getMessageSource());
        lambdaQueryWrapper.eq(UserMessageEntity::getUid, user.getUid());
        UserMessageEntity userMessageEntity = userMessageService.getOne(lambdaQueryWrapper);
        userMessageEntity.setUnRead(0);
        userMessageService.updateById(userMessageEntity);
    }

    @Override
    public IPage<UserMessageInteractResponse> getUserMessageInteractList(Integer uid, UserMessageRequest request) {
        LambdaQueryWrapper<UserMessageInteractEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserMessageInteractEntity::getUid, uid);
        wrapper.orderByDesc(UserMessageInteractEntity::getSendTime);
        IPage<UserMessageInteractEntity> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<UserMessageInteractEntity> userMessageInteractEntityPage = userMessageInteractService.page(page, wrapper);
        List<UserMessageInteractResponse> responseList = new ArrayList<>();
        userMessageInteractEntityPage.getRecords().forEach(userMessageInteract -> {
            UserMessageInteractResponse response = ObjectMapperUtil.convert(userMessageInteract, UserMessageInteractResponse.class);
            response.setSendTime(DateUtil.dateToStr(userMessageInteract.getSendTime(), DateUtil.DATE_FORMAT));
            responseList.add(response);
        });
        IPage<UserMessageInteractResponse> responsePage =  new Page<>();
        responsePage.setSize(userMessageInteractEntityPage.getSize());
        responsePage.setCurrent(userMessageInteractEntityPage.getCurrent());
        responsePage.setTotal(userMessageInteractEntityPage.getTotal());
        responsePage.setPages(userMessageInteractEntityPage.getPages());
        responsePage.setRecords(responseList);
        return responsePage;
    }

    @Override
    public void sendBottleMessage(BottleMessageEntity bottleMessage) {
        UserEntity user = userService.getUserByUid(bottleMessage.getReceiverUid());
        LambdaQueryWrapper<UserMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserMessageEntity::getUid, bottleMessage.getReceiverUid());
        wrapper.eq(UserMessageEntity::getMessageSource, MessageSource.BOTTLE.getValue());
        UserMessageEntity userMessage = userMessageService.getOne(wrapper);
        if(userMessage == null){
            userMessage = new UserMessageEntity();
            userMessage.setUid(bottleMessage.getReceiverUid());
            userMessage.setMessageSource(MessageSource.BOTTLE.getValue());
            userMessage.setUnRead(1);
            userMessage.setLastMessageType(bottleMessage.getMessageType());
            userMessage.setLastMessageTitle(user.getUserName() + " " + userMessage.buildLastMessageTitle(bottleMessage.getContent()));
            userMessage.setLastMessageTime(DateUtil.nowDateTime());
            userMessage.setCreateTime(DateUtil.nowDateTime());
            userMessageService.save(userMessage);
        }else{
            userMessage.setUnRead(userMessage.getUnRead() + 1);
            userMessage.setLastMessageType(bottleMessage.getMessageType());
            userMessage.setLastMessageTitle(user.getUserName() + " " + userMessage.buildLastMessageTitle(bottleMessage.getContent()));
            userMessage.setLastMessageTime(DateUtil.nowDateTime());
            userMessage.setUpdateTime(DateUtil.nowDateTime());
            userMessageService.updateById(userMessage);
        }

        //推送关注消息
        PushMessage pushMessage = new PushMessage();
        pushMessage.setReceiverUid(bottleMessage.getReceiverUid());
        pushMessage.setType(PushMessageType.BOTTLE.getValue());
        CommonSessionData messageData = ObjectMapperUtil.convert(userMessage, CommonSessionData.class);
        messageData.setSenderName(messageSourceMap.get(messageData.getMessageSource()));
        messageData.setLastMessageTime(DateUtil.dateToStr(userMessage.getLastMessageTime(), DateUtil.DATE_FORMAT));
        pushMessage.setData(messageData);
        messageProducer.sendMessage(Constant.DIRECT_EXCHANGE_NAME, Constant.PUSH_ROUTING_NAME, pushMessage);
    }

    @Override
    public void sendUserInteractMessage(UserMessageInteractEntity userMessageInteractEntity) {
        //更新未读消息状态
        LambdaQueryWrapper<UserMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserMessageEntity::getUid, userMessageInteractEntity.getUid());
        wrapper.eq(UserMessageEntity::getMessageSource, MessageSource.INTERACT.getValue());
        UserMessageEntity userMessage = userMessageService.getOne(wrapper);
        if(userMessage == null){
            userMessage = new UserMessageEntity();
            userMessage.setUid(userMessageInteractEntity.getUid());
            userMessage.setMessageSource(MessageSource.INTERACT.getValue());
            userMessage.setUnRead(1);
            userMessage.setLastMessageTitle(userMessageInteractEntity.getGuestUserName() + " " + userMessageInteractEntity.getTitle());
            userMessage.setLastMessageType(MessageType.TEXT.getValue());
            userMessage.setLastMessageTime(DateUtil.nowDateTime());
            userMessage.setCreateTime(DateUtil.nowDateTime());
            userMessageService.save(userMessage);
        }else{
            userMessage.setUnRead(userMessage.getUnRead() + 1);
            userMessage.setLastMessageTitle(userMessageInteractEntity.getGuestUserName() + " " + userMessageInteractEntity.getTitle());
            userMessage.setLastMessageType(MessageType.TEXT.getValue());
            userMessage.setLastMessageTime(DateUtil.nowDateTime());
            userMessage.setUpdateTime(DateUtil.nowDateTime());
            userMessageService.updateById(userMessage);
        }

        //推送关注消息
        PushMessage pushMessage = new PushMessage();
        pushMessage.setReceiverUid(userMessageInteractEntity.getUid());
        pushMessage.setType(PushMessageType.INTERACT.getValue());
        CommonSessionData messageData = ObjectMapperUtil.convert(userMessage, CommonSessionData.class);
        messageData.setSenderName(messageSourceMap.get(messageData.getMessageSource()));
        messageData.setLastMessageTime(DateUtil.dateToStr(userMessage.getLastMessageTime(), DateUtil.DATE_FORMAT));
        pushMessage.setData(messageData);
        messageProducer.sendMessage(Constant.DIRECT_EXCHANGE_NAME, Constant.PUSH_ROUTING_NAME, pushMessage);
    }


}