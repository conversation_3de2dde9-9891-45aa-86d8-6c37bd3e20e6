/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.vip.controller;

import io.linfeng.business.pay.response.PrePayResultResponse;
import io.linfeng.business.pay.service.AppPayService;
import io.linfeng.business.vip.request.VipRechargeRequest;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.Result;
import io.linfeng.love.config.entity.VipOptionEntity;
import io.linfeng.love.config.service.VipOptionService;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * 会员模块Api
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
@RestController
@RequestMapping("app/vip")
@Api(tags = "会员模块Api")
public class AppVipController {


    private final VipOptionService vipOptionService;

    private final AppPayService appPayService;

    public AppVipController(VipOptionService vipOptionService, AppPayService appPayService) {
        this.vipOptionService = vipOptionService;
        this.appPayService = appPayService;
    }

    /**
     * 会员充值选项列表查询
     * @return 会员充值选项列表
     */
    @GetMapping("/optionList")
    @ApiOperation("会员充值选项列表")
    public Result<List<VipOptionEntity>> vipList(){
        List<VipOptionEntity> result = vipOptionService.lambdaQuery()
                .orderByAsc(VipOptionEntity::getSort)
                .list();
        return new Result<List<VipOptionEntity>>().ok(result);
    }

    /**
     * 会员预支付订单生成
     * @param user 登录用户
     * @param request 订单请求
     * @param httpRequest http请求
     * @return 预支付订单
     * @throws Exception
     */
    @Login
    @PostMapping("/prePay")
    @ApiOperation("会员充值订单生成")
    public Result<PrePayResultResponse> pay(@ApiIgnore @LoginUser UserEntity user, @RequestBody VipRechargeRequest request, HttpServletRequest httpRequest) throws Exception{

        PrePayResultResponse payResult = appPayService.vipPrePay(user, request, httpRequest);
        return new Result<PrePayResultResponse>().ok(payResult);
    }

}
