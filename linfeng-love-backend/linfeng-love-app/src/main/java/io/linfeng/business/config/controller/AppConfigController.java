/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.config.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.config.dto.request.DictRequest;
import io.linfeng.love.config.dto.response.ConfigGiftGroupResponseDTO;
import io.linfeng.love.config.dto.response.ConfigTagGroupResponseDTO;
import io.linfeng.love.config.entity.*;
import io.linfeng.love.config.service.*;
import io.linfeng.love.message.entity.MessageSystemEntity;
import io.linfeng.love.message.service.MessageSystemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 配置Api
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
@RestController
@RequestMapping("app/config")
@Api(tags = "配置Api")
@AllArgsConstructor
public class AppConfigController {

    private final ConfigBusinessService configBusinessService;

    private final ConfigSystemService configSystemService;

    private final DictItemService dictItemService;

    private final ConfigTagService configTagService;

    private final ConfigGiftService configGiftService;

    private final MessageSystemService messageSystemService;

    private final ConfigExchangeCashService configExchangeCashService;

    private final ConfigExchangePetalService configExchangePetalService;

    private final ConfigPartnerService configPartnerService;

    private final ConfigSubscribeService configSubscribeService;

    private final ConfigUserBackService configUserBackService;


    /**
     * 业务配置查询
     * @return 业务配置
     */
    @GetMapping("/business")
    @ApiOperation("业务配置查询")
    public R businessConfig(){
        List<ConfigBusinessEntity> configBusinessList = configBusinessService.list();
        Map<String, Object> maps = configBusinessList.stream().collect(Collectors.toMap(ConfigBusinessEntity::getParamKey, ConfigBusinessEntity::getParamValue, (key1, key2) -> key2));
        String value = configSystemService.getValue("templateMessageOpen");
        maps.put("templateMessageOpen", value.equals("1"));
        return R.ok(maps);
    }

    @Login
    @PostMapping("/dict/list")
    @ApiOperation("获取字典列表")
    public Result<List<DictItemEntity>> list(@RequestBody DictRequest dictRequest){
        return new Result<List<DictItemEntity>>().ok(dictItemService.getItemList(dictRequest.getTypeCode()));
    }

    /**
     * 标签配置查询
     * @return 标签配置
     */
    @GetMapping("/tag")
    @ApiOperation("标签配置查询")
    public Result<List<ConfigTagGroupResponseDTO>> tagConfig(){
        return new Result<List<ConfigTagGroupResponseDTO>>().ok(configTagService.getTagGroupList());
    }

    /**
     * 礼物配置查询
     * @return 礼物配置
     */
    @GetMapping("/gift")
    @ApiOperation("礼物配置查询")
    public Result<List<ConfigGiftGroupResponseDTO>> giftConfig(){
        return new Result<List<ConfigGiftGroupResponseDTO>>().ok(configGiftService.getGiftGroupList());
    }

    /**
     * 兑换花瓣配置查询
     * @return 礼物配置
     */
    @GetMapping("/exchangePetal")
    @ApiOperation("兑换花瓣配置查询")
    public Result<List<ConfigExchangePetalEntity>> exchangePetalConfig(){
        LambdaQueryWrapper<ConfigExchangePetalEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(ConfigExchangePetalEntity::getSort);
        return new Result<List<ConfigExchangePetalEntity>>().ok(configExchangePetalService.list(wrapper));
    }

    /**
     * 兑换现金配置查询
     * @return 礼物配置
     */
    @GetMapping("/exchangeCash")
    @ApiOperation("兑换现金配置查询")
    public Result<List<ConfigExchangeCashEntity>> exchangeCashConfig(){
        LambdaQueryWrapper<ConfigExchangeCashEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(ConfigExchangeCashEntity::getSort);
        return new Result<List<ConfigExchangeCashEntity>>().ok(configExchangeCashService.list(wrapper));
    }

    /**
     * 首页弹窗信息
     * @return 首页弹窗信息
     */
    @GetMapping("/homePop")
    @ApiOperation("首页弹窗信息")
    public Result<MessageSystemEntity> homePop(){
        return new Result<MessageSystemEntity>().ok(messageSystemService.getLastMessage());
    }

    /**
     * 搭子配置查询
     * @return 搭子配置
     */
    @GetMapping("/partner")
    @ApiOperation("搭子配置查询")
    public Result<List<ConfigPartnerEntity>> partnerConfig(){
        LambdaQueryWrapper<ConfigPartnerEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(ConfigPartnerEntity::getSort);
        return new Result<List<ConfigPartnerEntity>>().ok(configPartnerService.list(wrapper));
    }

    /**
     * 订阅消息模板查询
     * @return 搭子配置
     */
    @GetMapping("/subscribe")
    @ApiOperation("订阅消息模板查询")
    public Result<List<ConfigSubscribeEntity>> subscribeConfig(){
        return new Result<List<ConfigSubscribeEntity>>().ok(configSubscribeService.list());
    }

    @Login
    @GetMapping("/user/back")
    @ApiOperation("获取用户背景列表")
    public Result<List<ConfigUserBackEntity>> list(){
        return new Result<List<ConfigUserBackEntity>>().ok(configUserBackService.list());
    }

}
