/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245/793326982
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.guest.controller;


import io.linfeng.business.guest.request.GuestOperatorRequest;
import io.linfeng.business.guest.response.GuestOperatorResponse;
import io.linfeng.business.guest.response.GuestSimpleResponse;
import io.linfeng.business.guest.service.AppGuestQualityService;
import io.linfeng.business.pay.request.PayRequest;
import io.linfeng.business.pay.response.PrePayResultResponse;
import io.linfeng.business.pay.service.AppPayService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 精选嘉宾信息Api
 */
@RestController
@RequestMapping("/app/guest/quality")
@Api(tags = "精选Api")
public class AppGuestQualityController {

    private final AppGuestQualityService appGuestQualityService;

    private final AppPayService appPayService;

    public AppGuestQualityController(AppGuestQualityService appGuestQualityService, AppPayService appPayService) {
        this.appGuestQualityService = appGuestQualityService;
        this.appPayService = appPayService;
    }

    /**
     * 查询精选嘉宾列表
     * @param user 登录用户
     * @return 精选嘉宾
     */
    @Login
    @GetMapping ("/list")
    @ApiOperation("查询精选嘉宾列表")
    public Result<List<GuestSimpleResponse>> getQualityGuestList(@ApiIgnore @LoginUser UserEntity user){
        List<GuestSimpleResponse> responseList = appGuestQualityService.getQualityGuestList(user);
        return new Result<List<GuestSimpleResponse>>().ok(responseList);
    }

    /**
     * 解锁精选嘉宾列表(1.9.0版本预留)
     * @param user 登录用户
     * @return 解锁精选嘉宾
     */
    @Login
    @GetMapping ("/unlock")
    @ApiOperation("解锁精选嘉宾列表")
    public Result<PrePayResultResponse> unlock(@ApiIgnore @LoginUser UserEntity user, PayRequest request, HttpServletRequest httpRequest){
        PrePayResultResponse payResult = appPayService.qualityUnlockPrePay(user, request, httpRequest);
        return new Result<PrePayResultResponse>().ok(payResult);
    }

    /**
     * 精选嘉宾操作（喜欢/不喜欢）(1.9.0版本预留)
     * @param user 登录用户
     * @param request 操作请求
     * @return 配对结果
     */
    @Login
    @PostMapping("/operator")
    @ApiOperation("推荐嘉宾配对")
    public Result<GuestOperatorResponse> recommendOperator(@ApiIgnore @LoginUser UserEntity user, @RequestBody GuestOperatorRequest request){
        GuestOperatorResponse guestOperatorResponse = appGuestQualityService.qualityOperator(user, request);
        return new Result<GuestOperatorResponse>().ok(guestOperatorResponse);
    }

}
