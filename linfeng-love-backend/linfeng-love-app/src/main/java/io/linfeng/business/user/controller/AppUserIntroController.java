/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.user.controller;


import io.linfeng.business.user.request.UserIntroRequest;
import io.linfeng.business.user.response.UserIntroResponse;
import io.linfeng.business.user.service.AppUserIntroService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * 用户介绍Api
 */
@RestController
@RequestMapping("/app/intro")
@Api(tags = "用户介绍Api")
public class AppUserIntroController {

    private final AppUserIntroService appUserIntroService;

    public AppUserIntroController(AppUserIntroService appUserIntroService) {
        this.appUserIntroService = appUserIntroService;
    }

    /**
     * 获取用户介绍详情
     * @param user 登录用户
     * @param request 查询条件
     * @return 用户介绍详情
     */
    @Login
    @GetMapping ("/detail")
    @ApiOperation("用户介绍详情")
    public Result<UserIntroResponse> detail(@ApiIgnore @LoginUser UserEntity user, UserIntroRequest request){
        UserIntroResponse userIntro = appUserIntroService.getUserIntro(user, request);
        return new Result<UserIntroResponse>().ok(userIntro);
    }

    /**
     * 用户介绍列表
     * @param user 登录用户
     * @return 用户介绍列表
     */
    @Login
    @GetMapping ("/list")
    @ApiOperation("用户介绍列表")
    public Result<List<UserIntroResponse>> list(@ApiIgnore @LoginUser UserEntity user){
        List<UserIntroResponse> userIntroList = appUserIntroService.getUserIntroList(user.getUid());
        return new Result<List<UserIntroResponse>>().ok(userIntroList);
    }

    /**
     * 用户介绍修改
     * @param user 登录用户
     * @param request 待修改的信息
     * @return 修改结果
     */
    @Login
    @PostMapping ("/edit")
    @ApiOperation("用户介绍修改")
    public R edit(@ApiIgnore @LoginUser UserEntity user, @RequestBody UserIntroRequest request){
        appUserIntroService.updateAppUserIntro(user, request);
        return R.ok("修改成功");
    }

}
