package io.linfeng.business.user.service;

import io.linfeng.business.user.request.UserIntroRequest;
import io.linfeng.business.user.response.UserIntroResponse;
import io.linfeng.love.user.entity.UserEntity;

import java.util.List;

/**
 * 用户介绍信息业务服务
 *
 * <AUTHOR>
 * @date 2023-09-06 16:22:14
 */
public interface AppUserIntroService {

    /**
     * 获取用户介绍详情
     * @param user 登录用户
     * @param request 查询条件
     * @return 用户介绍详情
     */
    UserIntroResponse getUserIntro(UserEntity user, UserIntroRequest request);

    /**
     * 获取用户介绍列表
     * @param uid 用户id
     * @return 用户介绍列表
     */
    List<UserIntroResponse> getUserIntroList(Integer uid);

    /**
     * 用户介绍修改
     * @param user 登录用户
     * @param request 待修改的信息
     * @return 修改结果
     */
    void updateAppUserIntro(UserEntity user, UserIntroRequest request);
}

