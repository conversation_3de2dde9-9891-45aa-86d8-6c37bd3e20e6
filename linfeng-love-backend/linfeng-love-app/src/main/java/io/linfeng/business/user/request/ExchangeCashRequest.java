package io.linfeng.business.user.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="现金提取请求对象")
public class ExchangeCashRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "现金提取配置id")
	private Integer id;

	@ApiModelProperty(value = "提现渠道")
	private Integer channel;

	@ApiModelProperty(value = "收款码")
	private String paymentCode;

}
