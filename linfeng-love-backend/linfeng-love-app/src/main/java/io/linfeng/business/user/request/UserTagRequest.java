package io.linfeng.business.user.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(description="用户标签请求对象")
public class UserTagRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 文件名称
	 */
	@ApiModelProperty(value = "文件名称")
	private List<Integer> addIdList;
	/**
	 * 文件名称
	 */
	@ApiModelProperty(value = "文件名称")
	private List<Integer> deleteIdList;

}
