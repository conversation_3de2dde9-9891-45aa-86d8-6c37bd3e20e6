/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.mission.controller;

import io.linfeng.business.mission.response.MissionResponse;
import io.linfeng.business.mission.service.AppMissionService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


/**
 * 任务
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
@RestController
@RequestMapping("app/mission")
@Api(tags = "任务Api")
public class AppMissionController {


    private final AppMissionService appMissionService;

    public AppMissionController(AppMissionService appMissionService) {
        this.appMissionService = appMissionService;
    }


    /**
     * 查询任务列表
     * @param user 登录用户
     * @param activityCode 活动编码
     * @return 任务列表
     */
    @Login
    @GetMapping("/list")
    @ApiOperation("任务列表")
    public Result<List<MissionResponse>> list(@ApiIgnore @LoginUser UserEntity user, String activityCode){
        List<MissionResponse> missionList = appMissionService.getMissionList(user, activityCode);
        return new Result<List<MissionResponse>>().ok(missionList);
    }

    /**
     * 领取任务
     * @param user 登录用户
     * @return 领取结果
     */
    @Login
    @PostMapping("/receive")
    @ApiOperation("任务领取")
    public R receive(@ApiIgnore @LoginUser UserEntity user){
        appMissionService.receiveToDayMissionList(user);
        return R.ok();
    }



}
