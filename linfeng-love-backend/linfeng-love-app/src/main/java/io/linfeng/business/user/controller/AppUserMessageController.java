/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.user.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.user.request.UserMessageRequest;
import io.linfeng.business.user.response.UserMessageDetailResponse;
import io.linfeng.business.user.response.UserMessageInteractResponse;
import io.linfeng.business.user.response.UserMessageResponse;
import io.linfeng.business.user.service.AppUserMessageService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * 用户消息Api
 */
@RestController
@RequestMapping("/app/message")
@Api(tags = "用户消息Api")
public class AppUserMessageController {

    private final AppUserMessageService appUserMessageService;

    public AppUserMessageController(AppUserMessageService appUserMessageService) {
        this.appUserMessageService = appUserMessageService;
    }

    /**
     * 用户消息列表
     * @param user 登录用户
     * @return 用户消息列表
     */
    @Login
    @GetMapping ("/list")
    @ApiOperation("用户消息列表")
    public Result<List<UserMessageResponse>> list(@ApiIgnore @LoginUser UserEntity user){
        List<UserMessageResponse> userMessageList = appUserMessageService.getUserMessageList(user.getUid());
        return new Result<List<UserMessageResponse>>().ok(userMessageList);
    }

    /**
     * 用户消息明细列表
     * @param user 登录用户
     * @param request 查询请求
     * @return 用户消息明细列表
     */
    @Login
    @GetMapping ("/detail/list")
    @ApiOperation("用户消息明细列表")
    public Result<IPage<UserMessageDetailResponse>> detailList(@ApiIgnore @LoginUser UserEntity user, UserMessageRequest request){
        IPage<UserMessageDetailResponse> userMessageDetailList = appUserMessageService.getUserMessageDetailList(user.getUid(), request);
        return new Result<IPage<UserMessageDetailResponse>>().ok(userMessageDetailList);
    }

    /**
     * 用户互动消息明细列表
     * @param user 登录用户
     * @param request 查询请求
     * @return 用户互动消息明细列表
     */
    @Login
    @GetMapping ("/interact/list")
    @ApiOperation("用户互动消息明细列表")
    public Result<IPage<UserMessageInteractResponse>> interactList(@ApiIgnore @LoginUser UserEntity user, UserMessageRequest request){
        IPage<UserMessageInteractResponse> userMessageDetailList = appUserMessageService.getUserMessageInteractList(user.getUid(), request);
        return new Result<IPage<UserMessageInteractResponse>>().ok(userMessageDetailList);
    }

    /**
     * 消息已读
     * @param user 登录用户
     * @param request 请求
     * @return 处理结果
     */
    @Login
    @PostMapping("/read")
    @ApiOperation("消息已读")
    public R read(@ApiIgnore @LoginUser UserEntity user, @RequestBody UserMessageRequest request){
        appUserMessageService.readMessage(user, request);
        return R.ok();
    }

}
