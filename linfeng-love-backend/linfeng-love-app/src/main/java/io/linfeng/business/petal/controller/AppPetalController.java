/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.petal.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.pay.response.PrePayResultResponse;
import io.linfeng.business.pay.service.AppPayService;
import io.linfeng.business.petal.request.ExchangePetalRequest;
import io.linfeng.business.petal.request.PetalRechargeRequest;
import io.linfeng.business.petal.request.PetalRecordRequest;
import io.linfeng.business.petal.request.PetalUnlockRequest;
import io.linfeng.business.petal.response.PetalRecordResponse;
import io.linfeng.business.petal.service.AppPetalService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.petal.entity.PetalOptionEntity;
import io.linfeng.love.petal.service.PetalOptionService;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * 花瓣Api
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
@RestController
@RequestMapping("app/petal")
@Api(tags = "花瓣Api")
public class AppPetalController {

    private final PetalOptionService petalOptionService;

    private final AppPetalService appPetalService;

    private final AppPayService appPayService;

    public AppPetalController(PetalOptionService petalOptionService, AppPetalService appPetalService, AppPayService appPayService) {
        this.petalOptionService = petalOptionService;
        this.appPetalService = appPetalService;
        this.appPayService = appPayService;
    }


    /**
     * 花瓣充值选项列表查询
     * @return 花瓣充值选项列表
     */
    @Login
    @GetMapping("/optionList")
    @ApiOperation("花瓣充值选项列表")
    public Result<List<PetalOptionEntity>> vipList(){
        List<PetalOptionEntity> result = petalOptionService.lambdaQuery()
                .orderByAsc(PetalOptionEntity::getSort)
                .list();
        return new Result<List<PetalOptionEntity>>().ok(result);
    }
    /**
     * 花瓣充值预支付订单生成
     * @param user 登录用户
     * @param request 充值请求对象
     * @param httpRequest http请求
     * @return 预支付订单
     * @throws Exception
     */
    @Login
    @PostMapping("/prePay")
    @ApiOperation("花瓣充值订单生成")
    public Result<PrePayResultResponse> pay(@ApiIgnore @LoginUser UserEntity user, @RequestBody PetalRechargeRequest request, HttpServletRequest httpRequest) throws Exception{

        PrePayResultResponse payResult = appPayService.petalPrePay(user, request, httpRequest);
        return new Result<PrePayResultResponse>().ok(payResult);
    }

    /**
     * 花瓣解锁嘉宾
     * @param user 登录用户
     * @param request 解锁请求
     * @return 解锁结果
     */
    @Login
    @PostMapping("/unlock")
    @ApiOperation("花瓣解锁嘉宾")
    public R unlock(@ApiIgnore @LoginUser UserEntity user, @RequestBody PetalUnlockRequest request){
        appPetalService.unlock(user, request);
        return R.ok();
    }

    /**
     * 兑换花瓣
     * @param user 登录用户
     * @param request 请求
     * @return 兑换结果
     */
    @Login
    @PostMapping("/exchange")
    @ApiOperation("兑换花瓣")
    public R exchangePetal(@ApiIgnore @LoginUser UserEntity user, @RequestBody ExchangePetalRequest request){
        appPetalService.exchangePetal(user, request);
        return R.ok();
    }

    /**
     * 花瓣记录
     * @param user 登录用户
     * @param request 查询请求
     * @return 兑换结果
     */
    @Login
    @GetMapping("/record")
    @ApiOperation("花瓣记录")
    public Result<IPage<PetalRecordResponse>> getRecordList(@ApiIgnore @LoginUser UserEntity user, PetalRecordRequest request){
        IPage<PetalRecordResponse> responseList = appPetalService.getRecordList(user, request);
        return new Result<IPage<PetalRecordResponse>>().ok(responseList);
    }

}
