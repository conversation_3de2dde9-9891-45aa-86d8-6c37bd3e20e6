package io.linfeng.business.subscribe.core;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.business.subscribe.dto.WxMaSubscribeMessageDTO;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.love.message.entity.SubscribeMessageEntity;
import io.linfeng.love.message.enums.SendStatus;
import io.linfeng.love.message.enums.SubscribeChannel;
import io.linfeng.love.message.enums.SubscribeStatus;
import io.linfeng.love.message.enums.SubscribeType;
import io.linfeng.love.message.service.SubscribeMessageService;
import io.linfeng.love.user.entity.UserEntity;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.subscribe.WxMpSubscribeMessage;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 
 * 订阅消息构造服务
 * 
 * <AUTHOR>
 *
 */
@Slf4j
public abstract class AbstractSubscribeMessageExecutor implements SubscribeMessageExecutor{

    @Autowired
    private WxMpService wxMpService;

    @Autowired
    private WxMaService wxMaService;

    @Autowired
    protected SubscribeMessageService subscribeMessageService;


    @Override
    public void executeWxMaSubscribeMessage(UserEntity user, String tmplId, String linkId) {
        WxMaSubscribeMessageDTO wxMaSubscribeMessageDTO = buildWxMaSubscribeMessage(user, tmplId, linkId);
        try {
            if(wxMaSubscribeMessageDTO.isImmediately()){
                wxMaService.getSubscribeService().sendSubscribeMsg(wxMaSubscribeMessageDTO.getWxMaSubscribeMessage());
            }

        } catch (WxErrorException e) {
            log.error("微信小程序消息订阅异常", e);
            throw new LinfengException("消息订阅异常");
        }
    }

    @Override
    public void executeWxMpSubscribeMessage(UserEntity user, String tmplId, String linkId) {
        WxMpSubscribeMessage wxMpSubscribeMessage = buildWxMpSubscribeMessage(user, tmplId, linkId);
        try {
            wxMpService.getSubscribeMsgService().send(wxMpSubscribeMessage);
        } catch (WxErrorException e) {
            log.error("微信公众号消息订阅异常", e);
            throw new LinfengException("消息订阅异常");
        }
    }

    /**
     * 构建小程序订阅消息主体
     * @return 小程序订阅消息主体
     */
    public abstract WxMaSubscribeMessageDTO buildWxMaSubscribeMessage(UserEntity user, String tmplId, String linkId);

    /**
     * 构建公众号订阅消息主体
     * @return 公众号订阅消息主体
     */
    public abstract WxMpSubscribeMessage buildWxMpSubscribeMessage(UserEntity user, String tmplId, String linkId);

    public abstract String getPageUrl(String linkId);

    public abstract String getMessageType();


	protected void saveSubscribeMessage(UserEntity user, String tmplId, String linkId, String content){
        LambdaQueryWrapper<SubscribeMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SubscribeMessageEntity::getMessageType, getMessageType());
        wrapper.eq(SubscribeMessageEntity::getLinkId, linkId);
        wrapper.eq(SubscribeMessageEntity::getUid, user.getUid());
        SubscribeMessageEntity subscribeMessageEntity = subscribeMessageService.getOne(wrapper);
        if(subscribeMessageEntity == null){
            subscribeMessageEntity = new SubscribeMessageEntity();
        }
        subscribeMessageEntity.setUid(user.getUid());
        subscribeMessageEntity.setOpenid(user.getOpenid());
        subscribeMessageEntity.setChannel(SubscribeChannel.MA.getValue());
        subscribeMessageEntity.setSendStatus(SendStatus.NO.getValue());
        subscribeMessageEntity.setMessageType(getMessageType());
        subscribeMessageEntity.setTmplId(tmplId);
        subscribeMessageEntity.setLinkId(linkId);
        subscribeMessageEntity.setUrl(getPageUrl(linkId));
        subscribeMessageEntity.setSubscribeType(SubscribeType.ONCE.getValue());
        subscribeMessageEntity.setSubscribeStatus(SubscribeStatus.YES.getValue());
        subscribeMessageEntity.setContent(content);
        if(subscribeMessageEntity.getId() == null){
            subscribeMessageEntity.setCreateTime(DateUtil.nowDateTime());
            subscribeMessageService.save(subscribeMessageEntity);
        }else{
            subscribeMessageEntity.setUpdateTime(DateUtil.nowDateTime());
            subscribeMessageService.updateById(subscribeMessageEntity);
        }
    }
}
