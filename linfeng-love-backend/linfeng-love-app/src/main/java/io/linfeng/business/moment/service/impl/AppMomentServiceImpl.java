package io.linfeng.business.moment.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.linfeng.business.censor.service.AppCensorService;
import io.linfeng.business.moment.request.*;
import io.linfeng.business.moment.response.*;
import io.linfeng.business.moment.service.AppMomentService;
import io.linfeng.business.user.service.AppUserMessageService;
import io.linfeng.common.enums.CountOperatorType;
import io.linfeng.common.enums.MediaType;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.*;
import io.linfeng.love.common.enums.CommonStatus;
import io.linfeng.love.config.service.DictItemService;
import io.linfeng.love.moment.dto.response.MomentCommentResponseDTO;
import io.linfeng.love.moment.dto.response.MomentLikeResponseDTO;
import io.linfeng.love.moment.dto.response.MomentMediaResponseDTO;
import io.linfeng.love.moment.dto.response.MomentResponseDTO;
import io.linfeng.love.moment.entity.*;
import io.linfeng.love.moment.enums.MomentQueryType;
import io.linfeng.love.moment.service.*;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.entity.UserMessageInteractEntity;
import io.linfeng.love.user.service.UserMessageInteractService;
import io.linfeng.love.user.service.UserService;
import io.linfeng.push.client.enums.InteractMessageDataType;
import io.linfeng.transport.cloud.tencent.TencentMapTransport;
import io.linfeng.transport.cloud.tencent.response.IpLocationResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Service("appMomentService")
@AllArgsConstructor
public class AppMomentServiceImpl implements AppMomentService {

    private static final String ANONYMOUS_USER_NAME = "匿名用户";

    private final MomentService momentService;

    private final UserService userService;

    private final MomentMediaService momentMediaService;

    private final MomentTopicService momentTopicService;

    private final MomentLikeService momentLikeService;

    private final MomentCommentService momentCommentService;

    private final DictItemService dictItemService;

    private final UserMessageInteractService userMessageInteractService;

    private final AppUserMessageService appUserMessageService;

    private final AppCensorService appCensorService;

    private final TencentMapTransport tencentMapTransport;


    @Override
    public IPage<MomentResponse> getMomentList(UserEntity user, MomentQueryRequest request) {

        IPage<MomentResponseDTO> dtoPage = new Page<>(request.getPageNum(),request.getPageSize());

        if(request.getQueryType() == MomentQueryType.NEW.getValue()){
            dtoPage = momentService.selectNewestMomentPage(dtoPage, user.getUid(), request.getTopicId());
        }

        if(request.getQueryType() == MomentQueryType.HOT.getValue()){
            dtoPage = momentService.selectHottestMomentPage(dtoPage, user.getUid(), request.getTopicId());
        }

        if(request.getQueryType() == MomentQueryType.CONCERN.getValue()){
            dtoPage = momentService.selectConcernMomentPage(dtoPage, user.getUid());
        }

        if(request.getQueryType() == MomentQueryType.GUEST.getValue()){
            UserEntity guestUser = userService.getUserByOid(request.getOid());
            dtoPage = momentService.selectGuestMomentPage(dtoPage, user.getUid(), guestUser.getUid());
        }

        if(request.getQueryType() == MomentQueryType.MY.getValue()){
            dtoPage = momentService.selectMyMomentPage(dtoPage, user.getUid());
        }

        IPage<MomentResponse> page = ObjectMapperUtil.convert(dtoPage, MomentResponse.class);
        List<MomentResponse> momentList = page.getRecords();
        momentList.forEach(moment -> {
            if (!StringUtil.isEmpty(moment.getJob())) {
                moment.setJobText(dictItemService.getItemName(Constant.DICT_JOB, moment.getJob()));
            }
            if(moment.getAnonymous()){
                moment.setUserName(ANONYMOUS_USER_NAME);
            }
            if (!StringUtil.isEmpty(moment.getBirthday())) {
                moment.setBirthdayYear(moment.getBirthday().substring(2, 4));
            }

        });

        return page;
    }

    @Override
    @Transactional
    public void addMoment(UserEntity user, MomentRequest request) {

        //文本审核
        appCensorService.censorText(request.getContent());

        if(request.getMediaList() != null && request.getMediaList().size() != 0) {
            //图片审核
            if(request.getMediaType() == MediaType.IMAGE.getValue()){
                request.getMediaList().forEach(momentMediaRequest -> {
                    appCensorService.censorImage(momentMediaRequest.getUrl());
                });
            }
            //视频审核
            if(request.getMediaType() == MediaType.VIDEO.getValue()){
                request.getMediaList().forEach(momentMediaRequest -> {
                    appCensorService.censorVideo(momentMediaRequest.getUrl());
                });
            }
        }

        //保存动态信息
        MomentEntity momentEntity = ObjectMapperUtil.convert(request, MomentEntity.class);

        momentEntity.setUid(user.getUid());
        momentEntity.setPv(0);
        momentEntity.setLv(0);
        momentEntity.setCv(0);
        if(!StringUtil.isEmpty(request.getLatitude())){
            Map<String, String> cityMap = tencentMapTransport.getCityByGeocoder(request.getLatitude(), request.getLongitude());
            momentEntity.setProvince(cityMap.get("province"));
            momentEntity.setCity(cityMap.get("city"));
        }

        momentEntity.setStatus(CommonStatus.CHECKED.getValue());
        momentEntity.setAnonymous(request.getAnonymous()?1:0);
        momentEntity.setPrivacy(request.getPrivacy()?1:0);
        momentEntity.setCreateTime(DateUtil.nowDateTime());
        momentService.save(momentEntity);

        //保存附件信息
        if(request.getMediaList() != null && request.getMediaList().size() != 0){
            List<MomentMediaEntity> momentMediaEntityList = new ArrayList<>();
            request.getMediaList().forEach(momentMediaRequest -> {
                MomentMediaEntity momentMediaEntity = new MomentMediaEntity();
                momentMediaEntity.setMomentId(momentEntity.getId());
                momentMediaEntity.setUid(user.getUid());
                momentMediaEntity.setMediaName(IdUtil.fastSimpleUUID());
                momentMediaEntity.setUrl(momentMediaRequest.getUrl());
                momentMediaEntity.setMediaType(momentEntity.getMediaType());
                momentMediaEntity.setCreateTime(DateUtil.nowDateTime());
                momentMediaEntityList.add(momentMediaEntity);
            });

            momentMediaService.saveBatch(momentMediaEntityList);
        }

        //保存话题信息
        if(request.getTopicList() != null && request.getTopicList().size() != 0){
            List<MomentTopicEntity> momentTopicEntityList = new ArrayList<>();
            request.getTopicList().forEach(momentTopicRequest -> {
                MomentTopicEntity momentTopicEntity = new MomentTopicEntity();
                momentTopicEntity.setMomentId(momentEntity.getId());
                momentTopicEntity.setTopicId(momentTopicRequest.getId());
                momentTopicEntity.setCreateTime(DateUtil.nowDateTime());
                momentTopicEntityList.add(momentTopicEntity);
            });

            momentTopicService.saveBatch(momentTopicEntityList);

        }

    }

    @Override
    public MomentResponse getMomentDetail(UserEntity user, Integer momentId) {
        MomentResponseDTO momentResponseDTO = momentService.selectMomentDetail(user.getUid(), momentId);
        if(momentResponseDTO == null){
            return null;
        }
        MomentResponse momentResponse = ObjectMapperUtil.convert(momentResponseDTO, MomentResponse.class);
        if (!StringUtil.isEmpty(momentResponse.getJob())) {
            momentResponse.setJobText(dictItemService.getItemName(Constant.DICT_JOB, momentResponse.getJob()));
        }
        if (!StringUtil.isEmpty(momentResponse.getBirthday())) {
            momentResponse.setBirthdayYear(momentResponse.getBirthday().substring(2,4));
        }
        if(momentResponse.getAnonymous()){
            momentResponse.setUserName(ANONYMOUS_USER_NAME);
        }

        //更新浏览量
        this.updatePv(momentId);

        return momentResponse;
    }

    @Override
    @Transactional
    public void updateLv(UserEntity user, MomentUpdateRequest request) {
        //之前点赞过就直接更新状态
        LambdaQueryWrapper<MomentLikeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentLikeEntity::getUid, user.getUid());
        wrapper.eq(MomentLikeEntity::getMomentId, request.getMomentId());
        MomentLikeEntity momentLikeEntity = momentLikeService.getOne(wrapper);
        if(request.getOperatorType()  == CountOperatorType.ADD.getValue()){

            if(momentLikeEntity != null){

                if(momentLikeEntity.getStatus() == CountOperatorType.ADD.getValue()){
                    return;
                }

                momentLikeEntity.setStatus(CountOperatorType.ADD.getValue());
                momentLikeEntity.setUpdateTime(DateUtil.nowDateTime());
                momentLikeService.updateById(momentLikeEntity);

                //更新动态表
                momentService.updateLv(request.getMomentId(), 1);
                return;
            }
            //首次点赞发送点赞通知
            momentLikeEntity = new MomentLikeEntity();
            momentLikeEntity.setMomentId(request.getMomentId());
            momentLikeEntity.setUid(user.getUid());
            momentLikeEntity.setStatus(CountOperatorType.ADD.getValue());
            momentLikeEntity.setCreateTime(DateUtil.nowDateTime());
            momentLikeService.save(momentLikeEntity);
            //更新动态表
            momentService.updateLv(request.getMomentId(), 1);
            sendLikeMessage(request.getMomentId(), user);
        }else{
            if(momentLikeEntity.getStatus() == CountOperatorType.REDUCE.getValue()){
                return;
            }
            //更新动态表
            momentService.updateLv(request.getMomentId(), -1);

            momentLikeEntity.setStatus(request.getOperatorType());
            momentLikeEntity.setUpdateTime(DateUtil.nowDateTime());
            momentLikeService.updateById(momentLikeEntity);
        }
    }

    @Override
    public void updatePv(Integer momentId) {
        momentService.updatePv(momentId);
    }

    @Override
    public IPage<MomentCommentResponse> getMomentCommentPage(CommentQueryRequest request) {
        IPage<MomentCommentResponseDTO> dtoPage = new Page<>(request.getPageNum(),request.getPageSize());
        dtoPage = momentCommentService.getMomentCommentPage(dtoPage, request.getMomentId());
        IPage<MomentCommentResponse> page = ObjectMapperUtil.convert(dtoPage, MomentCommentResponse.class);
        List<MomentCommentResponse> commentList = page.getRecords();
        page.setRecords(commentList);
        commentList.forEach(comment -> {
            if (!StringUtil.isEmpty(comment.getJob())) {
                comment.setJobText(dictItemService.getItemName(Constant.DICT_JOB, comment.getJob()));
            }
            if (!StringUtil.isEmpty(comment.getBirthday())) {
                comment.setBirthdayYear(comment.getBirthday().substring(2,4));
            }
        });
        return page;
    }

    @Override
    @Transactional
    public MomentCommentResponse addMomentComment(UserEntity user, MomentCommentRequest request, String ip) {
        //文本审核
        appCensorService.censorText(request.getContent());

        MomentCommentEntity momentCommentEntity = new MomentCommentEntity();
        momentCommentEntity.setMomentId(request.getMomentId());
        momentCommentEntity.setPid(request.getPid());
        momentCommentEntity.setContent(request.getContent());
        momentCommentEntity.setStatus(CommonStatus.CHECKED.getValue());
        momentCommentEntity.setCreateTime(DateUtil.nowDateTime());
        momentCommentEntity.setReplyUid(user.getUid());
        IpLocationResponse ipResponse = tencentMapTransport.getLocationByIp(ip);
        if(ipResponse.getCode() == Constant.COMMON_RESPONSE_SUCCESS_CODE){
            Map<String, String> cityMap = tencentMapTransport.getCityByGeocoder(ipResponse.getLatitude(), ipResponse.getLongitude());
            momentCommentEntity.setCity(cityMap.get("city"));
            momentCommentEntity.setProvince(cityMap.get("province"));
            momentCommentEntity.setLatitude(ipResponse.getLatitude());
            momentCommentEntity.setLongitude(ipResponse.getLongitude());
        }
        UserEntity beReplyUser = userService.getUserByOid(request.getBeReplyOid());
        momentCommentEntity.setBeReplyUid(beReplyUser.getUid());
        momentCommentService.save(momentCommentEntity);

        //更新动态评论数
        momentService.updateCv(request.getMomentId(), CountOperatorType.ADD.getValue(), 1);

        //发送评论通知
        sendCommentMessage(beReplyUser.getUid(), user, momentCommentEntity);

        MomentCommentResponse response = new MomentCommentResponse();
        response.setCommentId(momentCommentEntity.getId());
        response.setPid(request.getPid());
        response.setContent(request.getContent());
        response.setReplyOid(user.getOid());
        response.setReplyUserName(user.getUserName());
        response.setBeReplyOid(request.getBeReplyOid());
        response.setBeReplyUserName(beReplyUser.getUserName());
        response.setAvatar(user.getAvatar());
        response.setGender(user.getGender());
        response.setBirthday(user.getBirthday());
        response.setBirthdayYear(user.getBirthday().substring(2,4));
        response.setLivingCity(user.getLivingCity());
        response.setJob(user.getJob());
        response.setJobText(dictItemService.getItemName(Constant.DICT_JOB, user.getJob()));
        response.setIpProvince(momentCommentEntity.getProvince());
        response.setChildCommentList(new ArrayList<>());
        response.setCreateTime(DateUtil.nowDateTime(DateUtil.DATE_FORMAT));
        return response;
    }

    @Override
    @Transactional
    public void deleteMomentComment(MomentCommentRequest request) {
        LambdaQueryWrapper<MomentCommentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentCommentEntity::getPid, request.getCommentId());
        Integer childCommentCount = momentCommentService.count(wrapper);
        momentCommentService.removeById(request.getCommentId());
        if(childCommentCount == 0){
            momentService.updateCv(request.getMomentId(), CountOperatorType.REDUCE.getValue(), 1);
        }else{
            momentService.updateCv(request.getMomentId(), CountOperatorType.REDUCE.getValue(), childCommentCount + 1);
        }

    }

    @Override
    public List<MomentLikeResponse> getMomentLikeList(Integer momentId) {
        List<MomentLikeResponseDTO> responseDTOList = momentLikeService.getMomentLikeList(momentId);
        List<MomentLikeResponse> responseList = ObjectMapperUtil.convert(responseDTOList, MomentLikeResponse.class);
        return responseList;
    }

    @Override
    public List<MomentMediaResponse> getMomentImageList(Integer momentId) {
        List<MomentMediaResponseDTO> responseDTOList = momentMediaService.getMomentImageList(momentId);
        List<MomentMediaResponse> responseList = ObjectMapperUtil.convert(responseDTOList, MomentMediaResponse.class);
        return responseList;
    }

    @Override
    public CommentDetailResponse getMomentCommentDetail(Integer commentId) {
        MomentCommentEntity momentCommentEntity = momentCommentService.getById(commentId);
        CommentDetailResponse response = new CommentDetailResponse();
        response.setMomentId(momentCommentEntity.getMomentId());
        response.setContent(momentCommentEntity.getContent());
        UserEntity replyUser = userService.getUserByUid(momentCommentEntity.getReplyUid());
        response.setReplyOid(replyUser.getOid());
        response.setReplyUserName(replyUser.getUserName());
        response.setCreateTime(DateUtil.dateToStr(momentCommentEntity.getCreateTime(), DateUtil.DATE_FORMAT));
        return response;
    }

    @Override
    public MomentImageResponse getUserMomentImage(Integer uid) {

        MomentImageResponse response = new MomentImageResponse();
        //个人动态数量
        LambdaQueryWrapper<MomentEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(MomentEntity::getUid, uid);
        response.setMomentListLength(momentService.count(lambdaQueryWrapper));

        //个人动态照片，最多展示四张
        List<MomentMediaResponse> momentMediaList = getMomentImageList(uid);
        response.setMomentMediaList(momentMediaList);
        return response;
    }

    @Override
    public void deleteMoment(MomentUpdateRequest request) {
        momentService.removeById(request.getMomentId());
        LambdaQueryWrapper<MomentMediaEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentMediaEntity::getMomentId, request.getMomentId());
        momentMediaService.remove(wrapper);
    }

    private void sendCommentMessage(Integer guestUid, UserEntity user, MomentCommentEntity momentCommentEntity){
        //保存消息记录
        UserMessageInteractEntity userMessageInteractEntity = new UserMessageInteractEntity();
        userMessageInteractEntity.setUid(guestUid);
        userMessageInteractEntity.setGuestOid(user.getOid());
        userMessageInteractEntity.setGuestUserName(user.getUserName());
        userMessageInteractEntity.setGuestAvatar(user.getAvatar());
        if(momentCommentEntity.getPid() == 0){
            userMessageInteractEntity.setTitle(PushMessageConstant.COMMENT_TITLE);
            userMessageInteractEntity.setContent(PushMessageConstant.COMMENT_TITLE + "：" + momentCommentEntity.getContent());
        }else{
            userMessageInteractEntity.setTitle(PushMessageConstant.COMMENT_REPLY_TITLE);
            userMessageInteractEntity.setContent(PushMessageConstant.COMMENT_REPLY_TITLE + "：" + momentCommentEntity.getContent());
        }
        LambdaQueryWrapper<MomentMediaEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentMediaEntity::getMomentId, momentCommentEntity.getMomentId());
        wrapper.eq(MomentMediaEntity::getMediaType, MediaType.IMAGE.getValue());
        wrapper.orderByAsc(MomentMediaEntity::getId);
        List<MomentMediaEntity> mediaList = momentMediaService.list(wrapper);
        //如果是动态有图片，通知展示第一张图片，没有附件就展示内容文字
        if(mediaList != null && mediaList.size() != 0){
            userMessageInteractEntity.setLinkImage(mediaList.get(0).getUrl());
        }else{
            MomentEntity moment = momentService.getById(momentCommentEntity.getMomentId());
            userMessageInteractEntity.setLinkImage(moment.getContent());
        }
        userMessageInteractEntity.setLinkUrl(PushMessageConstant.MOMENT_LINK_URL + momentCommentEntity.getMomentId());
        userMessageInteractEntity.setDataType(InteractMessageDataType.COMMENT.getValue());
        userMessageInteractEntity.setSendTime(DateUtil.nowDateTime());
        userMessageInteractService.save(userMessageInteractEntity);
        appUserMessageService.sendUserInteractMessage(userMessageInteractEntity);
    }

    private void sendLikeMessage(Integer momentId, UserEntity user){
        MomentEntity moment = momentService.getById(momentId);
        UserEntity guestUser = userService.getUserByUid(moment.getUid());
        //保存消息记录
        UserMessageInteractEntity userMessageInteractEntity = new UserMessageInteractEntity();
        userMessageInteractEntity.setUid(guestUser.getUid());
        userMessageInteractEntity.setGuestOid(user.getOid());
        userMessageInteractEntity.setGuestUserName(user.getUserName());
        userMessageInteractEntity.setGuestAvatar(user.getAvatar());
        userMessageInteractEntity.setTitle(PushMessageConstant.LIKE_TITLE);
        userMessageInteractEntity.setContent(PushMessageConstant.LIKE_TITLE);
        LambdaQueryWrapper<MomentMediaEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentMediaEntity::getMomentId, momentId);
        wrapper.eq(MomentMediaEntity::getMediaType, MediaType.IMAGE.getValue());
        wrapper.orderByAsc(MomentMediaEntity::getId);
        List<MomentMediaEntity> mediaList = momentMediaService.list(wrapper);
        //如果是动态有图片，通知展示第一张图片，没有附件就展示内容文字
        if(mediaList != null && mediaList.size() != 0){
            userMessageInteractEntity.setLinkImage(mediaList.get(0).getUrl());
        }else{
            userMessageInteractEntity.setLinkImage(moment.getContent());
        }
        userMessageInteractEntity.setLinkUrl(PushMessageConstant.MOMENT_LINK_URL + momentId);
        userMessageInteractEntity.setDataType(InteractMessageDataType.LIKE.getValue());
        userMessageInteractEntity.setSendTime(DateUtil.nowDateTime());
        userMessageInteractService.save(userMessageInteractEntity);
        appUserMessageService.sendUserInteractMessage(userMessageInteractEntity);

    }
}