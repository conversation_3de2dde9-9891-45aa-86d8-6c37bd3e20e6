package io.linfeng.business.user.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
@ApiModel(description="用户账户记录响应实体")
public class UserAccountRecordResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 类型（1收入、2提现）
	 */
	@ApiModelProperty(value = "类型（1收入、2提现）")
	private Integer type;
	/**
	 * 子类型
	 */
	@ApiModelProperty(value = "子类型")
	private Integer subType;
	/**
	 * 金额
	 */
	@ApiModelProperty(value = "金额")
	private BigDecimal amount;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

}
