/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.guest.service;


import io.linfeng.business.guest.request.GuestOperatorRequest;
import io.linfeng.business.guest.response.GuestOperatorResponse;
import io.linfeng.business.guest.response.GuestSimpleResponse;
import io.linfeng.love.pay.entity.PayOrderEntity;
import io.linfeng.love.user.entity.UserEntity;

import java.util.List;

/**
 * 精选嘉宾业务服务
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
public interface AppGuestQualityService {

    List<GuestSimpleResponse> getQualityGuestList(UserEntity user);

    void unLock(PayOrderEntity payOrder);

    GuestOperatorResponse qualityOperator(UserEntity user, GuestOperatorRequest request);
}

