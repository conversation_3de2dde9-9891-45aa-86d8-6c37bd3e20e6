package io.linfeng.business.user.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.business.user.request.UserIntroRequest;
import io.linfeng.business.user.response.UserIntroResponse;
import io.linfeng.business.user.service.AppUserIntroService;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.user.dto.response.UserIntroResponseDTO;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.entity.UserIntroEntity;
import io.linfeng.love.user.service.UserIntroService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("appUserIntroService")
public class AppUserIntroServiceImpl implements AppUserIntroService {

    final
    UserIntroService userIntroService;

    public AppUserIntroServiceImpl(UserIntroService userIntroService) {
        this.userIntroService = userIntroService;
    }

    @Override
    public UserIntroResponse getUserIntro(UserEntity user, UserIntroRequest request) {
        UserIntroResponse userIntroResponse = new UserIntroResponse();
        UserIntroResponseDTO userIntroResponseDTO = userIntroService.getUserIntro(user.getUid(), request.getIntroId());
        transformerResponse(userIntroResponseDTO, userIntroResponse);
        return userIntroResponse;
    }

    @Override
    public List<UserIntroResponse> getUserIntroList(Integer uid) {
        List<UserIntroResponseDTO> responseDTOList = userIntroService.getUserIntroList(uid);
        return ObjectMapperUtil.convert(responseDTOList, UserIntroResponse.class);
    }

    @Override
    public void updateAppUserIntro(UserEntity user, UserIntroRequest request) {

        LambdaQueryWrapper<UserIntroEntity> wrapper =new LambdaQueryWrapper<>();
        wrapper.eq(UserIntroEntity::getUid,user.getUid());
        wrapper.eq(UserIntroEntity::getIntroId,request.getIntroId());
        UserIntroEntity userIntro = userIntroService.getOne(wrapper);
        if(userIntro == null){
            userIntro = new UserIntroEntity();
            userIntro.setUid(user.getUid());
            userIntro.setIntroId(request.getIntroId());
            userIntro.setContent(request.getContent());
            userIntro.setCreateTime(DateUtil.nowDateTime());
            userIntroService.save(userIntro);
        }else{
            userIntro.setContent(request.getContent());
            userIntro.setUpdateTime(DateUtil.nowDateTime());
            userIntroService.updateById(userIntro);
        }

    }

    private void transformerResponse(UserIntroResponseDTO responseDTO, UserIntroResponse response){
        BeanUtils.copyProperties(responseDTO, response);
    }
}