package io.linfeng.business.guest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.business.guest.request.GuestOperatorRequest;
import io.linfeng.business.guest.response.GuestOperatorResponse;
import io.linfeng.business.guest.response.GuestSimpleResponse;
import io.linfeng.business.guest.service.AppGuestQualityService;
import io.linfeng.business.petal.service.AppPetalService;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.*;
import io.linfeng.love.config.service.ConfigBusinessService;
import io.linfeng.love.config.service.DictItemService;
import io.linfeng.love.guest.dto.response.GuestSimpleResponseDTO;
import io.linfeng.love.guest.entity.GuestQualityEntity;
import io.linfeng.love.guest.enums.RecommendOperatorStatus;
import io.linfeng.love.guest.service.GuestQualityService;
import io.linfeng.love.pay.entity.PayOrderEntity;
import io.linfeng.love.petal.enums.PetalRecordSubType;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.service.UserService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;


@Service("appGuestQualityService")
@AllArgsConstructor
public class AppGuestQualityServiceImpl implements AppGuestQualityService {

    private static final String CACHE_USER_QUALITY_PREFIX = "quality:";

    private final RedisUtil redisUtils;

    private final ConfigBusinessService configBusinessService;

    private final GuestQualityService guestQualityService;

    private final UserService userService;

    private final DictItemService dictItemService;

    private final AppPetalService appPetalService;

    @Override
    public List<GuestSimpleResponse> getQualityGuestList(UserEntity user) {
        //给用户推送的精选嘉宾数据不在数据库中进行存储，由redis记录，如果用户解锁了这一批嘉宾，再进行入库
        List<GuestSimpleResponseDTO> guestSimpleResponseList = redisUtils.getList(CACHE_USER_QUALITY_PREFIX + user.getUid(), GuestSimpleResponseDTO.class);
        if (guestSimpleResponseList != null) {
            return transform(guestSimpleResponseList);
        }
        Map<String, Object> params = new HashMap<>();
        if(user.getGender() == Constant.MAN){
            params.put("gender", Constant.WOMAN);
        }else{
            params.put("gender", Constant.MAN);
        }
        params.put("uid", user.getUid());

        Integer count = Integer.parseInt(configBusinessService.getValue(Constant.QUALITY_COUNT));
        params.put("count", count);

        List<GuestSimpleResponseDTO> guestSimpleResponseDTOList = guestQualityService.selectQualityGuestList(params);
        if(guestSimpleResponseDTOList == null || guestSimpleResponseDTOList.size() == 0){
            throw new LinfengException("精选嘉宾已全部解锁", ErrorMessage.COMMON_GUIDE_CODE);
        }
        Integer refreshHour = Integer.parseInt(configBusinessService.getValue(Constant.QUALITY_REFRESH_HOUR));
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.HOUR_OF_DAY, refreshHour);
        redisUtils.set(CACHE_USER_QUALITY_PREFIX + user.getUid(), guestSimpleResponseDTOList, calendar.getTime());
        return transform(guestSimpleResponseDTOList);
    }

    private List<GuestSimpleResponse> transform(List<GuestSimpleResponseDTO> guestSimpleResponseList){
        List<GuestSimpleResponse> responseList = ObjectMapperUtil.convert(guestSimpleResponseList, GuestSimpleResponse.class);
        responseList.forEach(guestSimple -> {
            if (!StringUtil.isEmpty(guestSimple.getJob())) {
                guestSimple.setJobText(dictItemService.getItemName(Constant.DICT_JOB, guestSimple.getJob()));
            }
            if (!StringUtil.isEmpty(guestSimple.getEducation())) {
                guestSimple.setEducationText(dictItemService.getItemName(Constant.DICT_EDUCATION, guestSimple.getEducation()));
            }
            if (!StringUtil.isEmpty(guestSimple.getSalary())) {
                guestSimple.setSalaryText(dictItemService.getItemName(Constant.DICT_SALARY, guestSimple.getSalary()));
            }
        });
        return responseList;
    }

    @Override
    public void unLock(PayOrderEntity payOrder) {
        UserEntity user = userService.getUserByUid(payOrder.getUid());
        List<GuestSimpleResponseDTO> guestSimpleResponseList = redisUtils.getList(CACHE_USER_QUALITY_PREFIX + user.getUid(), GuestSimpleResponseDTO.class);
        List<GuestQualityEntity> guestQualityEntityList = new ArrayList<>();
        guestSimpleResponseList.forEach(guestSimpleResponseDTO -> {
            GuestQualityEntity guestQualityEntity = new GuestQualityEntity();
            guestQualityEntity.setUid(user.getUid());
            guestQualityEntity.setGuestUid(guestSimpleResponseDTO.getUid());
            guestQualityEntity.setStatus(RecommendOperatorStatus.DEFAULT.getValue());
            guestQualityEntity.setCreateTime(DateUtil.nowDateTime());
            guestQualityEntityList.add(guestQualityEntity);
        });
        guestQualityService.saveBatch(guestQualityEntityList);
        redisUtils.delete(CACHE_USER_QUALITY_PREFIX + user.getUid());
        getQualityGuestList(user);
    }

    @Override
    public GuestOperatorResponse qualityOperator(UserEntity user, GuestOperatorRequest request) {

        if(request.getStatus() == RecommendOperatorStatus.DISLIKE.getValue()){

        }

        //校验精选嘉宾是否已解锁，未解锁需要花瓣解锁
        UserEntity guestUser = userService.getUserByOid(request.getOid());
        LambdaQueryWrapper<GuestQualityEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GuestQualityEntity::getUid, user.getUid());
        wrapper.eq(GuestQualityEntity::getGuestUid, guestUser.getUid());
        GuestQualityEntity qualityEntity = guestQualityService.getOne(wrapper);
        if(qualityEntity != null){
            throw new LinfengException("");
        }

        int qualityPetal = Integer.parseInt(configBusinessService.getValue(Constant.QUALITY_PETAL));
        //更新用户额度
        appPetalService.deductPetal(user, qualityPetal, PetalRecordSubType.QUALITY.getValue());

        return null;
    }
}