package io.linfeng.business.question.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(description="问题分组响应对象")
public class QuestionGroupResponse implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 分组名称
	 */
	@ApiModelProperty(value = "分组名称")
	private String groupName;
	/**
	 * 问题列表
	 */
	@ApiModelProperty(value = "问题列表")
	private List<QuestionResponse> questionList;

}
