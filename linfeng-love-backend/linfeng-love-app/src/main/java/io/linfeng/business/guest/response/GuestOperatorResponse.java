package io.linfeng.business.guest.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="嘉宾操作响应对象")
public class GuestOperatorResponse implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 配对状态
	 */
	@ApiModelProperty(value = "配对状态")
	private Integer coupleStatus;

}
