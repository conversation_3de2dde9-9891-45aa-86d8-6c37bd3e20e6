package io.linfeng.business.user.service.impl;


import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.business.censor.service.AppCensorService;
import io.linfeng.business.user.request.UserMediaRequest;
import io.linfeng.business.user.response.UserMediaResponse;
import io.linfeng.business.user.service.AppUserMediaService;
import io.linfeng.common.enums.MediaType;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.executor.MissionEventExecutor;
import io.linfeng.love.mission.enums.MissionTarget;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.entity.UserMediaEntity;
import io.linfeng.love.user.service.UserMediaService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("appUserMediaService")
public class AppUserMediaServiceImpl implements AppUserMediaService {

    private final UserMediaService userMediaService;

    private final MissionEventExecutor missionEventExecutor;

    private final AppCensorService appCensorService;

    public AppUserMediaServiceImpl(UserMediaService userMediaService, MissionEventExecutor missionEventExecutor, AppCensorService appCensorService) {
        this.userMediaService = userMediaService;
        this.missionEventExecutor = missionEventExecutor;
        this.appCensorService = appCensorService;
    }

    @Override
    public List<UserMediaResponse> getUserMediaList(Integer uid, Integer mediaType) {
        LambdaQueryWrapper<UserMediaEntity> wrapper =new LambdaQueryWrapper<>();
        wrapper.eq(UserMediaEntity::getUid, uid);
        wrapper.eq(UserMediaEntity::getMediaType, mediaType);
        List<UserMediaEntity> userMediaList = userMediaService.list(wrapper);
        return ObjectMapperUtil.convert(userMediaList, UserMediaResponse.class);
    }

    @Override
    public void uploadUserMedia(UserEntity user, List<UserMediaRequest> requestList) {
        List<UserMediaEntity> userMediaList = new ArrayList<>();

        requestList.forEach(request -> {
            //图片审核
            appCensorService.censorImage(request.getUrl());
            UserMediaEntity userMediaEntity  = new UserMediaEntity();
            userMediaEntity.setUid(user.getUid());
            userMediaEntity.setMediaName(IdUtil.fastSimpleUUID());
            userMediaEntity.setMediaType(request.getMediaType());
            userMediaEntity.setUrl(request.getUrl());
            userMediaList.add(userMediaEntity);
        });

        userMediaService.saveBatch(userMediaList);
        LambdaQueryWrapper<UserMediaEntity> wrapper =new LambdaQueryWrapper<>();
        wrapper.eq(UserMediaEntity::getUid, user.getUid());
        wrapper.eq(UserMediaEntity::getMediaType, MediaType.IMAGE.getValue());
        missionEventExecutor.updateUserMissionTarget(user.getUid(), MissionTarget.UPLOAD_IMAGE, userMediaService.list(wrapper).size());

    }

    @Override
    public void deleteUserMedia(UserEntity user, UserMediaRequest request) {
        LambdaQueryWrapper<UserMediaEntity> wrapper =new LambdaQueryWrapper<>();
        wrapper.eq(UserMediaEntity::getUid,user.getUid());
        wrapper.eq(UserMediaEntity::getMediaName,request.getMediaName());
        wrapper.eq(UserMediaEntity::getMediaType,request.getMediaType());
        userMediaService.remove(wrapper);
    }
}