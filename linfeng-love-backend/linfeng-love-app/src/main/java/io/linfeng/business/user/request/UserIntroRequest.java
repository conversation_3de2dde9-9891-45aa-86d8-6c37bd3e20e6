package io.linfeng.business.user.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="用户介绍请求对象")
public class UserIntroRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 配置id
	 */
	@ApiModelProperty(value = "配置id")
	private Integer introId;
	/**
	 * 填写内容
	 */
	@ApiModelProperty(value = "填写内容")
	private String content;

}
