/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245/793326982
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.guest.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.guest.request.GuestOperatorRequest;
import io.linfeng.business.guest.request.RecommendQueryRequest;
import io.linfeng.business.guest.request.VicinityQueryRequest;
import io.linfeng.business.guest.response.*;
import io.linfeng.business.guest.service.AppGuestService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * 嘉宾信息Api
 */
@RestController
@RequestMapping("/app/guest")
@Api(tags = "嘉宾信息Api")
public class AppGuestController {

    private final AppGuestService appGuestService;

    public AppGuestController(AppGuestService appGuestService) {
        this.appGuestService = appGuestService;
    }

    /**
     * 校验并初始化推荐嘉宾
     * @param user 登录用户
     */
    @Login
    @GetMapping ("/recommend/checkAndInit")
    @ApiOperation("推荐嘉宾初始化")
    public R checkAndInitRecommend(@ApiIgnore @LoginUser UserEntity user){
        appGuestService.checkAndInitRecommend(user);
        return R.ok();
    }

    /**
     * 查询推荐嘉宾列表
     * @param user 登录用户
     * @param request 查询条件
     * @return 推荐嘉宾
     */
    @Login
    @PostMapping ("/recommend/list")
    @ApiOperation("查询推荐嘉宾列表")
    public Result<List<GuestSimpleResponse>> getRecommendGuestList(@ApiIgnore @LoginUser UserEntity user, @RequestBody RecommendQueryRequest request){
        List<GuestSimpleResponse> responseList = appGuestService.getRecommendGuestList(user, request);
        return new Result<List<GuestSimpleResponse>>().ok(responseList);
    }

    /**
     * 查询附近嘉宾列表
     * @param user 登录用户
     * @param request 查询条件
     * @return 附近嘉宾
     */
    @Login
    @PostMapping ("/vicinity/list")
    @ApiOperation("查询附近嘉宾列表")
    public Result<IPage<GuestSimpleResponse>> getVicinityGuestList(@ApiIgnore @LoginUser UserEntity user, @RequestBody VicinityQueryRequest request){
        IPage<GuestSimpleResponse> responsePage = appGuestService.getVicinityGuestList(user, request);
        return new Result<IPage<GuestSimpleResponse>>().ok(responsePage);
    }
    /**
     * 获取最新的推荐嘉宾
     * @param user 登录用户
     * @return 最新推荐嘉宾
     */
    @Login
    @GetMapping ("/recommend/latest")
    @ApiOperation("获取最新推荐嘉宾")
    public Result<GuestResponse> getLatestRecommend(@ApiIgnore @LoginUser UserEntity user){
        GuestResponse latestRecommendGuest = appGuestService.getLatestRecommend(user);
        return new Result<GuestResponse>().ok(latestRecommendGuest);
    }

    /**
     * 新增一个推荐嘉宾
     * @param user 登录用户
     */
    @Login
    @PostMapping ("/recommend/new")
    @ApiOperation("新增一个推荐嘉宾")
    public R newRecommend(@ApiIgnore @LoginUser UserEntity user){
        appGuestService.newRecommend(user);
        return R.ok();
    }

    /**
     * 推荐嘉宾操作（喜欢/不喜欢）
     * @param user 登录用户
     * @param request 操作请求
     * @return 配对结果
     */
    @Login
    @PostMapping("/recommend/operator")
    @ApiOperation("推荐嘉宾配对")
    public Result<GuestOperatorResponse> recommendOperator(@ApiIgnore @LoginUser UserEntity user, @RequestBody GuestOperatorRequest request){
        GuestOperatorResponse guestOperatorResponse = appGuestService.recommendOperator(user, request);
        return new Result<GuestOperatorResponse>().ok(guestOperatorResponse);
    }

    /**
     * 查询历史推荐嘉宾列表
     * @param user 登录用户
     * @return 历史推荐嘉宾列表
     */
    @Login
    @GetMapping ("/recommend/history")
    @ApiOperation("历史推荐嘉宾列表")
    public Result<GuestHistoryResponse> recommendHistory(@ApiIgnore @LoginUser UserEntity user){
        GuestHistoryResponse historyRecommend = appGuestService.getHistoryRecommend(user);
        return new Result<GuestHistoryResponse>().ok(historyRecommend);
    }

    /**
     * 查询嘉宾主页信息
     * @param user 登录用户
     * @param oid 嘉宾oid
     * @return 嘉宾主页信息
     */
    @Login
    @GetMapping ("/detail")
    @ApiOperation("查询嘉宾主页信息")
    public Result<GuestResponse> detail(@ApiIgnore @LoginUser UserEntity user, String oid){
        GuestResponse latestRecommendGuest = appGuestService.getGuestDetail(user, oid);
        return new Result<GuestResponse>().ok(latestRecommendGuest);
    }

    /**
     * 获取嘉宾汇总信息
     * @param user 登录用户
     * @return 嘉宾汇总信息
     */
    @Login
    @GetMapping ("/summary")
    @ApiOperation("嘉宾汇总信息")
    public Result<GuestSummaryResponse> summary(@ApiIgnore @LoginUser UserEntity user){
        GuestSummaryResponse guestSummaryResponse = appGuestService.getGuestSummary(user);
        return new Result<GuestSummaryResponse>().ok(guestSummaryResponse);
    }

    /**
     * 查询我喜欢的列表
     * @param user 登录用户
     * @return 我喜欢的列表
     */
    @Login
    @GetMapping ("/like/list")
    @ApiOperation("我喜欢的列表")
    public Result<List<GuestSimpleResponse>> likeList(@ApiIgnore @LoginUser UserEntity user){
        List<GuestSimpleResponse> guestList = appGuestService.getLikeList(user);
        return new Result<List<GuestSimpleResponse>>().ok(guestList);
    }
    /**
     * 查询喜欢我的列表
     * @param user 登录用户
     * @return 喜欢我的列表
     */
    @Login
    @GetMapping ("/likeMe/list")
    @ApiOperation("喜欢我的列表")
    public Result<List<GuestSimpleResponse>> likeMeList(@ApiIgnore @LoginUser UserEntity user){
        List<GuestSimpleResponse> guestList = appGuestService.getLikeMeList(user);
        return new Result<List<GuestSimpleResponse>>().ok(guestList);
    }

    /**
     * 查看我的列表
     * @param user 登录用户
     * @return 查看我的列表
     */
    @Login
    @GetMapping ("/lookMe/list")
    @ApiOperation("查看我的列表")
    public Result<List<GuestSimpleResponse>> lookMe(@ApiIgnore @LoginUser UserEntity user){
        List<GuestSimpleResponse> guestList = appGuestService.getLookMeList(user);
        return new Result<List<GuestSimpleResponse>>().ok(guestList);
    }

    /**
     * 嘉宾礼物信息
     * @param oid 用户oid
     * @return 嘉宾礼物信息
     */
    @Login
    @GetMapping ("/giftInfo")
    @ApiOperation("嘉宾礼物信息")
    public Result<GuestGiftInfoResponse> getUserGiftInfo(String oid){
        GuestGiftInfoResponse guestGiftInfoResponse = appGuestService.getGuestGiftInfo(oid);
        return new Result<GuestGiftInfoResponse>().ok(guestGiftInfoResponse);
    }

}
