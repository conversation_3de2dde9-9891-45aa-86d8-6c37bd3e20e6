package io.linfeng.business.bottle.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="漂流品打招呼请求对象")
public class BottleHelloRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 嘉宾oid
	 */
	@ApiModelProperty(value = "嘉宾oid")
	private String oid;

	/**
	 * 内容
	 */
	@ApiModelProperty(value = "内容")
	private String content;

}
