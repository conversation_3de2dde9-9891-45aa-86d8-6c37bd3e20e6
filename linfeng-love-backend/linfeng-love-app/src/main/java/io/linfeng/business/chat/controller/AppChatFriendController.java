/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.chat.controller;


import io.linfeng.business.chat.request.ChatFriendRequest;
import io.linfeng.business.chat.response.ChatFriendResponse;
import io.linfeng.business.chat.service.AppChatFriendService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 *  聊天好友Api
 */
@RestController
@RequestMapping("/app/chat/friend")
@Api(tags = "聊天好友Api")
public class AppChatFriendController {

    private final AppChatFriendService appChatFriendService;


    public AppChatFriendController(AppChatFriendService appChatFriendService) {
        this.appChatFriendService = appChatFriendService;
    }

    /**
     * 好友列表
     * @param user 登录用户
     * @return 好友列表
     */
    @Login
    @GetMapping("/list")
    @ApiOperation("查询好友列表")
    public Result<List<ChatFriendResponse>> list(@ApiIgnore @LoginUser UserEntity user){
        List<ChatFriendResponse> chatFriendList = appChatFriendService.getFriendList(user);
        return new Result<List<ChatFriendResponse>>().ok(chatFriendList);
    }

    /**
     * 删除好友
     * @param user 用户
     * @param request 删除亲贵
     * @return 删除结果
     */
    @Login
    @PostMapping("/delete")
    @ApiOperation("删除好友")
    public R delete(@ApiIgnore @LoginUser UserEntity user, @RequestBody ChatFriendRequest request){
        appChatFriendService.deleteFriend(user, request.getOid());
        return R.ok();
    }

    /**
     * 黑名单操作
     * @param user 登录用户
     * @param request 操作请求
     * @return 操作结果
     */
    @Login
    @PostMapping("/blackOperator")
    @ApiOperation("黑名单操作")
    public R blackOperator(@ApiIgnore @LoginUser UserEntity user, @RequestBody ChatFriendRequest request){
        appChatFriendService.updateBlackStatus(user, request);
        return R.ok();
    }
}
