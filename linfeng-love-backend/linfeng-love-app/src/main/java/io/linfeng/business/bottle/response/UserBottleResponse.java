package io.linfeng.business.bottle.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="用户漂流瓶响应")
public class UserBottleResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 剩余次数
	 */
	@ApiModelProperty(value = "剩余次数")
	private Integer residuePickTimes;


}
