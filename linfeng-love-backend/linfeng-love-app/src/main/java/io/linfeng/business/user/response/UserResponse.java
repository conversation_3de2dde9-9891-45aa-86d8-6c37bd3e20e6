package io.linfeng.business.user.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
@ApiModel(description="用户信息响应对象")
public class UserResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * oid
	 */
	@ApiModelProperty(value = "oid")
	private String oid;

	/**
	 * 手机号
	 */
	@ApiModelProperty(value = "手机号")
	private String mobile;
	/**
	 * 用户名
	 */
	@ApiModelProperty(value = "用户名")
	private String userName;
	/**
	 * 头像
	 */
	@ApiModelProperty(value = "头像")
	private String avatar;
	/**
	 * 最后更新的头像
	 */
	@ApiModelProperty(value = "最后更新的头像")
	private String lastAvatar;
	/**
	 * 性别(0未知，1男，2女)
	 */
	@ApiModelProperty(value = "性别(字典)")
	private Integer gender;
	/**
	 * 性别(字典翻译)
	 */
	@ApiModelProperty(value = "性别(字典翻译)")
	private String genderText;
	/**
	 * 生日
	 */
	@ApiModelProperty(value = "生日")
	private String birthday;
	/**
	 * 出生年份
	 */
	@ApiModelProperty(value = "出生年份")
	private String birthdayYear;
	/**
	 * 年龄
	 */
	@ApiModelProperty(value = "年龄")
	private Integer age;
	/**
	 * 星座
	 */
	@ApiModelProperty(value = "星座")
	private String constellation;
	/**
	 * 身高
	 */
	@ApiModelProperty(value = "身高")
	private Integer stature;
	/**
	 * 体重
	 */
	@ApiModelProperty(value = "体重")
	private Integer weight;
	/**
	 * 家乡省份
	 */
	@ApiModelProperty(value = "家乡省份")
	private String homeProvince;
	/**
	 * 家乡城市
	 */
	@ApiModelProperty(value = "家乡城市")
	private String homeCity;
	/**
	 * 居住省份
	 */
	@ApiModelProperty(value = "居住省份")
	private String livingProvince;
	/**
	 * 居住城市
	 */
	@ApiModelProperty(value = "居住城市")
	private String livingCity;
	/**
	 * 婚姻状态（字典）
	 */
	@ApiModelProperty(value = "婚姻状态（字典）")
	private Integer marriage;
	/**
	 * 婚姻状态(字典翻译)
	 */
	@ApiModelProperty(value = "婚姻状态(字典翻译)")
	private String marriageText;
	/**
	 * 毕业院校
	 */
	@ApiModelProperty(value = "毕业院校")
	private String school;
	/**
	 * 最高学历（字典）
	 */
	@ApiModelProperty(value = "最高学历（字典）")
	private Integer education;
	/**
	 * 最高学历(字典翻译)
	 */
	@ApiModelProperty(value = "最高学历(字典翻译)")
	private String educationText;
	/**
	 * 行业/职业（字典）
	 */
	@ApiModelProperty(value = "行业/职业（字典）")
	private Integer job;
	/**
	 * 行业/职业(字典翻译)
	 */
	@ApiModelProperty(value = "行业/职业(字典翻译)")
	private String jobText;
	/**
	 * 年薪（字典）
	 */
	@ApiModelProperty(value = "年薪（字典）")
	private Integer salary;
	/**
	 * 年薪(字典翻译)
	 */
	@ApiModelProperty(value = "年薪(字典翻译)")
	private String salaryText;
	/**
	 * 实名状态
	 */
	@ApiModelProperty(value = "实名状态")
	private Integer realNameStatus;
	/**
	 * 头像审核状态
	 */
	@ApiModelProperty(value = "头像审核状态")
	private Integer avatarStatus;
	/**
	 * 基本信息评分
	 */
	@ApiModelProperty(value = "基本信息评分")
	private Integer infoScore;
	/**
	 * 人脸认证状态
	 */
	@ApiModelProperty(value = "人脸认证状态")
	private Integer faceStatus;
	/**
	 * 学历认证状态
	 */
	@ApiModelProperty(value = "学历认证状态")
	private Integer educationStatus;
	/**
	 * 工作认证状态
	 */
	@ApiModelProperty(value = "工作认证状态")
	private Integer jobStatus;

	/**
	 * 花瓣余额（限时）
	 */
	@ApiModelProperty(value = "花瓣余额（限时）")
	private Integer petalLimit;
	/**
	 * 花瓣余额（永久）
	 */
	@ApiModelProperty(value = "花瓣余额（永久）")
	private Integer petalForever;
	/**
	 * 是否为会员 0普通用户 1会员
	 */
	@ApiModelProperty(value = "是否为会员")
	private Integer vip;
	/**
	 * 会员过期时间
	 */
	@ApiModelProperty(value = "会员过期时间")
	private Date vipExpireTime;

	/**
	 * 剩余免费打招呼次数
	 */
	@ApiModelProperty(value = "剩余免费打招呼次数")
	private Integer freeHelloNum;
	/**
	 * 最后登录ip
	 */
	@ApiModelProperty(value = "最后登录ip")
	private String lastLoginIp;
	/**
	 * 个性签名
	 */
	@ApiModelProperty(value = "个性签名")
	private String signature;

	/**
	 * 登录状态
	 */
	@ApiModelProperty(value = "登录状态")
	private Integer onlineStatus;

	/**
	 * 登录状态
	 */
	@ApiModelProperty(value = "登录状态")
	private Date lastOfflineTime;

	/**
	 * 用户介绍
	 */
	@ApiModelProperty(value = "用户介绍")
	private List<UserIntroResponse> introList;

	/**
	 * 用户附件
	 */
	@ApiModelProperty(value = "用户附件")
	private List<UserMediaResponse> userMediaList;

	/**
	 * 用户标签
	 */
	@ApiModelProperty(value = "用户标签")
	private List<UserTagResponse> userTagList;
	/**
	 * 语音持续时间
	 */
	@ApiModelProperty(value = "语音持续时间")
	private Integer voiceDuration;
	/**
	 * 语音地址
	 */
	@ApiModelProperty(value = "语音地址")
	private String voiceUrl;
	/**
	 * 背景图片
	 */
	@ApiModelProperty(value = "背景图片")
	private String backImage;

}
