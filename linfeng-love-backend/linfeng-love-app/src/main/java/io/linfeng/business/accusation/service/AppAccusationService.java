/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.accusation.service;


import io.linfeng.business.accusation.request.AccusationRequest;
import io.linfeng.business.accusation.response.AccusationResponse;
import io.linfeng.love.user.entity.UserEntity;

import java.util.List;

/**
 * 举报服务
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
public interface AppAccusationService {

    /**
     * 举报提交
     * @param user 登录用户
     * @param request 举报请求
     */
    Integer submit(UserEntity user, AccusationRequest request);

    /**
     * 举报列表
     * @param user 用户
     * @param status 状态
     * @return 举报列表
     */
    List<AccusationResponse> getAccusationList(UserEntity user, Integer status);

    /**
     * 举报详情
     * @param user 用户
     * @param id 举报id
     * @return 举报详情
     */
    AccusationResponse getAccusationDetail(UserEntity user, Integer id);
}

