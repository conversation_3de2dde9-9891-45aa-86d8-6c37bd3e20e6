package io.linfeng.business.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.user.request.UserMessageRequest;
import io.linfeng.business.user.response.UserMessageDetailResponse;
import io.linfeng.business.user.response.UserMessageInteractResponse;
import io.linfeng.business.user.response.UserMessageResponse;
import io.linfeng.love.bottle.entity.BottleMessageEntity;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.entity.UserMessageEntity;
import io.linfeng.love.user.entity.UserMessageInteractEntity;

import java.util.List;
import java.util.Map;

/**
 * 用户消息业务服务
 *
 * <AUTHOR>
 * @date 2023-09-06 16:22:14
 */
public interface AppUserMessageService {

    /**
     * 获取用户消息列表
     * @param uid 用户id
     * @return 用户消息列表
     */
    List<UserMessageResponse> getUserMessageList(Integer uid);

    /**
     * 获取用户消息详情列表
     * @param uid 用户id
     * @param request 请求
     * @return 用户消息详情列表
     */
    IPage<UserMessageDetailResponse> getUserMessageDetailList(Integer uid, UserMessageRequest request);

    /**
     * 消息已读
     * @param user 用户
     * @param request 请求
     */
    void readMessage(UserEntity user, UserMessageRequest request);

    /**
     * 获取互动消息列表
     * @param uid 用户id
     * @param request 查询请求
     * @return 互动消息列表
     */
    IPage<UserMessageInteractResponse> getUserMessageInteractList(Integer uid, UserMessageRequest request);

    /**
     * 发送漂流瓶消息
     * @param bottleMessage 漂流瓶消息
     */
    void sendBottleMessage(BottleMessageEntity bottleMessage);

    /**
     * 发送动态消息
     * @param userMessageInteractEntity 动态消息
     */
    void sendUserInteractMessage(UserMessageInteractEntity userMessageInteractEntity);
}

