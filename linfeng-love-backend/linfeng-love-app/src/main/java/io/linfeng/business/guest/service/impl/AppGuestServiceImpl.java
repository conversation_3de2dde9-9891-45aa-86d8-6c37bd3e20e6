package io.linfeng.business.guest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.linfeng.business.chat.service.AppChatFriendService;
import io.linfeng.business.guest.request.GuestOperatorRequest;
import io.linfeng.business.guest.request.RecommendQueryRequest;
import io.linfeng.business.guest.request.VicinityQueryRequest;
import io.linfeng.business.guest.response.*;
import io.linfeng.business.guest.service.AppGuestService;
import io.linfeng.business.moment.response.MomentMediaResponse;
import io.linfeng.business.moment.service.AppMomentService;
import io.linfeng.business.petal.service.AppPetalService;
import io.linfeng.business.user.response.UserGiftResponse;
import io.linfeng.business.user.response.UserIntroResponse;
import io.linfeng.business.user.response.UserMediaResponse;
import io.linfeng.business.user.response.UserTagResponse;
import io.linfeng.business.user.service.*;
import io.linfeng.common.enums.MediaType;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.*;
import io.linfeng.love.chat.service.ChatFriendService;
import io.linfeng.love.common.enums.CommonStatus;
import io.linfeng.love.config.service.ConfigBusinessService;
import io.linfeng.love.config.service.DictItemService;
import io.linfeng.love.guest.dto.response.GuestSimpleResponseDTO;
import io.linfeng.love.guest.entity.GuestCoupleEntity;
import io.linfeng.love.guest.entity.GuestRecommendEntity;
import io.linfeng.love.guest.entity.GuestVisitEntity;
import io.linfeng.love.guest.enums.ConcernStatus;
import io.linfeng.love.guest.enums.CoupleStatus;
import io.linfeng.love.guest.enums.RecommendOperatorStatus;
import io.linfeng.love.guest.service.GuestCoupleService;
import io.linfeng.love.guest.service.GuestQualityService;
import io.linfeng.love.guest.service.GuestRecommendService;
import io.linfeng.love.guest.service.GuestVisitService;
import io.linfeng.love.moment.entity.MomentEntity;
import io.linfeng.love.moment.enums.MomentPrivacy;
import io.linfeng.love.moment.service.MomentService;
import io.linfeng.love.petal.entity.PetalRecommendEntity;
import io.linfeng.love.petal.enums.PetalRecordSubType;
import io.linfeng.love.petal.service.PetalRecommendService;
import io.linfeng.love.user.entity.*;
import io.linfeng.love.user.service.*;
import io.linfeng.push.client.enums.InteractMessageDataType;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


@Service("appGuestService")
@AllArgsConstructor
public class AppGuestServiceImpl implements AppGuestService {

    private final ConfigBusinessService configBusinessService;

    private final GuestRecommendService guestRecommendService;

    private final GuestCoupleService guestCoupleService;

    private final GuestVisitService guestVisitService;

    private final UserConcernService userConcernService;

    private final UserPreferencesService userPreferencesService;

    private final UserService userService;

    private final AppUserMediaService appUserMediaService;

    private final AppUserIntroService appUserIntroService;

    private final MomentService momentService;

    private final AppUserTagService appUserTagService;

    private final AppPetalService appPetalService;

    private final AppMomentService appMomentService;

    private final DictItemService dictItemService;

    private final PetalRecommendService petalRecommendService;

    private final AppChatFriendService appChatFriendService;

    private final UserMessageInteractService userMessageInteractService;

    private final AppUserMessageService appUserMessageService;

    private final UserLoginService userLoginService;

    private final ChatFriendService chatFriendService;

    private final AppUserGiftService appUserGiftService;

    private final GuestQualityService guestQualityService;

    @Override
    public void checkAndInitRecommend(UserEntity user){
        Integer refreshHour = Integer.parseInt(configBusinessService.getValue(Constant.GUEST_REFRESH_HOUR));
        Calendar toDayRefreshTime = Calendar.getInstance();
        toDayRefreshTime.set(Calendar.HOUR_OF_DAY, refreshHour);

        Calendar yesterdayRefreshTime = Calendar.getInstance();
        yesterdayRefreshTime.set(Calendar.HOUR_OF_DAY, refreshHour);
        yesterdayRefreshTime.add(Calendar.DAY_OF_MONTH, -1);
        //下面两个条件分开来写，帮助理解业务
        //推荐时间在今天刷新时间之后的不再刷新
        if(user.getRecommendTime() != null && user.getRecommendTime().compareTo(toDayRefreshTime.getTime()) > 0){
            return;
        }
        //推荐时间在昨天刷新之后并且当前时间还没有到今日刷新时间的不刷新
        if(user.getRecommendTime() != null && user.getRecommendTime().compareTo(yesterdayRefreshTime.getTime()) > 0 && DateUtil.nowDateTime().compareTo(toDayRefreshTime.getTime()) < 0){
            return;
        }
        //校验还未操作的推荐嘉宾数量
        LambdaQueryWrapper<GuestRecommendEntity> wrapper =new LambdaQueryWrapper<>();
        wrapper.eq(GuestRecommendEntity::getUid,user.getUid());
        wrapper.eq(GuestRecommendEntity::getStatus, RecommendOperatorStatus.DEFAULT.getValue());
        int unOperateCount = guestRecommendService.count(wrapper);
        int recommendCount = 0;

        //后管配置VIP推送嘉宾的数量和普通用户推送嘉宾的数量
        if(user.getVip() == Constant.VIP_USER){
            recommendCount = Integer.parseInt(configBusinessService.getValue(Constant.VIP_RECOMMEND));
        }else{
            recommendCount = Integer.parseInt(configBusinessService.getValue(Constant.COMMON_RECOMMEND));
        }

        //如果未操作的数量大于等于每日推荐的数量，就不再新增推荐
        if(unOperateCount >= recommendCount){
            //刷新更新推荐的时间
            user.setRecommendTime(DateUtil.nowDateTime());
            user.setUpdateTime(DateUtil.nowDateTime());
            userService.updateAndDeleteCache(user);
            return;
        }

        //新增还需再添加嘉宾的数量
        int addCount = recommendCount - unOperateCount;

        recommendNewGuest(user, addCount);

        //刷新更新推荐的时间
        user.setRecommendTime(DateUtil.nowDateTime());
        user.setUpdateTime(DateUtil.nowDateTime());
        userService.updateAndDeleteCache(user);
    }

    @Override
    @Transactional
    public List<GuestSimpleResponse> getRecommendGuestList(UserEntity user, RecommendQueryRequest request) {
        //如果是重置刷新，就删除当前未操作过的推荐记录
        if(request.getType() == 1){
            guestRecommendService.deleteUnOperatorRecommend(user.getUid());
        }

        //添加过滤条件：默认配对反向性别
        Map<String, Object> params = new HashMap<>();
        if(user.getGender() == Constant.MAN){
            params.put("gender", Constant.WOMAN);
        }else{
            params.put("gender", Constant.MAN);
        }

        //添加过滤条件：客户设置的匹配规则
        LambdaQueryWrapper<UserPreferencesEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(UserPreferencesEntity::getUid, user.getUid());
        UserPreferencesEntity userPreferences = userPreferencesService.getOne(lambdaQueryWrapper);
        params.put("livingCity", user.getLivingCity());
        params.put("homeProvince", user.getHomeProvince());
        params.put("limit", 10);

        List<UserEntity> recommendGuestList = guestRecommendService.getRecommendGuestList(userPreferences, params);

        List<Integer> uidList = recommendGuestList.stream().map(userEntity -> userEntity.getUid()).collect(Collectors.toList());

        if(uidList == null || uidList.size() == 0){
            return new ArrayList<>();
        }

        List<GuestSimpleResponseDTO> guestSimpleDTOList = guestRecommendService.getRecommendGuestList(uidList, user.getUid());
        List<GuestSimpleResponse> guestSimpleList = ObjectMapperUtil.convert(guestSimpleDTOList, GuestSimpleResponse.class);
        guestSimpleList.forEach(guestSimple -> {
            if (!StringUtil.isEmpty(guestSimple.getJob())) {
                guestSimple.setJobText(dictItemService.getItemName(Constant.DICT_JOB, guestSimple.getJob()));
            }
        });

        //添加推荐记录
        List<GuestRecommendEntity> recommendList = new ArrayList<>();
        recommendGuestList.forEach(recommendGuest -> {
            GuestRecommendEntity recommend = new GuestRecommendEntity();
            recommend.setUid(user.getUid());
            recommend.setGuestUid(recommendGuest.getUid());
            recommend.setStatus(RecommendOperatorStatus.DEFAULT.getValue());
            recommend.setCreateTime(DateUtil.nowDateTime());
            recommendList.add(recommend);
        });
        guestRecommendService.saveBatch(recommendList);

        return guestSimpleList;
    }

    @Override
    @Transactional
    public void newRecommend(UserEntity user) {

        int petaRecommend = Integer.parseInt(configBusinessService.getValue(Constant.PETAL_RECOMMEND));
        //更新用户额度
        appPetalService.deductPetal(user, petaRecommend, PetalRecordSubType.RECOMMEND.getValue());

        //新增一个推荐嘉宾
        List<GuestRecommendEntity> recommendList = recommendNewGuest(user, 1);

        //保存推荐记录
        PetalRecommendEntity petalRecommend = new PetalRecommendEntity();
        petalRecommend.setUid(user.getUid());
        petalRecommend.setGuestUid(recommendList.get(0).getGuestUid());
        petalRecommend.setPetalAmount(petaRecommend);
        petalRecommend.setCreateTime(DateUtil.nowDateTime());
        petalRecommendService.save(petalRecommend);

    }

    @Override
    public IPage<GuestSimpleResponse> getVicinityGuestList(UserEntity user, VicinityQueryRequest request) {

        LambdaQueryWrapper<UserLoginEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserLoginEntity::getUid, user.getUid());
        UserLoginEntity userLoginEntity = userLoginService.getOne(wrapper);
        IPage<GuestSimpleResponseDTO> dtoPage = new Page<>(request.getPageNum(),request.getPageSize());
        Integer gender = Constant.MAN;
        if(user.getGender() == Constant.MAN){
            gender = Constant.WOMAN;
        }
        dtoPage = guestRecommendService.selectVicinityGuestPage(dtoPage, user.getUid(), userLoginEntity.getLongitude(), userLoginEntity.getLatitude(), gender);
        IPage<GuestSimpleResponse> page = ObjectMapperUtil.convert(dtoPage, GuestSimpleResponse.class);
        return page;
    }

    @Override
    public GuestGiftInfoResponse getGuestGiftInfo(String oid) {
        GuestGiftInfoResponse response = new GuestGiftInfoResponse();
        UserEntity guestUser = userService.getUserByOid(oid);
        List<UserGiftResponse> giftList = appUserGiftService.getUserGiftList(guestUser);
        Long giftLightNumber = giftList.stream().filter(userGiftResponse -> userGiftResponse.getNumber() != null).count();
        response.setLightNumber(giftLightNumber.intValue());
        response.setUserGiftList(giftList);
        response.setAvatar(guestUser.getAvatar());
        response.setUserName(guestUser.getUserName());
        return response;
    }

    @Override
    public GuestResponse getLatestRecommend(UserEntity user) {

        GuestRecommendEntity latestGuestRecommend = this.guestRecommendService.getLatestRecommend(user.getUid());

        if(latestGuestRecommend == null){
            return null;
        }

        UserEntity guestUser = userService.getUserByUid(latestGuestRecommend.getGuestUid());

        //根据最新推荐嘉宾信息构造返回对象
        GuestResponse guestResponse = buildGuestResponse(user, guestUser);

        //添加访问记录
        saveGuestVisit(user.getUid(), guestUser.getUid());

        return guestResponse;
    }

    @Override
    @Transactional
    public GuestOperatorResponse recommendOperator(UserEntity user, GuestOperatorRequest request) {
        GuestOperatorResponse guestOperatorResponse = new GuestOperatorResponse();
        UserEntity guestUser = userService.getUserByOid(request.getOid());

        //更新推荐嘉宾为已操作
        LambdaQueryWrapper<GuestRecommendEntity> wrapper =new LambdaQueryWrapper<>();
        wrapper.eq(GuestRecommendEntity::getUid, user.getUid());
        wrapper.eq(GuestRecommendEntity::getGuestUid, guestUser.getUid());
        GuestRecommendEntity guestRecommendEntity = new GuestRecommendEntity();
        guestRecommendEntity.setStatus(request.getStatus());
        guestRecommendEntity.setUpdateTime(DateUtil.nowDateTime());
        guestRecommendService.update(guestRecommendEntity, wrapper);

        //选择不喜欢直接配对失败
        if(request.getStatus() == RecommendOperatorStatus.DISLIKE.getValue()){
            guestOperatorResponse.setCoupleStatus(CoupleStatus.FAILED.getValue());
            return guestOperatorResponse;
        }

        //选择喜欢进行配对校验
        Integer coupleStatus = guestCoupleCheck(user, guestUser);
        guestOperatorResponse.setCoupleStatus(coupleStatus);
        return guestOperatorResponse;
    }

    /**
     * 嘉宾配对校验
     * 如果选择的是喜欢就校验嘉宾是否也喜欢本人
     * 是就配对成功开启聊天引导
     * 否就进入等待配对的状态
     * @param user 客户
     * @param guestUser 嘉宾
     * @return 配对状态
     */
    private Integer guestCoupleCheck(UserEntity user, UserEntity guestUser){
        Integer coupleStatus = CoupleStatus.WAITING.getValue();
        GuestCoupleEntity guestCoupleEntity = new GuestCoupleEntity();
        guestCoupleEntity.setUid(user.getUid());
        guestCoupleEntity.setGuestUid(guestUser.getUid());
        guestCoupleEntity.setCreateTime(DateUtil.nowDateTime());

        LambdaQueryWrapper<GuestCoupleEntity> params =new LambdaQueryWrapper<>();
        params.eq(GuestCoupleEntity::getUid, guestUser.getUid());
        params.eq(GuestCoupleEntity::getGuestUid, user.getUid());
        GuestCoupleEntity otherCoupleEntity = guestCoupleService.getOne(params);

        if(otherCoupleEntity == null){
            guestCoupleEntity.setStatus(CoupleStatus.WAITING.getValue());
            sendHeartMessage(guestUser.getUid(), user);
        }else{
            guestCoupleEntity.setStatus(CoupleStatus.SUCCESS.getValue());
            otherCoupleEntity.setStatus(CoupleStatus.SUCCESS.getValue());
            otherCoupleEntity.setUpdateTime(DateUtil.nowDateTime());
            guestCoupleService.updateById(otherCoupleEntity);
            coupleStatus = CoupleStatus.SUCCESS.getValue();

            //建立好友关系
            appChatFriendService.buildFriendRelation(user, guestUser);
        }
        guestCoupleService.save(guestCoupleEntity);

        return coupleStatus;
    }

    private void sendHeartMessage(Integer guestUid, UserEntity user){
        UserMessageInteractEntity userMessageInteractEntity = new UserMessageInteractEntity();
        userMessageInteractEntity.setUid(guestUid);
        userMessageInteractEntity.setGuestOid(user.getOid());
        userMessageInteractEntity.setGuestUserName(user.getUserName());
        userMessageInteractEntity.setGuestAvatar(user.getAvatar());
        userMessageInteractEntity.setTitle(PushMessageConstant.HEART_TITLE);
        userMessageInteractEntity.setContent(PushMessageConstant.HEART_TITLE);
        userMessageInteractEntity.setLinkUrl(PushMessageConstant.GUEST_LINK_URL + user.getOid());
        userMessageInteractEntity.setDataType(InteractMessageDataType.HEART.getValue());
        userMessageInteractEntity.setSendTime(DateUtil.nowDateTime());
        userMessageInteractService.save(userMessageInteractEntity);
        appUserMessageService.sendUserInteractMessage(userMessageInteractEntity);
    }

    @Override
    public GuestHistoryResponse getHistoryRecommend(UserEntity user) {
        GuestHistoryResponse guestHistoryResponse = new GuestHistoryResponse();
        Integer queryDay = Integer.parseInt(configBusinessService.getValue(Constant.GUEST_QUERY_HISTORY_DAY));
        Map<String, Object> params = new HashMap<>();
        params.put("uid", user.getUid());
        params.put("queryDay", queryDay);

        List<GuestSimpleResponseDTO> guestSimpleResponseDTOList = guestRecommendService.getHistoryRecommend(params);
        List<GuestSimpleResponse> guestSimpleResponseList = ObjectMapperUtil.convert(guestSimpleResponseDTOList, GuestSimpleResponse.class);

        guestHistoryResponse.setGuestUserList(guestSimpleResponseList);

        //统计数量
        Integer recommendCount = guestSimpleResponseList.size();
        guestHistoryResponse.setRecommendCount(recommendCount);

        Integer likeCount = Math.toIntExact(guestSimpleResponseList.stream().filter(
                guestSimpleResponse -> guestSimpleResponse.getOperatorStatus() == RecommendOperatorStatus.LIKE.getValue()).count());
        guestHistoryResponse.setLikeCount(likeCount);

        Integer dislikeCount = Math.toIntExact(guestSimpleResponseList.stream().filter(
                guestSimpleResponse -> guestSimpleResponse.getOperatorStatus() == RecommendOperatorStatus.DISLIKE.getValue()).count());
        guestHistoryResponse.setDislikeCount(dislikeCount);

        return guestHistoryResponse;
    }
    @Override
    public GuestResponse getGuestDetail(UserEntity user, String oid) {
        UserEntity guestUser = userService.getUserByOid(oid);

        //根据最新推荐嘉宾信息构造返回对象
        GuestResponse guestResponse = buildGuestResponse(user, guestUser);

        //添加访问记录(访问自己的主页不添加)
        if(!user.getOid().equals(oid)){
            saveGuestVisit(user.getUid(), guestUser.getUid());
        }

        return guestResponse;
    }

    @Override
    public GuestSummaryResponse getGuestSummary(UserEntity user) {
        GuestSummaryResponse response = new GuestSummaryResponse();

        //看过我的
        response.setLookMeCount(guestVisitService.getLookMeUserCount(user.getUid()));

        //喜欢我的
        LambdaQueryWrapper<GuestCoupleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GuestCoupleEntity::getGuestUid,user.getUid());
        response.setLikeMeCount(guestCoupleService.count(wrapper));

        //我喜欢的
        wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GuestCoupleEntity::getUid,user.getUid());
        response.setLikeCount(guestCoupleService.count(wrapper));

        return response;
    }

    @Override
    public List<GuestSimpleResponse> getLikeList(UserEntity user) {
        Integer queryDay = Integer.parseInt(configBusinessService.getValue(Constant.GUEST_QUERY_LIKE_DAY));
        Map<String, Object> params = new HashMap<>();
        params.put("uid", user.getUid());
        params.put("queryDay", queryDay);
        List<GuestSimpleResponseDTO> guestSimpleResponseDTOList = guestCoupleService.getLikeList(params);
        return ObjectMapperUtil.convert(guestSimpleResponseDTOList, GuestSimpleResponse.class);
    }

    @Override
    public List<GuestSimpleResponse> getLikeMeList(UserEntity user) {
        Integer queryDay = Integer.parseInt(configBusinessService.getValue(Constant.GUEST_QUERY_LIKE_DAY));
        Map<String, Object> params = new HashMap<>();
        params.put("uid", user.getUid());
        params.put("queryDay", queryDay);
        List<GuestSimpleResponseDTO> guestSimpleResponseDTOList = guestCoupleService.getLikeMeList(params);
        return ObjectMapperUtil.convert(guestSimpleResponseDTOList, GuestSimpleResponse.class);
    }

    @Override
    public List<GuestSimpleResponse> getLookMeList(UserEntity user) {
        Integer queryDay = Integer.parseInt(configBusinessService.getValue(Constant.GUEST_QUERY_LOOK_DAY));
        Map<String, Object> params = new HashMap<>();
        params.put("uid", user.getUid());
        params.put("queryDay", queryDay);
        List<GuestSimpleResponseDTO> guestSimpleResponseDTOList = guestVisitService.getLookMeList(params);
        return ObjectMapperUtil.convert(guestSimpleResponseDTOList, GuestSimpleResponse.class);
    }

    /**
     * 构建嘉宾返回对象
     * @param guestUser 嘉宾用户
     * @return 嘉宾信息返回对象
     */
    private GuestResponse buildGuestResponse(UserEntity user, UserEntity guestUser){

        //用户基本信息
        GuestResponse response = transformerResponse(guestUser);

        //个人形象图片
        List<UserMediaResponse> userMediaList = appUserMediaService.getUserMediaList(guestUser.getUid(), MediaType.IMAGE.getValue());
        response.setUserMediaList(userMediaList);

        //个人介绍
        List<UserIntroResponse> userIntroList = appUserIntroService.getUserIntroList(guestUser.getUid());
        response.setIntroList(userIntroList);

        //个人动态数量
        LambdaQueryWrapper<MomentEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(MomentEntity::getUid, guestUser.getUid());
        lambdaQueryWrapper.eq(MomentEntity::getPrivacy, MomentPrivacy.NO.getValue());
        lambdaQueryWrapper.eq(MomentEntity::getStatus, CommonStatus.CHECKED.getValue());
        response.setMomentListLength(momentService.count(lambdaQueryWrapper));

        //个人动态照片，最多展示四张
        List<MomentMediaResponse> momentMediaList = appMomentService.getMomentImageList(guestUser.getUid());
        response.setMomentMediaList(momentMediaList);

        //个人收到礼物列表
        List<UserGiftResponse> userGiftList = appUserGiftService.getUserGiftList(guestUser);
        Long giftLightNumber = userGiftList.stream().filter(userGiftResponse -> userGiftResponse.getNumber() != null).count();
        response.setUserGiftList(userGiftList.subList(0, 5));
        response.setGiftLightNumber(giftLightNumber.intValue());

        //个人标签
        List<UserTagResponse> tagList = appUserTagService.getUserTagList(guestUser.getUid());
        response.setUserTagList(tagList);

        //关注状态
        LambdaQueryWrapper<UserConcernEntity> concernWrapper = Wrappers.lambdaQuery();
        concernWrapper.eq(UserConcernEntity::getUid, user.getUid());
        concernWrapper.eq(UserConcernEntity::getGuestUid, guestUser.getUid());
        UserConcernEntity userConcernEntity = userConcernService.getOne(concernWrapper);
        if(userConcernEntity != null && userConcernEntity.getStatus() == ConcernStatus.CONCERN.getValue()){
            response.setConcernFlag(ConcernStatus.CONCERN.getValue());
        }else {
            response.setConcernFlag(ConcernStatus.CANCEL.getValue());
        }

        //配对状态
        LambdaQueryWrapper<GuestCoupleEntity> coupleWrapper = Wrappers.lambdaQuery();
        coupleWrapper.eq(GuestCoupleEntity::getUid, user.getUid());
        coupleWrapper.eq(GuestCoupleEntity::getGuestUid, guestUser.getUid());
        GuestCoupleEntity guestCoupleEntity = guestCoupleService.getOne(coupleWrapper);
        if(guestCoupleEntity != null){
            response.setCoupleStatus(guestCoupleEntity.getStatus());
        }else {
            response.setCoupleStatus(CoupleStatus.FAILED.getValue());
        }

        //好友关系
        response.setFriendStatus(chatFriendService.checkFriendStatus(user.getUid(), guestUser.getUid()));

        return response;
    }

    /**
     * 添加访问记录
     * @param uid 客户uid
     * @param guestUid 嘉宾uid
     */
    private void saveGuestVisit(Integer uid, Integer guestUid){
        GuestVisitEntity guestVisitEntity = new GuestVisitEntity();
        guestVisitEntity.setUid(uid);
        guestVisitEntity.setGuestUid(guestUid);
        guestVisitEntity.setCreateTime(DateUtil.nowDateTime());
        guestVisitService.save(guestVisitEntity);
    }

    /**
     * 转化对象
     * @param userEntity 用户
     * @return 用户响应
     */
    private GuestResponse transformerResponse(UserEntity userEntity){
        GuestResponse response = new GuestResponse();
        BeanUtils.copyProperties(userEntity, response);
        if (!StringUtil.isEmpty(userEntity.getGender())) {
            response.setGender(userEntity.getGender());
            response.setGenderText(dictItemService.getItemName(Constant.DICT_GENDER, userEntity.getGender()));
        }
        if (!StringUtil.isEmpty(userEntity.getBirthday())) {
            response.setBirthdayYear(userEntity.getBirthday().substring(2,4));
        }
        if (!StringUtil.isEmpty(userEntity.getMarriage())) {
            response.setMarriageText(dictItemService.getItemName(Constant.DICT_MARRIAGE, userEntity.getMarriage()));
        }
        if (!StringUtil.isEmpty(userEntity.getEducation())) {
            response.setEducationText(dictItemService.getItemName(Constant.DICT_EDUCATION, userEntity.getEducation()));
        }
        if (!StringUtil.isEmpty(userEntity.getJob())) {
            response.setJobText(dictItemService.getItemName(Constant.DICT_JOB, userEntity.getJob()));
        }
        if (!StringUtil.isEmpty(userEntity.getSalary())) {
            response.setSalaryText(dictItemService.getItemName(Constant.DICT_SALARY, userEntity.getSalary()));
        }
        return response;
    }

    /**
     * 根据用户偏好等条件推荐新的嘉宾
     * @param user 用户
     * @param addCount 新增推荐嘉宾的数量
     */
    private List<GuestRecommendEntity> recommendNewGuest(UserEntity user, int addCount){

        //添加过滤条件：默认配对反向性别
        Map<String, Object> params = new HashMap<>();
        if(user.getGender() == Constant.MAN){
            params.put("gender", Constant.WOMAN);
        }else{
            params.put("gender", Constant.MAN);
        }

        //添加过滤条件：客户设置的匹配规则
        LambdaQueryWrapper<UserPreferencesEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(UserPreferencesEntity::getUid, user.getUid());
        UserPreferencesEntity userPreferences = userPreferencesService.getOne(lambdaQueryWrapper);
        params.put("livingCity", user.getLivingCity());
        params.put("homeProvince", user.getHomeProvince());
        params.put("limit", addCount);

        List<UserEntity> recommendGuestList = guestRecommendService.getRecommendGuestList(userPreferences, params);

        if(recommendGuestList == null || recommendGuestList.size() == 0){
            throw new LinfengException("找不到合适的嘉宾，放宽匹配条件再尝试");
        }

        List<GuestRecommendEntity> recommendList = new ArrayList<>();
        recommendGuestList.forEach(recommendGuest -> {
            GuestRecommendEntity recommend = new GuestRecommendEntity();
            recommend.setUid(user.getUid());
            recommend.setGuestUid(recommendGuest.getUid());
            recommend.setStatus(RecommendOperatorStatus.DEFAULT.getValue());
            recommend.setCreateTime(DateUtil.nowDateTime());
            recommendList.add(recommend);
        });
        guestRecommendService.saveBatch(recommendList);
        return recommendList;
    }

}