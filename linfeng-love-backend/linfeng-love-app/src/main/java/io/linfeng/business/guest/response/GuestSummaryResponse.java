package io.linfeng.business.guest.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="嘉宾汇总信息响应对象")
public class GuestSummaryResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 查看我的数量
	 */
	@ApiModelProperty(value = "查看我的数量")
	private int lookMeCount;
	/**
	 * 喜欢数量
	 */
	@ApiModelProperty(value = "喜欢数量")
	private int likeCount;
	/**
	 * 喜欢我的数量
	 */
	@ApiModelProperty(value = "喜欢我的数量")
	private int likeMeCount;


}
