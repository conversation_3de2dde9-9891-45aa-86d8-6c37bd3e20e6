/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.chat.service;


import io.linfeng.business.chat.request.ChatFriendRequest;
import io.linfeng.business.chat.response.ChatFriendResponse;
import io.linfeng.love.user.entity.UserEntity;

import java.util.List;

/**
 * 聊天朋友服务
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
public interface AppChatFriendService {

    /**
     * 获取所有好友列表
     * @param user 登录用户
     * @return 好友列表
     */
    List<ChatFriendResponse> getFriendList(UserEntity user);

    /**
     * 删除好友
     * @param user 登录用户
     * @param oid 好友oid
     */
    void deleteFriend(UserEntity user, String oid);

    /**
     * 更新黑名单状态
     * @param user 登录用户
     * @param request 请求信息
     */
    void updateBlackStatus(UserEntity user, ChatFriendRequest request);

    /**
     * 建立朋友关系
     * @param user 用户
     * @param friend 朋友
     */
    void buildFriendRelation(UserEntity user, UserEntity friend);
}


