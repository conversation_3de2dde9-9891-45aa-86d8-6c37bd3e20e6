/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245/793326982
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.chat.controller;


import io.linfeng.business.chat.response.ChatHelloResponse;
import io.linfeng.business.chat.service.AppChatHelloService;
import io.linfeng.business.chat.request.ChatHelloRequest;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * 嘉宾打招呼Api
 */
@RestController
@RequestMapping("/app/chat/hello")
@Api(tags = "嘉宾打招呼Api")
public class AppChatHelloController {

    private final AppChatHelloService appChatHelloService;

    public AppChatHelloController(AppChatHelloService appChatHelloService) {
        this.appChatHelloService = appChatHelloService;
    }


    /**
     * 打招呼
     * @param user 登录用户
     * @param request 打招呼请求
     * @return 打招呼结果
     */
    @Login
    @PostMapping()
    @ApiOperation("打招呼")
    public R hello(@ApiIgnore @LoginUser UserEntity user, @RequestBody ChatHelloRequest request){
        appChatHelloService.hello(user, request);
        return R.ok();
    }

    /**
     * 打招呼回应
     * @param user 登录用户
     * @param request 打招呼回应请求
     * @return 打招呼回应结果
     */
    @Login
    @PostMapping("/reply")
    @ApiOperation("打招呼回应")
    public R helloReply(@ApiIgnore @LoginUser UserEntity user, @RequestBody ChatHelloRequest request){
        appChatHelloService.helloReply(user, request);
        return R.ok();
    }

    /**
     * 打招呼列表
     * @param user 登录用户
     * @return 打招呼列表
     */
    @Login
    @GetMapping ("/applyList")
    @ApiOperation("打招呼列表")
    public Result<List<ChatHelloResponse>> helloApplyList(@ApiIgnore @LoginUser UserEntity user){
        List<ChatHelloResponse> guestHelloList = appChatHelloService.helloApplyList(user);
        return new Result<List<ChatHelloResponse>>().ok(guestHelloList);
    }

    /**
     * 删除打招呼记录
     * @param user 登录用户
     * @param request 打招呼请求
     * @return 打招呼结果
     */
    @Login
    @PostMapping("/deleteApply")
    @ApiOperation("删除打招呼记录")
    public R deleteHelloApply(@ApiIgnore @LoginUser UserEntity user, @RequestBody ChatHelloRequest request){
        appChatHelloService.deleteHelloApply(user, request);
        return R.ok();
    }
}
