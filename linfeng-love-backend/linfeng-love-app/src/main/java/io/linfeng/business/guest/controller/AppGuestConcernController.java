/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245/793326982
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.guest.controller;


import io.linfeng.business.guest.request.GuestOperatorRequest;
import io.linfeng.business.guest.service.AppGuestConcernService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 嘉宾关注Api
 */
@RestController
@RequestMapping("/app/guest/concern")
@Api(tags = "嘉宾关注Api")
public class AppGuestConcernController {

    private final AppGuestConcernService appGuestConcernService;

    public AppGuestConcernController(AppGuestConcernService appGuestConcernService) {
        this.appGuestConcernService = appGuestConcernService;
    }

    /**
     * 关注嘉宾
     * @param user 登录用户
     * @param request 操作请求
     * @return 操作结果
     */
    @Login
    @PostMapping("/operator")
    @ApiOperation("关注嘉宾")
    public R concernOperator(@ApiIgnore @LoginUser UserEntity user, @RequestBody GuestOperatorRequest request){
        appGuestConcernService.concernOperator(user, request);
        return R.ok();
    }

}
