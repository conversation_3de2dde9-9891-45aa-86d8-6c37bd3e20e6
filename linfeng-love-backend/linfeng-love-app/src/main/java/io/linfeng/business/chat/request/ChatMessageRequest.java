package io.linfeng.business.chat.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel(description = "消息请求对象")
public class ChatMessageRequest {


	/**
	 * 会话id
	 */
	@ApiModelProperty(value = "会话id")
	private String sessionId;
	/**
	 * 消息id
	 */
	@ApiModelProperty(value = "消息id")
	private String messageId;
	/**
	 * 朋友oid
	 */
	@ApiModelProperty(value = "朋友oid")
	private String friendOid;
	/**
	 * 消息类型
	 */
	@ApiModelProperty(value = "消息类型")
	private int messageType;
	/**
	 * 内容
	 */
	@ApiModelProperty(value = "消息内容")
	private String content;
	/**
	 * 持续时间
	 */
	@ApiModelProperty(value = "持续时间")
	private Integer duration;
	/**
	 * 页数
	 */
	@ApiModelProperty(value = "页数")
	private Integer pageNum;
	/**
	 * 页码
	 */
	@ApiModelProperty(value = "页码")
	private Integer pageSize;

}
