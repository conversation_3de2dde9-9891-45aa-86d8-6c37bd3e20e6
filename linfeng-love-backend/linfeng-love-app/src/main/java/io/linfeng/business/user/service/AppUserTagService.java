package io.linfeng.business.user.service;

import io.linfeng.business.user.request.UserTagRequest;
import io.linfeng.business.user.response.UserTagResponse;
import io.linfeng.love.user.entity.UserEntity;

import java.util.List;

/**
 * 用户标签信息业务服务
 *
 * <AUTHOR>
 * @date 2023-09-06 16:22:14
 */
public interface AppUserTagService {

    /**
     * 获取用户标签列表
     * @param uid 用户id
     * @return 用户标签列表
     */
    List<UserTagResponse> getUserTagList(Integer uid);

    /**
     * 用户标签秀爱
     * @param user 用户
     * @param request 修改请求
     */
    void edit(UserEntity user, UserTagRequest request);
}

