/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.user.controller;


import io.linfeng.business.user.request.UserTagRequest;
import io.linfeng.business.user.response.UserTagResponse;
import io.linfeng.business.user.service.AppUserTagService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * 用户标签Api
 *
 */
@RestController
@RequestMapping("/app/tag")
@Api(tags = "用户标签Api")
public class AppUserTagController {


    private final AppUserTagService appUserTagService;

    public AppUserTagController(AppUserTagService appUserTagService) {
        this.appUserTagService = appUserTagService;
    }

    /**
     * 用户标签列表
     * @param user 登录用户
     * @param request 查询请求
     * @return 用户标签列表
     */
    @Login
    @GetMapping("/list")
    @ApiOperation("用户标签列表")
    public Result<List<UserTagResponse>> list(@ApiIgnore @LoginUser UserEntity user, UserTagRequest request){
        List<UserTagResponse> userTagList = appUserTagService.getUserTagList(user.getUid());
        return new Result<List<UserTagResponse>>().ok(userTagList);
    }

    /**
     * 用户标签编辑
     * @param user 登录用户
     * @param request 标签列表
     * @return 编辑结果
     */
    @Login
    @PostMapping("/edit")
    @ApiOperation("用户标签编辑")
    public R edit(@ApiIgnore @LoginUser UserEntity user, @RequestBody UserTagRequest request){
        appUserTagService.edit(user, request);
        return R.ok("修改成功");
    }

}
