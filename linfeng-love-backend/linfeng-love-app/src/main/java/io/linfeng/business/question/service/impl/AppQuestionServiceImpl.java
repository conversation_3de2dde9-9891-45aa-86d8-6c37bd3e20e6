package io.linfeng.business.question.service.impl;

import io.linfeng.business.question.response.QuestionGroupResponse;
import io.linfeng.business.question.response.QuestionResponse;
import io.linfeng.business.question.service.AppQuestionService;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.question.dto.response.QuestionGroupResponseDTO;
import io.linfeng.love.question.entity.QuestionEntity;
import io.linfeng.love.question.service.QuestionService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("appQuestionService")
public class AppQuestionServiceImpl implements AppQuestionService {

    private final QuestionService questionService;

    public AppQuestionServiceImpl(QuestionService questionService) {
        this.questionService = questionService;
    }

    @Override
    public List<QuestionGroupResponse> getQuestionList() {
        List<QuestionGroupResponseDTO> groupList = questionService.getQuestionList();
        return ObjectMapperUtil.convert(groupList, QuestionGroupResponse.class);
    }

    @Override
    public QuestionResponse getQuestionById(Integer id) {
        QuestionEntity question = questionService.getById(id);
        return ObjectMapperUtil.convert(question, QuestionResponse.class);
    }
}