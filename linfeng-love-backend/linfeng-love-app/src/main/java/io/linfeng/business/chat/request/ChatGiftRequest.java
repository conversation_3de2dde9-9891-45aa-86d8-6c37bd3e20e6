package io.linfeng.business.chat.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="礼物赠送请求对象")
public class ChatGiftRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 用户oid
	 */
	@ApiModelProperty(value = "用户oid")
	private String oid;

	/**
	 * 礼物ID
	 */
	@ApiModelProperty(value = "礼物ID")
	private Integer giftId;
	/**
	 * 礼物数量
	 */
	@ApiModelProperty(value = "礼物数量")
	private Integer number;

}
