package io.linfeng.business.subscribe.build;

import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.business.subscribe.core.AbstractSubscribeMessageExecutor;
import io.linfeng.business.subscribe.dto.WxMaSubscribeMessageDTO;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.love.accusation.entity.AccusationEntity;
import io.linfeng.love.accusation.enums.AccusationType;
import io.linfeng.love.accusation.service.AccusationService;
import io.linfeng.love.message.entity.SubscribeMessageEntity;
import io.linfeng.love.message.enums.SendStatus;
import io.linfeng.love.message.enums.SubscribeChannel;
import io.linfeng.love.message.enums.SubscribeStatus;
import io.linfeng.love.message.enums.SubscribeType;
import io.linfeng.love.message.service.SubscribeMessageService;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.service.UserService;
import me.chanjar.weixin.mp.bean.subscribe.WxMpSubscribeMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;


@Service("accusationMessageExecutor")
public class AccusationMessageExecutor extends AbstractSubscribeMessageExecutor {

    @Autowired
    private AccusationService accusationService;

    @Override
    public WxMaSubscribeMessageDTO buildWxMaSubscribeMessage(UserEntity user, String tmplId, String linkId) {
        AccusationEntity accusation = accusationService.getById(Integer.parseInt(linkId));
        WxMaSubscribeMessageDTO wxMaSubscribeMessageDTO = new WxMaSubscribeMessageDTO();
        WxMaSubscribeMessage wxMaSubscribeMessage = new WxMaSubscribeMessage();
        wxMaSubscribeMessage.setTemplateId(tmplId);
        wxMaSubscribeMessage.setToUser(user.getOpenid());
        wxMaSubscribeMessage.setPage(getPageUrl(linkId));
        List<WxMaSubscribeMessage.MsgData> msgDataList = new ArrayList<>();
        WxMaSubscribeMessage.MsgData msgData1 = new WxMaSubscribeMessage.MsgData();
        msgData1.setName("thing1");
        if(accusation.getType() == AccusationType.USER.getValue()){
            msgData1.setValue("举报用户");
        }
        if(accusation.getType() == AccusationType.MOMENT.getValue()){
            msgData1.setValue("举报动态");
        }
        if(accusation.getType() == AccusationType.COMMENT.getValue()){
            msgData1.setValue("举报评论");
        }
        WxMaSubscribeMessage.MsgData msgData2 = new WxMaSubscribeMessage.MsgData();
        msgData2.setName("time3");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        msgData2.setValue(sdf.format(accusation.getCreateTime()));
        WxMaSubscribeMessage.MsgData msgData3 = new WxMaSubscribeMessage.MsgData();
        msgData3.setName("thing4");
        msgData3.setValue("举报已受理完成");
        msgDataList.add(msgData1);
        msgDataList.add(msgData2);
        msgDataList.add(msgData3);
        wxMaSubscribeMessage.setData(msgDataList);

        saveSubscribeMessage(user, tmplId, linkId, JSON.toJSONString(wxMaSubscribeMessage));

        wxMaSubscribeMessageDTO.setImmediately(false);
        wxMaSubscribeMessageDTO.setWxMaSubscribeMessage(wxMaSubscribeMessage);
        return wxMaSubscribeMessageDTO;
    }

    @Override
    public WxMpSubscribeMessage buildWxMpSubscribeMessage(UserEntity user, String tmplId, String linkId) {
        WxMpSubscribeMessage wxMpSubscribeMessage = new WxMpSubscribeMessage();
        return wxMpSubscribeMessage;
    }

    @Override
    public String getPageUrl(String linkId) {
        return "/pages/accusation/detail?id=" + linkId;
    }

    @Override
    public String getMessageType() {
        return "accusation";
    }
}