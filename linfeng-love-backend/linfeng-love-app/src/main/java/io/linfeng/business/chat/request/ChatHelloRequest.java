package io.linfeng.business.chat.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="打招呼请求对象")
public class ChatHelloRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 嘉宾oid
	 */
	@ApiModelProperty(value = "嘉宾oid")
	private String oid;

	/**
	 * 内容
	 */
	@ApiModelProperty(value = "内容")
	private String content;

}
