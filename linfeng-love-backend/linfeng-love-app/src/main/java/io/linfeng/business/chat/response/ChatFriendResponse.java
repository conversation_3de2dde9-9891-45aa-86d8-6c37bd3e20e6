package io.linfeng.business.chat.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
@ApiModel(description="聊天朋友响应对象")
public class ChatFriendResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 朋友oid
	 */
	@ApiModelProperty(value = "朋友oid")
	private String oid;

	/**
	 * 好友头像
	 */
	@ApiModelProperty(value = "好友头像")
	private String avatar;

	/**
	 * 朋友用户名
	 */
	@ApiModelProperty(value = "朋友用户名")
	private String userName;

	/**
	 * 生日年份
	 */
	@ApiModelProperty(value = "生日年份")
	private String birthdayYear;

	/**
	 * 居住城市
	 */
	@ApiModelProperty(value = "居住城市")
	private String livingCity;

	/**
	 * 单位名称
	 */
	@ApiModelProperty(value = "单位名称")
	private String jobText;


	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private Integer status;

	/**
	 * 在线状态
	 */
	@ApiModelProperty(value = "在线状态")
	private Integer onlineStatus;
	/**
	 * 最近离线时间
	 */
	@ApiModelProperty(value = "最近离线时间")
	private Date lastOfflineTime;


}
