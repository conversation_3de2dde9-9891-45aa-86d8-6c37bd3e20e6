/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.user.controller;


import io.linfeng.business.user.service.AppUserSignService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;

/**
 * 用户签到Api
 */
@RestController
@RequestMapping("/app/sign")
@Api(tags = "用户签到Api")
public class AppUserSignController {

    private final AppUserSignService appUserSignService;

    public AppUserSignController(AppUserSignService appUserSignService) {
        this.appUserSignService = appUserSignService;
    }

    /**
     * 用户签到详情
     * @param user 登录用户
     * @return 用户签到详情
     */
    @Login
    @GetMapping ("/detail")
    @ApiOperation("用户签到情况")
    public Result<Map<String, Object>> list(@ApiIgnore @LoginUser UserEntity user){
        Map<String, Object> userSignDetail = appUserSignService.getUserSignDetail(user);
        return new Result<Map<String, Object>>().ok(userSignDetail);
    }

    /**
     * 用户签到提交
     * @param user 登录用户
     * @return 提交结果
     */
    @Login
    @PostMapping ("/submit")
    @ApiOperation("用户签到提交")
    public R edit(@ApiIgnore @LoginUser UserEntity user){
        appUserSignService.signSubmit(user);
        return R.ok("签到成功");
    }

}
