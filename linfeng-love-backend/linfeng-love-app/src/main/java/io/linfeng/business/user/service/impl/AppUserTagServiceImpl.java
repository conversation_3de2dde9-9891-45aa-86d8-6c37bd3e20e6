package io.linfeng.business.user.service.impl;


import io.linfeng.business.user.request.UserTagRequest;
import io.linfeng.business.user.response.UserTagResponse;
import io.linfeng.business.user.service.AppUserTagService;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.user.dto.response.UserTagResponseDTO;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.service.UserTagService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service("appUserTagService")
public class AppUserTagServiceImpl implements AppUserTagService {

    private final UserTagService userTagService;

    public AppUserTagServiceImpl(UserTagService userTagService) {
        this.userTagService = userTagService;
    }

    @Override
    public List<UserTagResponse> getUserTagList(Integer uid) {
        List<UserTagResponseDTO> responseDTOList = userTagService.getUserTagList(uid);
        return ObjectMapperUtil.convert(responseDTOList, UserTagResponse.class);

    }

    @Override
    @Transactional
    public void edit(UserEntity user, UserTagRequest request) {
        if(request.getDeleteIdList() != null && request.getDeleteIdList().size() != 0){
            userTagService.removeUserTag(user.getUid(), request.getDeleteIdList());
        }
        if(request.getAddIdList() != null && request.getAddIdList().size() != 0){
            userTagService.saveUserTag(user.getUid(), request.getAddIdList());
        }

    }

    private void transformerResponse(UserTagResponseDTO responseDTO, UserTagResponse response){
        BeanUtils.copyProperties(responseDTO, response);
    }
}