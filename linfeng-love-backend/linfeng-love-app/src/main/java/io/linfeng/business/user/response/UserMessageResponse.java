package io.linfeng.business.user.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="用户消息响应实体")
public class UserMessageResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 消息类型
	 */
	@ApiModelProperty(value = "消息类型")
	private String messageSource;
	/**
	 * 发送者姓名
	 */
	@ApiModelProperty(value = "发送者姓名")
	private String senderName;
	/**
	 * 未读消息数量
	 */
	@ApiModelProperty(value = "未读消息数量")
	private Integer unRead;
	/**
	 * 最后一条消息标题
	 */
	@ApiModelProperty(value = "最后一条消息标题")
	private String lastMessageTitle;
	/**
	 * 最后一条消息数据类型
	 */
	@ApiModelProperty(value = "最后一条消息数据类型")
	private Integer lastMessageType;
	/**
	 * 最后一条消息时间
	 */
	@ApiModelProperty(value = "最后一条消息时间")
	private String lastMessageTime;


}
