package io.linfeng.business.user.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="用户消息详情响应实体")
public class UserMessageDetailResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 消息类型
	 */
	@ApiModelProperty(value = "消息类型")
	private String messageType;
	/**
	 * 消息标题
	 */
	@ApiModelProperty(value = "消息标题")
	private String title;
	/**
	 * 消息内容
	 */
	@ApiModelProperty(value = "消息内容")
	private String content;
	/**
	 * 数据类型
	 */
	@ApiModelProperty(value = "数据类型")
	private String dataType;
	/**
	 * 图片
	 */
	@ApiModelProperty(value = "图片")
	private String image;
	/**
	 * 跳转名称
	 */
	@ApiModelProperty(value = "跳转名称")
	private String linkName;
	/**
	 * 跳转链接
	 */
	@ApiModelProperty(value = "跳转链接")
	private String linkUrl;
	/**
	 * 最后一条消息时间
	 */
	@ApiModelProperty(value = "最后一条消息时间")
	private String sendTime;


}
