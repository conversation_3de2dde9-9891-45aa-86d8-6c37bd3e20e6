package io.linfeng.business.user.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.linfeng.business.user.request.ExchangeCashRequest;
import io.linfeng.business.user.request.UserCashedQueryRequest;
import io.linfeng.business.user.response.UserAccountRecordResponse;
import io.linfeng.business.user.response.UserCashedResponse;
import io.linfeng.business.user.service.AppUserCashedService;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.common.enums.CommonStatus;
import io.linfeng.love.config.entity.ConfigExchangeCashEntity;
import io.linfeng.love.config.entity.ConfigExchangePetalEntity;
import io.linfeng.love.config.service.ConfigBusinessService;
import io.linfeng.love.config.service.ConfigExchangeCashService;
import io.linfeng.love.user.entity.UserAccountEntity;
import io.linfeng.love.user.entity.UserAccountRecordEntity;
import io.linfeng.love.user.entity.UserCashedEntity;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.enums.AccountRecordSubType;
import io.linfeng.love.user.enums.AccountRecordType;
import io.linfeng.love.user.service.UserAccountRecordService;
import io.linfeng.love.user.service.UserAccountService;
import io.linfeng.love.user.service.UserCashedService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service("appUserCashedService")
@AllArgsConstructor
public class AppUserCashedServiceImpl implements AppUserCashedService {

    private final UserAccountService userAccountService;

    private final UserAccountRecordService userAccountRecordService;

    private final UserCashedService userCashedService;

    private final ConfigExchangeCashService configExchangeCashService;

    private final ConfigBusinessService configBusinessService;

    @Override
    public IPage<UserCashedResponse> getUserCashedPage(UserEntity user, UserCashedQueryRequest request) {
        IPage<UserCashedEntity> dtoPage = new Page<>(request.getPageNum(),request.getPageSize());
        LambdaQueryWrapper<UserCashedEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserCashedEntity::getUid, user.getUid());
        if(request.getStatus() != 0){
            wrapper.eq(UserCashedEntity::getStatus, request.getStatus());
        }
        wrapper.orderByDesc(UserCashedEntity::getSubmitTime);
        dtoPage = userCashedService.page(dtoPage, wrapper);
        return ObjectMapperUtil.convert(dtoPage, UserCashedResponse.class);
    }

    @Override
    public void exchangeCashSubmit(UserEntity user, ExchangeCashRequest request) {
        ConfigExchangeCashEntity configExchangeCash = configExchangeCashService.getById(request.getId());

        LambdaQueryWrapper<UserAccountEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserAccountEntity::getUid, user.getUid());
        UserAccountEntity userAccount = userAccountService.getOne(wrapper);
        if(userAccount.getUnCashedAmount().compareTo(configExchangeCash.getAmount()) < 0){
            throw new LinfengException("您的账户余额不足，无法兑换");
        }

        if(userAccount.getDayCashedCount() == Integer.parseInt(configBusinessService.getValue(Constant.CASH_COUNT_DAY)) ||
            userAccount.getWeekCashedCount() == Integer.parseInt(configBusinessService.getValue(Constant.CASH_COUNT_WEEK))){
            throw new LinfengException("您的提现次数已达上线");
        }

        //更新账户信息
        userAccount.setUnCashedAmount(userAccount.getUnCashedAmount().subtract(configExchangeCash.getAmount()));
        userAccount.setFreezeAmount(configExchangeCash.getAmount());
        //最后提现在今日之前，重置提现次数
        if(userAccount.getLastCashedTime() == null || DateUtil.getToDay().compareTo(userAccount.getLastCashedTime()) > 0){
            userAccount.setDayCashedCount(1);
        }else{
            userAccount.setDayCashedCount(userAccount.getDayCashedCount() + 1);
        }
        //最后提现时间在本周之前，重置提现次数，更新时间为周一
        if(userAccount.getLastCashedTime() == null || DateUtil.getWeekMonday().compareTo(userAccount.getLastCashedTime()) > 0){
            userAccount.setWeekCashedCount(1);
        }else{
            userAccount.setWeekCashedCount(userAccount.getWeekCashedCount() + 1);
        }

        userAccount.setLastCashedTime(DateUtil.nowDateTime());
        userAccount.setUpdateTime(DateUtil.nowDateTime());
        userAccountService.updateById(userAccount);

        //保存提交记录
        UserCashedEntity userCashed = new UserCashedEntity();
        userCashed.setUid(user.getUid());
        userCashed.setAmount(configExchangeCash.getAmount());
        userCashed.setChannel(request.getChannel());
        userCashed.setPaymentCode(request.getPaymentCode());
        userCashed.setStatus(CommonStatus.CHECKING.getValue());
        userCashed.setSubmitTime(DateUtil.nowDateTime());
        userCashed.setCreateTime(DateUtil.nowDateTime());
        userCashedService.save(userCashed);

    }
}