package io.linfeng.business.chat.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="聊天消息响应对象")
public class ChatMessageResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 会话id
	 */
	@ApiModelProperty(value = "会话id")
	private String sessionId;
	/**
	 * 消息id
	 */
	@ApiModelProperty(value = "消息id")
	private String messageId;
	/**
	 * 发送者oid
	 */
	@ApiModelProperty(value = "发送者oid")
	private String senderOid;

	/**
	 * 接收者oid
	 */
	@ApiModelProperty(value = "接收者oid")
	private String receiverOid;
	/**
	 * 发送时间
	 */
	@ApiModelProperty(value = "发送时间")
	private String sendTime;
	/**
	 * 内容
	 */
	@ApiModelProperty(value = "内容")
	private String content;
	/**
	 * 类型
	 */
	@ApiModelProperty(value = "类型")
	private Integer messageType;
	/**
	 * 持续时间
	 */
	@ApiModelProperty(value = "持续时间")
	private Integer duration;

}
