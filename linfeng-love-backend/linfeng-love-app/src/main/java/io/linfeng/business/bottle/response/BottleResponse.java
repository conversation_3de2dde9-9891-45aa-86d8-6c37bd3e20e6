package io.linfeng.business.bottle.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="漂流瓶")
public class BottleResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 漂流瓶id
	 */
	@ApiModelProperty(value = "漂流瓶id")
	private Integer bottleId;
	/**
	 * 漂流瓶用户oid
	 */
	@ApiModelProperty(value = "漂流瓶用户oid")
	private String oid;
	/**
	 * 内容
	 */
	@ApiModelProperty(value = "内容")
	private String content;
	/**
	 * 抛出时间
	 */
	@ApiModelProperty(value = "抛出时间")
	private String throwTime;
	/**
	 * 用户名
	 */
	@ApiModelProperty(value = "用户名")
	private String userName;
	/**
	 * 头像
	 */
	@ApiModelProperty(value = "头像")
	private String avatar;
	/**
	 * 性别(0未知，1男，2女)
	 */
	@ApiModelProperty(value = "性别(字典)")
	private Integer gender;

	/**
	 * 出生年份
	 */
	@ApiModelProperty(value = "出生年份")
	private String birthdayYear;
	/**
	 * 行业/职业(字典翻译)
	 */
	@ApiModelProperty(value = "行业/职业(字典翻译)")
	private String jobText;
	/**
	 * 居住城市
	 */
	@ApiModelProperty(value = "居住城市")
	private String livingCity;
	/**
	 * 锁定标识
	 */
	@ApiModelProperty(value = "锁定标识")
	private Integer lockFlag;


}
