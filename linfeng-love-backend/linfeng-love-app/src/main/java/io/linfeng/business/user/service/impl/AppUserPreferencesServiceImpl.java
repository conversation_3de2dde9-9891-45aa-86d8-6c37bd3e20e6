package io.linfeng.business.user.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.linfeng.business.user.request.UserPreferencesRequest;
import io.linfeng.business.user.response.UserPreferencesResponse;
import io.linfeng.business.user.service.AppUserPreferencesService;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.entity.UserPreferencesEntity;
import io.linfeng.love.user.service.UserPreferencesService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service("appUserPreferencesService")
public class AppUserPreferencesServiceImpl implements AppUserPreferencesService {

    private final UserPreferencesService userPreferencesService;

    public AppUserPreferencesServiceImpl(UserPreferencesService userPreferencesService) {
        this.userPreferencesService = userPreferencesService;
    }

    @Override
    public UserPreferencesResponse getUserPreferences(UserEntity user) {
        UserPreferencesResponse response = new UserPreferencesResponse();
        LambdaQueryWrapper<UserPreferencesEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(UserPreferencesEntity::getUid, user.getUid());
        UserPreferencesEntity userPreferences = userPreferencesService.getOne(lambdaQueryWrapper);
        BeanUtils.copyProperties(userPreferences, response);
        return response;
    }

    @Override
    public void updateAppUserPreferences(UserEntity user, UserPreferencesRequest request) {
        LambdaQueryWrapper<UserPreferencesEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(UserPreferencesEntity::getUid, user.getUid());
        UserPreferencesEntity userPreferences = new UserPreferencesEntity();
        BeanUtils.copyProperties(request, userPreferences);
        userPreferences.setUid(user.getUid());
        userPreferences.setUpdateTime(DateUtil.nowDateTime());
        userPreferencesService.update(userPreferences, lambdaQueryWrapper);
    }
}