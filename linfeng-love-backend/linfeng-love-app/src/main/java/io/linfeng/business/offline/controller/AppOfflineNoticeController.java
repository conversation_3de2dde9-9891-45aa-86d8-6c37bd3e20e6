/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.offline.controller;

import io.linfeng.business.offline.response.NoticeResponse;
import io.linfeng.business.offline.service.AppOfflineNoticeService;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.api.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 线下活动公告
 *
 * <AUTHOR>
 * @date 2024-03-28 14:26:17
 */
@RestController
@RequestMapping("app/offline/notice")
@Api(tags = "线下活动公告Api")
@AllArgsConstructor
public class AppOfflineNoticeController {

    private final AppOfflineNoticeService appOfflineNoticeService;

    /**
     * 活动公告列表
     * @return 活动公告列表
     */
    @Login
    @GetMapping("/list")
    @ApiOperation("活动公告列表")
    public Result<List<NoticeResponse>> list(){
        List<NoticeResponse> noticeList = appOfflineNoticeService.getNoticelist();
        return new Result<List<NoticeResponse>>().ok(noticeList);
    }

    /**
     * 活动公告详情
     * @param id 查询请求
     * @return 活动公告详情
     */
    @Login
    @GetMapping("/detail/{id}")
    @ApiOperation("活动公告详情")
    public Result<NoticeResponse> detail(@PathVariable("id") Integer id){
        NoticeResponse activityDetail = appOfflineNoticeService.getOfflineNoticeDetail(id);
        return new Result<NoticeResponse>().ok(activityDetail);
    }


}
