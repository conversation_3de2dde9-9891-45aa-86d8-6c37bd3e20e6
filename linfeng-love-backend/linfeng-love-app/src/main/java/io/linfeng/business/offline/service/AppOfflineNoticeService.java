/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.offline.service;


import io.linfeng.business.offline.response.NoticeResponse;

import java.util.List;

/**
 * 线下活动
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
public interface AppOfflineNoticeService {

    /**
     * 获取公告列表
     * @return 公告列表
     */
    List<NoticeResponse> getNoticelist();

    /**
     * 获取公告详情
     * @param id 公告id
     * @return 公告详情
     */
    NoticeResponse getOfflineNoticeDetail(Integer id);
}

