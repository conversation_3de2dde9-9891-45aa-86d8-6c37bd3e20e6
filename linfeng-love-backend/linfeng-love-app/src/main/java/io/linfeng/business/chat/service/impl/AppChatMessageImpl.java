package io.linfeng.business.chat.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.linfeng.love.chat.service.ChatFriendService;
import io.linfeng.love.user.enums.RealNameStatus;
import io.linfeng.push.client.dto.ChatMessageData;
import io.linfeng.business.chat.request.ChatMessageRequest;
import io.linfeng.business.chat.response.ChatMessageResponse;
import io.linfeng.business.chat.service.AppChatFriendService;
import io.linfeng.business.chat.service.AppChatMessageService;
import io.linfeng.business.chat.service.AppChatSessionService;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.chat.dto.response.ChatMessageResponseDTO;
import io.linfeng.love.chat.entity.ChatMessageEntity;
import io.linfeng.love.chat.entity.ChatSessionEntity;
import io.linfeng.love.chat.enums.FriendStatus;
import io.linfeng.love.chat.enums.MessageType;
import io.linfeng.love.chat.service.ChatMessageService;
import io.linfeng.love.chat.service.ChatSessionService;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.service.UserService;
import io.linfeng.push.client.dto.PushMessage;
import io.linfeng.push.client.enums.ChatMessageDataType;
import io.linfeng.push.client.enums.PushMessageType;
import io.linfeng.push.client.producer.MessageProducer;
import org.springframework.stereotype.Service;

import java.util.Date;


@Service("appChatMessageService")
public class AppChatMessageImpl implements AppChatMessageService {

    private final ChatSessionService chatSessionService;

    private final ChatMessageService chatMessageService;

    private final AppChatSessionService appChatSessionService;

    private final UserService userService;

    private final MessageProducer messageProducer;

    private final ChatFriendService chatFriendService;

    public AppChatMessageImpl(ChatSessionService chatSessionService, ChatMessageService chatMessageService, AppChatSessionService appChatSessionService, UserService userService, MessageProducer messageProducer, ChatFriendService chatFriendService) {
        this.chatSessionService = chatSessionService;
        this.chatMessageService = chatMessageService;
        this.appChatSessionService = appChatSessionService;
        this.userService = userService;
        this.messageProducer = messageProducer;
        this.chatFriendService = chatFriendService;
    }

    @Override
    public ChatMessageResponse sendMessage(UserEntity user, ChatMessageRequest request) {

        UserEntity friendUser = userService.getUserByOid(request.getFriendOid());

        Integer friendStatus = chatFriendService.checkFriendStatus(friendUser.getUid(), user.getUid());

        if(friendStatus != FriendStatus.NORMAL.getValue()){
            throw new LinfengException("您发出的消息被对方拒收了");
        }

        Date sendTime = DateUtil.nowDateTime();
        //创建或者更新会话
        ChatSessionEntity chatSessionEntity = buildSession(user, friendUser, sendTime, request);
        String sessionId = appChatSessionService.createOrUpdateSession(chatSessionEntity);

        //聊天内容入库
        ChatMessageEntity chatMessage = new ChatMessageEntity();
        chatMessage.setMessageId(IdUtil.fastSimpleUUID());
        chatMessage.setCreateTime(DateUtil.nowDateTime());
        chatMessage.setSessionId(sessionId);
        chatMessage.setSenderUid(user.getUid());
        chatMessage.setReceiverUid(friendUser.getUid());
        chatMessage.setMessageType(request.getMessageType());
        chatMessage.setContent(request.getContent());
        chatMessage.setDuration(request.getDuration());
        chatMessage.setSendTime(sendTime);
        ChatMessageResponseDTO responseDTO = chatMessageService.saveAndRefreshCache(chatMessage);

        //聊天消息推送
        pushChatMessage(user, friendUser, chatMessage, ChatMessageDataType.SEND.getValue());

        ChatMessageResponse response = ObjectMapperUtil.convert(responseDTO, ChatMessageResponse.class);
        return response;
    }

    @Override
    public IPage<ChatMessageResponse> getMessageList(UserEntity user, ChatMessageRequest request) {

        LambdaQueryWrapper<ChatSessionEntity> sessionWrapper = new LambdaQueryWrapper();
        sessionWrapper.eq(ChatSessionEntity::getSessionId, request.getSessionId());
        sessionWrapper.eq(ChatSessionEntity::getUid, user.getUid());
        ChatSessionEntity chatSession = chatSessionService.getOne(sessionWrapper);

        IPage<ChatMessageResponseDTO> dtoPage = chatMessageService.selectMessagePage(request.getPageNum(),request.getPageSize(), chatSession);
        IPage<ChatMessageResponse> page = ObjectMapperUtil.convert(dtoPage, ChatMessageResponse.class);
        return page;

    }

    @Override
    public void recallMessage(UserEntity user, ChatMessageRequest request) {

        LambdaQueryWrapper<ChatMessageEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ChatMessageEntity::getMessageId, request.getMessageId());
        ChatMessageEntity chatMessage = chatMessageService.getOne(wrapper);

        chatMessage.setMessageType(MessageType.RECALL.getValue());
        chatMessageService.updateAndRefreshCache(chatMessage);

        //更新我的会话
        LambdaQueryWrapper<ChatSessionEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ChatSessionEntity::getSessionId, request.getSessionId());
        lambdaQueryWrapper.eq(ChatSessionEntity::getUid, user.getUid());
        ChatSessionEntity chatSessionEntity = chatSessionService.getOne(lambdaQueryWrapper);
        chatSessionEntity.setLastMessageType(MessageType.RECALL.getValue());
        chatSessionEntity.setUpdateTime(DateUtil.nowDateTime());
        appChatSessionService.createOrUpdateSession(chatSessionEntity);

        //聊天消息推送
        UserEntity friendUser = userService.getUserByOid(request.getFriendOid());
        pushChatMessage(user, friendUser, chatMessage, ChatMessageDataType.RECALL.getValue());
    }

    @Override
    public void readMessage(UserEntity user, ChatMessageRequest request) {
        LambdaQueryWrapper<ChatSessionEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ChatSessionEntity::getSessionId, request.getSessionId());
        lambdaQueryWrapper.eq(ChatSessionEntity::getUid, user.getUid());
        ChatSessionEntity chatSession = chatSessionService.getOne(lambdaQueryWrapper);
        chatSession.setUnRead(0);
        chatSessionService.updateAndRefreshCache(chatSession);
    }

    private void pushChatMessage(UserEntity user, UserEntity friendUser, ChatMessageEntity chatMessage, String operatorTYpe){
        PushMessage pushMessage = new PushMessage();
        pushMessage.setSenderUid(user.getUid());
        pushMessage.setReceiverUid(friendUser.getUid());
        pushMessage.setType(PushMessageType.CHAT.getValue());
        ChatMessageData chatMessageData = ObjectMapperUtil.convert(chatMessage, ChatMessageData.class);
        chatMessageData.setOperatorType(operatorTYpe);
        chatMessageData.setSenderOid(user.getOid());
        chatMessageData.setReceiverOid(friendUser.getOid());
        chatMessageData.setSendTime(DateUtil.dateToStr(chatMessage.getSendTime(), DateUtil.DATE_FORMAT));
        pushMessage.setData(chatMessageData);
        messageProducer.sendMessage(Constant.DIRECT_EXCHANGE_NAME, Constant.PUSH_ROUTING_NAME, pushMessage);
    }

    private ChatSessionEntity buildSession(UserEntity user, UserEntity friendUser, Date sendTime, ChatMessageRequest request){
        ChatSessionEntity chatSessionEntity = new ChatSessionEntity();
        chatSessionEntity.setSessionId(request.getSessionId());
        chatSessionEntity.setUid(user.getUid());
        chatSessionEntity.setFriendUid(friendUser.getUid());
        chatSessionEntity.setLastMessageUid(user.getUid());
        chatSessionEntity.setLastMessageType(request.getMessageType());
        chatSessionEntity.setLastMessageContent(request.getContent());
        chatSessionEntity.setLastMessageTime(sendTime);
        chatSessionEntity.setUnRead(0);
        return chatSessionEntity;
    }
}