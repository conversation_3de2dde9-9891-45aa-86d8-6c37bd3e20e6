package io.linfeng.business.offline.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
@ApiModel(description="线下活动响应对象（列表）")
public class ActivityDetailResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 活动id
	 */
	@ApiModelProperty(value = "活动id")
	private Integer id;

	/**
	 * 活动名称
	 */
	@ApiModelProperty(value = "活动名称")
	private String activityName;
	/**
	 * 城市
	 */
	@ApiModelProperty(value = "城市")
	private String city;
	/**
	 * 地址标题
	 */
	@ApiModelProperty(value = "地址标题")
	private String addressTitle;
	/**
	 * 精度
	 */
	@ApiModelProperty(value = "精度")
	private String longitude;
	/**
	 * 纬度
	 */
	@ApiModelProperty(value = "纬度")
	private String latitude;

	/**
	 * 男生参加人数
	 */
	@ApiModelProperty(value = "男生参加人数")
	private Integer manJoinNumber;
	/**
	 * 女生参加人数
	 */
	@ApiModelProperty(value = "女生参加人数")
	private Integer womanJoinNumber;
	/**
	 * 男生人数
	 */
	@ApiModelProperty(value = "男生人数")
	private Integer manNumber;
	/**
	 * 女生人数
	 */
	@ApiModelProperty(value = "女生人数")
	private Integer womanNumber;
	/**
	 * 总人数
	 */
	@ApiModelProperty(value = "总人数")
	private Integer totalNumber;
	/**
	 * 男生费用
	 */
	@ApiModelProperty(value = "男生费用")
	private BigDecimal manAmount;
	/**
	 * 女生费用
	 */
	@ApiModelProperty(value = "女生费用")
	private BigDecimal womanAmount;
	/**
	 * 男生早鸟费用
	 */
	@ApiModelProperty(value = "男生早鸟费用")
	private BigDecimal manEarlyAmount;
	/**
	 * 女生早鸟费用
	 */
	@ApiModelProperty(value = "女生早鸟费用")
	private BigDecimal womanEarlyAmount;
	/**
	 * 活动开始时间
	 */
	@ApiModelProperty(value = "活动开始时间")
	private String startTime;
	/**
	 * 活动结束时间
	 */
	@ApiModelProperty(value = "活动结束时间")
	private String endTime;
	/**
	 * 活动报名结束时间
	 */
	@ApiModelProperty(value = "活动报名结束时间")
	private String joinEndTime;
	/**
	 * 早鸟结束时间
	 */
	@ApiModelProperty(value = "早鸟结束时间")
	private String earlyEndTime;
	/**
	 * 官方oid
	 */
	@ApiModelProperty(value = "官方oid")
	private String officialOid;
	/**
	 * 官方昵称
	 */
	@ApiModelProperty(value = "官方昵称")
	private String officialUserName;
	/**
	 * 官方头像
	 */
	@ApiModelProperty(value = "官方头像")
	private String officialAvatar;
	/**
	 * 官方联系方式
	 */
	@ApiModelProperty(value = "官方联系方式")
	private String officialImage;
	/**
	 * 活动状态
	 */
	@ApiModelProperty(value = "活动状态")
	private Integer activityStatus;
	/**
	 * 报名状态
	 */
	@ApiModelProperty(value = "报名状态")
	private Integer joinStatus;
	/**
	 * 活动封面照片
	 */
	@ApiModelProperty(value = "活动封面照片")
	private String urlCover;
	/**
	 * 活动主照片
	 */
	@ApiModelProperty(value = "活动主照片")
	private String urlMain;
	/**
	 * 轮播图
	 */
	@ApiModelProperty(value = "轮播图")
	private String urlList;
	/**
	 * 参与人头像列表
	 */
	@ApiModelProperty(value = "参与人头像列表")
	private List<String> avatarList;

}
