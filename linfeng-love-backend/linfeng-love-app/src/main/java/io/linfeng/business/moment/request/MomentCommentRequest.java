package io.linfeng.business.moment.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description="评论请求对象")
public class MomentCommentRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 动态Id
	 */
	@ApiModelProperty(value = "动态Id")
	private Integer momentId;

	/**
	 * 评论Id
	 */
	@ApiModelProperty(value = "评论Id")
	private Integer commentId;

	/**
	 * pid
	 */
	@ApiModelProperty(value = "pid")
	private Integer pid;

	/**
	 * 被回复者oid
	 */
	@ApiModelProperty(value = "被回复者oid")
	private String beReplyOid;

	/**
	 * 内容
	 */
	@ApiModelProperty(value = "内容")
	private String content;

}
