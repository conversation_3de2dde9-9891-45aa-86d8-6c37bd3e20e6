package io.linfeng.business.user.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.linfeng.business.user.request.AccountRecordQueryRequest;
import io.linfeng.business.user.response.UserAccountRecordResponse;
import io.linfeng.business.user.response.UserAccountResponse;
import io.linfeng.business.user.response.UserSignResponse;
import io.linfeng.business.user.service.AppUserAccountService;
import io.linfeng.business.user.service.AppUserSignService;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.common.enums.PrizeOrigin;
import io.linfeng.love.common.enums.PrizeType;
import io.linfeng.love.config.entity.ConfigSignEntity;
import io.linfeng.love.config.service.ConfigBusinessService;
import io.linfeng.love.config.service.ConfigSignService;
import io.linfeng.love.executor.PrizeEventExecutor;
import io.linfeng.love.moment.dto.response.MomentResponseDTO;
import io.linfeng.love.user.dto.response.UserAccountRecordResponseDTO;
import io.linfeng.love.user.entity.UserAccountEntity;
import io.linfeng.love.user.entity.UserAccountRecordEntity;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.entity.UserSignEntity;
import io.linfeng.love.user.enums.AccountRecordSubType;
import io.linfeng.love.user.enums.AccountRecordType;
import io.linfeng.love.user.service.UserAccountRecordService;
import io.linfeng.love.user.service.UserAccountService;
import io.linfeng.love.user.service.UserSignService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("appUserAccountService")
@AllArgsConstructor
public class AppUserAccountServiceImpl implements AppUserAccountService {

    private final UserAccountService userAccountService;

    private final UserAccountRecordService userAccountRecordService;

    @Override
    public void entry(Integer uid, BigDecimal amount, AccountRecordSubType subType, String remark) {
        LambdaQueryWrapper<UserAccountEntity> accountWrapper = new LambdaQueryWrapper<>();
        accountWrapper.eq(UserAccountEntity::getUid, uid);
        UserAccountEntity friendAccount = userAccountService.getOne(accountWrapper);
        if(friendAccount == null){
            friendAccount = new UserAccountEntity();
            friendAccount.setUid(uid);
            friendAccount.setTotalAmount(amount);
            friendAccount.setCashedAmount(new BigDecimal(0));
            friendAccount.setFreezeAmount(new BigDecimal(0));
            friendAccount.setUnCashedAmount(amount);
            friendAccount.setCreateTime(DateUtil.nowDateTime());
            userAccountService.save(friendAccount);
        }else{
            friendAccount.setTotalAmount(friendAccount.getTotalAmount().add(amount));
            friendAccount.setUnCashedAmount(friendAccount.getUnCashedAmount().add(amount));
            friendAccount.setUpdateTime(DateUtil.nowDateTime());
            userAccountService.updateById(friendAccount);
        }

        //记录流水
        UserAccountRecordEntity userAccountRecord = new UserAccountRecordEntity();
        userAccountRecord.setUid(uid);
        userAccountRecord.setAmount(amount);
        userAccountRecord.setType(AccountRecordType.ENTRY.getValue());
        userAccountRecord.setSubType(subType.getValue());
        userAccountRecord.setRemark(remark);
        userAccountRecord.setCreateTime(DateUtil.nowDateTime());
        userAccountRecordService.save(userAccountRecord);
    }

    @Override
    public UserAccountResponse getUserAccount(UserEntity user) {
        LambdaQueryWrapper<UserAccountEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserAccountEntity::getUid, user.getUid());
        UserAccountEntity userAccount = userAccountService.getOne(wrapper);
        if(userAccount == null){
            userAccount = new UserAccountEntity();
            userAccount.setUid(user.getUid());
            userAccount.setTotalAmount(new BigDecimal(0));
            userAccount.setCashedAmount(new BigDecimal(0));
            userAccount.setPetalAmount(new BigDecimal(0));
            userAccount.setFreezeAmount(new BigDecimal(0));
            userAccount.setUnCashedAmount(new BigDecimal(0));
            userAccount.setDayCashedCount(0);
            userAccount.setWeekCashedCount(0);
            userAccount.setCreateTime(DateUtil.nowDateTime());
            userAccountService.save(userAccount);
        }
        UserAccountResponse response = ObjectMapperUtil.convert(userAccount, UserAccountResponse.class);
        BigDecimal toDayAmount = userAccountRecordService.getToDayReward(user.getUid());
        response.setToDayAmount(toDayAmount);
        return response;
    }

    @Override
    public IPage<UserAccountRecordResponse> getUserAccountRecordPage(UserEntity user, AccountRecordQueryRequest request) {
        IPage<UserAccountRecordEntity> dtoPage = new Page<>(request.getPageNum(),request.getPageSize());
        LambdaQueryWrapper<UserAccountRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserAccountRecordEntity::getUid, user.getUid());
        wrapper.eq(UserAccountRecordEntity::getType, request.getType());
        wrapper.orderByDesc(UserAccountRecordEntity::getCreateTime);
        dtoPage = userAccountRecordService.page(dtoPage, wrapper);
        return ObjectMapperUtil.convert(dtoPage, UserAccountRecordResponse.class);
    }
}