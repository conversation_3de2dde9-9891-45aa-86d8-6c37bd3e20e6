package io.linfeng.business.user.service;

import io.linfeng.business.user.request.BindPhoneRequest;
import io.linfeng.business.user.request.UserLocationRequest;
import io.linfeng.business.user.request.UserUpdateRequest;
import io.linfeng.business.user.response.UserResponse;
import io.linfeng.love.user.entity.UserEntity;

/**
 * App用户服务
 *
 * <AUTHOR>
 * @date 2023-09-06 16:22:14
 */
public interface AppUserService {

    /**
     * 获取登录用户信息
     * @param uid 用户id
     * @return 登录用户信息
     */
    UserResponse getLoginUserInfo(Integer uid);

    /**
     * 根据oid获取用户信息
     * @param oid 用户oid
     * @return 用户信息
     */
    UserResponse getUserResponseByOid(String oid);

    /**
     * 更新用户信息
     * @param userUpdateRequest 需要更新的信息
     * @param user 登录用户
     */
    void updateAppUserInfo(UserUpdateRequest userUpdateRequest, UserEntity user);

    /**
     * 首次补充个人资料
     * @param userUpdateRequest 需要更新的信息
     * @param user 登录用户
     */
    void updateAppUserInfoFirst(UserUpdateRequest userUpdateRequest, UserEntity user);

    /**
     * 修改头像
     * @param userUpdateRequest 需要更新的信息
     * @param user 登录用户
     */
    void updateUserAvatar(UserUpdateRequest userUpdateRequest, UserEntity user);

    /**
     * 绑定手机号
     * @param user 登录用户
     * @param request 手机号请求对象
     */
    void bindPhone(UserEntity user, BindPhoneRequest request);

    /**
     * 用户地理位置更新
     * @param userLocationRequest 地理位置更新请求
     * @param user 登录用户
     */
    void updateUserLocation(UserLocationRequest userLocationRequest, UserEntity user);

    /**
     * 用户地理位置更新
     * @param ip ip
     * @param user 登录用户
     */
    void updateUserLocation(String ip, UserEntity user);

    /**
     * 根据手机号查询用户oid
     * @param mobile 手机号
     * @return 用户oid
     */
    String searchUserOid(String mobile);

    /**
     * 更新语音介绍
     * @param userUpdateRequest 更新请求
     * @param user 登录用户
     */
    void updateUserVoice(UserUpdateRequest userUpdateRequest, UserEntity user);

    /**
     * 删除语音介绍
     * @param user 登录用户
     */
    void deleteVoice(UserEntity user);

    /**
     * 删除用户（逻辑删除）
     * @param user 登录用户
     */
    void deleteUser(UserEntity user);

    /**
     * 修改背景
     * @param userUpdateRequest 修改请求
     * @param user 登录用户
     */
    void updateBackImage(UserUpdateRequest userUpdateRequest, UserEntity user);
}

