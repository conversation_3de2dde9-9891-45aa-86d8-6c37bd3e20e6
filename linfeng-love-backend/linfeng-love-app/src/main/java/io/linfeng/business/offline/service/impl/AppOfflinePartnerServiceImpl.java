package io.linfeng.business.offline.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.linfeng.business.offline.request.*;
import io.linfeng.business.offline.response.MemberResponse;
import io.linfeng.business.offline.response.PartnerDetailResponse;
import io.linfeng.business.offline.response.PartnerSimpleResponse;
import io.linfeng.business.offline.service.AppOfflinePartnerService;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.love.common.enums.CommonStatus;
import io.linfeng.love.config.service.ConfigBusinessService;
import io.linfeng.love.message.entity.SubscribeMessageEntity;
import io.linfeng.love.message.service.SubscribeMessageService;
import io.linfeng.love.offline.dto.response.MemberResponseDTO;
import io.linfeng.love.offline.dto.response.PartnerDetailResponseDTO;
import io.linfeng.love.offline.dto.response.PartnerSimpleResponseDTO;
import io.linfeng.love.offline.entity.OfflinePartnerEntity;
import io.linfeng.love.offline.entity.OfflinePartnerJoinEntity;
import io.linfeng.love.offline.enums.ActivityJoinStatus;
import io.linfeng.love.offline.service.OfflinePartnerJoinService;
import io.linfeng.love.offline.service.OfflinePartnerService;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.service.UserService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service("appOfflinePartnerService")
@AllArgsConstructor
public class AppOfflinePartnerServiceImpl implements AppOfflinePartnerService {

    private final OfflinePartnerService offlinePartnerService;

    private final OfflinePartnerJoinService offlinePartnerJoinService;

    private final ConfigBusinessService configBusinessService;

    private final UserService userService;

    private final SubscribeMessageService subscribeMessageService;

    @Override
    @Transactional
    public void createPartner(UserEntity user, PartnerCreateRequest request) {
        OfflinePartnerEntity offlinePartner = ObjectMapperUtil.convert(request, OfflinePartnerEntity.class);

        if(request.getId() != null){
            offlinePartner.setUpdateTime(DateUtil.nowDateTime());
            offlinePartner.setStatus(CommonStatus.CHECKING.getValue());
            offlinePartnerService.updateById(offlinePartner);

        }else{
            offlinePartner.setUid(user.getUid());
            offlinePartner.setManJoinNumber(0);
            offlinePartner.setWomanJoinNumber(0);
            offlinePartner.setStatus(CommonStatus.CHECKING.getValue());
            offlinePartner.setCreateTime(DateUtil.nowDateTime());
            offlinePartnerService.save(offlinePartner);
            //自己先加入活动
            PartnerJoinRequest joinRequest = new PartnerJoinRequest();
            joinRequest.setPartnerId(offlinePartner.getId());
            joinPartner(user, joinRequest);
        }
    }

    @Override
    public IPage<PartnerSimpleResponse> getOfflinePartnerList(UserEntity user, PartnerQueryRequest request) {
        IPage<PartnerSimpleResponseDTO> dtoPage = new Page<>(request.getPageNum(),request.getPageSize());
        dtoPage = offlinePartnerService.getPartnerList(dtoPage, request.getTypeId(), request.getCity(), request.getQueryType());
        IPage<PartnerSimpleResponse> page = ObjectMapperUtil.convert(dtoPage, PartnerSimpleResponse.class);
        return page;
    }

    @Override
    public IPage<PartnerSimpleResponse> getMyPartnerList(UserEntity user, PartnerQueryRequest request) {
        IPage<PartnerSimpleResponseDTO> dtoPage = new Page<>(request.getPageNum(),request.getPageSize());
        dtoPage = offlinePartnerService.getMyPartnerList(dtoPage, user.getUid(), request.getQueryType());
        IPage<PartnerSimpleResponse> page = ObjectMapperUtil.convert(dtoPage, PartnerSimpleResponse.class);
        return page;
    }

    @Override
    public PartnerDetailResponse getOfflinePartnerDetail(UserEntity user, Integer id) {
        PartnerDetailResponseDTO responseDTO = offlinePartnerService.getPartnerDetail(id, user.getUid());
        PartnerDetailResponse response = ObjectMapperUtil.convert(responseDTO, PartnerDetailResponse.class);
        List<String> avatarList = offlinePartnerJoinService.getJoinAvatarList(id);
        response.setAvatarList(avatarList);
        UserEntity createUser = userService.getUserByUid(responseDTO.getUid());
        response.setOid(createUser.getOid());
        return response;
    }

    @Override
    @Transactional
    public void joinPartner(UserEntity user, PartnerJoinRequest request) {

        //判断是否重置次数
        if(user.getCancelPartnerLastTime() == null || user.getCancelPartnerLastTime().before(DateUtil.getMonthFirstDay())){
            user.setCancelPartnerCount(0);
            user.setUpdateTime(DateUtil.nowDateTime());
            userService.updateAndDeleteCache(user);
        }

        Integer maxCancelPartnerCount = Integer.parseInt(configBusinessService.getValue(Constant.MAX_CANCEL_PARTNER_COUNT));
        if(user.getCancelPartnerCount()!= null && user.getCancelPartnerCount() >= maxCancelPartnerCount){
            throw new LinfengException("您本月取消报名次数已达上限,无法继续参加活动");
        }

        LambdaQueryWrapper<OfflinePartnerJoinEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OfflinePartnerJoinEntity::getPartnerId, request.getPartnerId());
        wrapper.eq(OfflinePartnerJoinEntity::getUid, user.getUid());

        OfflinePartnerJoinEntity offlinePartnerJoin = offlinePartnerJoinService.getOne(wrapper);
        if(offlinePartnerJoin == null){
            offlinePartnerJoin = new OfflinePartnerJoinEntity();
            offlinePartnerJoin.setPartnerId(request.getPartnerId());
            offlinePartnerJoin.setUid(user.getUid());
            offlinePartnerJoin.setJoinTime(DateUtil.nowDateTime());
            offlinePartnerJoin.setStatus(ActivityJoinStatus.JOIN.getValue());
            offlinePartnerJoin.setCreateTime(DateUtil.nowDateTime());
            offlinePartnerJoinService.save(offlinePartnerJoin);
        }else{
            offlinePartnerJoin.setJoinTime(DateUtil.nowDateTime());
            offlinePartnerJoin.setStatus(ActivityJoinStatus.JOIN.getValue());
            offlinePartnerJoin.setUpdateTime(DateUtil.nowDateTime());
            offlinePartnerJoinService.updateById(offlinePartnerJoin);
        }

        OfflinePartnerEntity offlinePartner = offlinePartnerService.getById(request.getPartnerId());
        if(user.getGender() == Constant.MAN){
            offlinePartner.setManJoinNumber(offlinePartner.getManJoinNumber() + 1);
        }
        if(user.getGender() == Constant.WOMAN){
            offlinePartner.setWomanJoinNumber(offlinePartner.getWomanJoinNumber() + 1);
        }
        offlinePartner.setUpdateTime(DateUtil.nowDateTime());

        offlinePartnerService.updateById(offlinePartner);
    }

    @Override
    @Transactional
    public void exitPartner(UserEntity user, PartnerExitRequest request) {

        LambdaQueryWrapper<OfflinePartnerJoinEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OfflinePartnerJoinEntity::getUid, user.getUid());
        wrapper.eq(OfflinePartnerJoinEntity::getPartnerId, request.getPartnerId());
        OfflinePartnerJoinEntity offlinePartnerJoin = offlinePartnerJoinService.getOne(wrapper);
        offlinePartnerJoin.setStatus(ActivityJoinStatus.EXITED.getValue());
        offlinePartnerJoin.setExitTime(DateUtil.nowDateTime());
        offlinePartnerJoin.setUpdateTime(DateUtil.nowDateTime());
        offlinePartnerJoinService.updateById(offlinePartnerJoin);

        OfflinePartnerEntity offlinePartner = offlinePartnerService.getById(offlinePartnerJoin.getPartnerId());
        if(user.getGender() == Constant.MAN){
            offlinePartner.setManJoinNumber(offlinePartner.getManJoinNumber() - 1);
        }
        if(user.getGender() == Constant.WOMAN){
            offlinePartner.setWomanJoinNumber(offlinePartner.getWomanJoinNumber() - 1);
        }
        offlinePartner.setUpdateTime(DateUtil.nowDateTime());
        offlinePartnerService.updateById(offlinePartner);

        user.setCancelPartnerCount(user.getCancelPartnerCount() + 1);
        user.setCancelPartnerLastTime(DateUtil.nowDateTime());
        user.setUpdateTime(DateUtil.nowDateTime());
        userService.updateAndDeleteCache(user);

        subscribeMessageService.cancelSubscribe(offlinePartner.getId().toString(), user.getUid(), "offlinePartnerStart");

    }

    @Override
    public List<MemberResponse> getMmemberList(UserEntity user, Integer partnerId) {
        List<MemberResponseDTO> dtoList = offlinePartnerJoinService.getMemberList(user.getUid(), partnerId);
        return ObjectMapperUtil.convert(dtoList, MemberResponse.class);
    }

    @Override
    public void overPartner(UserEntity user, PartnerOverRequest request) {
        OfflinePartnerEntity offlinePartner = offlinePartnerService.getById(request.getPartnerId());
        offlinePartner.setStatus(CommonStatus.DEFAULT.getValue());
        offlinePartner.setUpdateTime(DateUtil.nowDateTime());
        offlinePartnerService.updateById(offlinePartner);
    }

    @Override
    @Transactional
    public void deletePartner(UserEntity user, PartnerDeleteRequest request) {
        offlinePartnerService.removeById(request.getPartnerId());
        LambdaQueryWrapper<OfflinePartnerJoinEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OfflinePartnerJoinEntity::getPartnerId, request.getPartnerId());
        offlinePartnerJoinService.remove(wrapper);
    }
}