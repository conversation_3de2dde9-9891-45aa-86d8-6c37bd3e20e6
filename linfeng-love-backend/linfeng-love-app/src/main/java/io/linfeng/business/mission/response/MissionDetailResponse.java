package io.linfeng.business.mission.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(description = "任务详情响应对象")
public class MissionDetailResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 阶段目标值
	 */
	@ApiModelProperty(value="阶段目标值")
	private Integer targetValue;
	/**
	 * 当前阶段奖励名称
	 */
	@ApiModelProperty(value="当前阶段奖励名称")
	private String prizeName;
	/**
	 * 当前阶段奖励值
	 */
	@ApiModelProperty(value="当前阶段奖励值")
	private Integer prizeValue;


}
