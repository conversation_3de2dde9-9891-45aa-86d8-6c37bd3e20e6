/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.business.offline.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.business.offline.request.*;
import io.linfeng.business.offline.response.MemberResponse;
import io.linfeng.business.offline.response.PartnerDetailResponse;
import io.linfeng.business.offline.response.PartnerSimpleResponse;
import io.linfeng.business.offline.response.PartnerSimpleResponse;
import io.linfeng.business.offline.service.AppOfflinePartnerService;
import io.linfeng.business.pay.response.PrePayResultResponse;
import io.linfeng.common.annotation.Login;
import io.linfeng.common.annotation.LoginUser;
import io.linfeng.common.api.R;
import io.linfeng.common.api.Result;
import io.linfeng.love.user.entity.UserEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * 线下找搭子
 *
 * <AUTHOR>
 * @date 2024-03-28 14:26:17
 */
@RestController
@RequestMapping("app/offline/partner")
@Api(tags = "线下找搭子Api")
@AllArgsConstructor
public class AppOfflinePartnerController {

    private final AppOfflinePartnerService appOfflinePartnerService;

    /**
     * 创建活动
     * @param user 登录用户
     * @param request 加入活动请求
     * @return 加入结果
     */
    @Login
    @ApiOperation(value = "创建活动")
    @PostMapping("/create")
    public R join(@ApiIgnore @LoginUser UserEntity user, @RequestBody PartnerCreateRequest request){
        appOfflinePartnerService.createPartner(user, request);
        return R.ok();
    }

    /**
     * 搭子活动列表
     * @param user 登录用户
     * @return 搭子活动列表
     */
    @Login
    @GetMapping("/list")
    @ApiOperation("线下活动列表")
    public Result<IPage<PartnerSimpleResponse>> list(@ApiIgnore @LoginUser UserEntity user, PartnerQueryRequest request){
        IPage<PartnerSimpleResponse> activityList = appOfflinePartnerService.getOfflinePartnerList(user, request);
        return new Result<IPage<PartnerSimpleResponse>>().ok(activityList);
    }

    /**
     * 我的活动列表
     * @param user 登录用户
     * @return 我的活动列表
     */
    @Login
    @GetMapping("/list/my")
    @ApiOperation("我的活动列表")
    public Result<IPage<PartnerSimpleResponse>> myList(@ApiIgnore @LoginUser UserEntity user, PartnerQueryRequest request) {
        IPage<PartnerSimpleResponse> activityList = appOfflinePartnerService.getMyPartnerList(user, request);
        return new Result<IPage<PartnerSimpleResponse>>().ok(activityList);
    }


    /**
     * 查询活动详情
     * @param user 登录用户
     * @param id 查询请求
     * @return 活动详情
     */
    @Login
    @GetMapping("/detail/{id}")
    @ApiOperation("查询活动详情")
    public Result<PartnerDetailResponse> detail(@ApiIgnore @LoginUser UserEntity user, @PathVariable("id") Integer id){
        PartnerDetailResponse activityDetail = appOfflinePartnerService.getOfflinePartnerDetail(user, id);
        return new Result<PartnerDetailResponse>().ok(activityDetail);
    }

    /**
     * 加入活动
     * @param user 登录用户
     * @param request 加入活动请求
     * @return 加入结果
     */
    @Login
    @ApiOperation(value = "加入活动")
    @PostMapping("/join")
    public R join(@ApiIgnore @LoginUser UserEntity user, @RequestBody PartnerJoinRequest request){
        appOfflinePartnerService.joinPartner(user, request);
        return R.ok();
    }

    /**
     * 退出活动
     * @param user 登录用户
     * @param request 加入活动请求
     * @return 退出结果
     */
    @Login
    @ApiOperation(value = "退出活动")
    @PostMapping("/exit")
    public R exit(@ApiIgnore @LoginUser UserEntity user, @RequestBody PartnerExitRequest request){
        appOfflinePartnerService.exitPartner(user, request);
        return R.ok();
    }

    /**
     * 结束活动
     * @param user 登录用户
     * @param request 结束活动请求
     * @return 结束结果
     */
    @Login
    @ApiOperation(value = "结束活动")
    @PostMapping("/over")
    public R over(@ApiIgnore @LoginUser UserEntity user, @RequestBody PartnerOverRequest request){
        appOfflinePartnerService.overPartner(user, request);
        return R.ok();
    }

    /**
     * 搭子成员列表
     * @param user 登录用户
     * @param partnerId 搭子活动id
     * @return 搭子成员列表
     */
    @Login
    @GetMapping("/member/list")
    @ApiOperation("搭子成员列表")
    public Result<List<MemberResponse>> memberList(@ApiIgnore @LoginUser UserEntity user, Integer partnerId) {
        List<MemberResponse> memberList = appOfflinePartnerService.getMmemberList(user, partnerId);
        return new Result<List<MemberResponse>>().ok(memberList);
    }

    /**
     * 删除活动
     * @param user 登录用户
     * @param request 删除活动请求
     * @return 删除结果
     */
    @Login
    @ApiOperation(value = "删除活动")
    @PostMapping("/delete")
    public R join(@ApiIgnore @LoginUser UserEntity user, @RequestBody PartnerDeleteRequest request){
        appOfflinePartnerService.deletePartner(user, request);
        return R.ok();
    }

}
