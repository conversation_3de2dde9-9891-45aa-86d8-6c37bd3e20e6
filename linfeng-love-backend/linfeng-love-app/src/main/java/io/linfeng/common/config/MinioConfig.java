package io.linfeng.common.config;

import io.linfeng.common.utils.Constant;
import io.linfeng.love.config.service.ConfigSystemService;
import io.minio.MinioClient;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * minio配置
 * <AUTHOR>
 */
@Data
@Configuration
public class MinioConfig {

    private final ConfigSystemService configService;

    public MinioConfig(ConfigSystemService configService) {
        this.configService = configService;
    }

    /**
     * 创建Minio客户端
     * @return MinioClient
     */
    @Bean
    public MinioClient minioClient() {
        return MinioClient.builder()
                .endpoint(configService.getValue(Constant.MINIO_UPLOAD_URL))
                .credentials(configService.getValue(Constant.MINIO_ACCESS_KEY), configService.getValue(Constant.MINIO_SECRET_KEY))
                .build();
    }
}
