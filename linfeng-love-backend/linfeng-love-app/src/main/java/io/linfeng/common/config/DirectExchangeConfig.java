package io.linfeng.common.config;

import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import org.springframework.amqp.core.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.Inet4Address;
import java.net.InetAddress;

@Configuration
public class DirectExchangeConfig {

    @Value("${netty.port}")
    private Integer port ;

    /**
     * 动态队列名称
     */
    private static String queueName;

    /**
     * 创建直连交换机
     * @return
     */
    @Bean
    public DirectExchange directExchange() {
        return ExchangeBuilder
                .directExchange(Constant.DIRECT_EXCHANGE_NAME)//交换机类型 ;参数为名字
                .durable(true)//是否持久化，true即存到磁盘,false只在内存上
                .build();
    }

    //推送消息队列
    @Bean("queue")
    public Queue pushQueue() {
        InetAddress localHost = null;
        try {
            localHost = Inet4Address.getLocalHost();
        } catch (Exception e) {
            throw new LinfengException("获取当前服务器IP地址出错");
        }
        String ip = localHost.getHostAddress();
        queueName = ip + ":" + port + Constant.QUEUE_PUSH_MESSAGE;
        return new Queue(queueName);
    }


    //交换机绑定推送消息队列
    @Bean
    public Binding bindImSessionQueue(){
        return BindingBuilder
                .bind(pushQueue())
                .to(directExchange())
                .with(Constant.PUSH_ROUTING_NAME);
    }
}
