/**
 * -----------------------------------
 *  Copyright (c) 2021-2023
 *  All rights reserved, Designed By www.linfeng.tech
 *  林风婚恋交友商业版本请务必保留此注释头信息
 *  商业版授权联系技术客服	 QQ:  973921677/3582996245
 *  严禁分享、盗用、转卖源码或非法牟利！
 *  版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.common.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.WxMaConfig;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import io.linfeng.common.utils.Constant;
import io.linfeng.love.config.service.ConfigSystemService;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.WxMpConfigStorage;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WechatConfig {

    private final ConfigSystemService configService;

    public WechatConfig(ConfigSystemService configService) {
        this.configService = configService;
    }

    /**
     * 微信小程序配置
     * @return 微信小程序配置
     */
    @Bean
    public WxMaConfig wxMaConfig(){
        WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
        config.setAppid(configService.getValue(Constant.WX_MA_APP_ID));
        config.setSecret(configService.getValue(Constant.WX_MA_APP_SECRET));
        return config;
    }

    @Bean
    public WxMaService wxMaService(WxMaConfig config){
        WxMaService wxMaService = new WxMaServiceImpl();
        wxMaService.setWxMaConfig(config);
        return wxMaService;
    }

    @Bean
    public WxPayService wxPayService(){
        WxPayConfig payConfig = new WxPayConfig();
        payConfig.setMchId(configService.getValue(Constant.WX_MCH_ID));
        payConfig.setMchKey(configService.getValue(Constant.WX_MCH_KEY));
        payConfig.setNotifyUrl(configService.getValue(Constant.WX_PAY_REDIRECT_URL));
        // 可以指定是否使用沙箱环境
        payConfig.setUseSandboxEnv(false);

        WxPayService wxPayService = new WxPayServiceImpl();
        wxPayService.setConfig(payConfig);
        return wxPayService;
    }

    /**
     * 微信公众号配置
     * @return 微信公众号配置
     */
    @Bean
    public WxMpConfigStorage wxMpConfig(){
        WxMpDefaultConfigImpl config = new WxMpDefaultConfigImpl();
        config.setAppId(configService.getValue(Constant.WX_MP_APP_ID));
        config.setSecret(configService.getValue(Constant.WX_MP_APP_SECRET));
        return config;
    }

    @Bean
    public WxMpService wxMpService(WxMpConfigStorage config){
        WxMpService wxMpService = new WxMpServiceImpl();
        wxMpService.setWxMpConfigStorage(config);
        return wxMpService;
    }

}