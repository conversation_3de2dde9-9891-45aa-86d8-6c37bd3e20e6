spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        druid:
            driver-class-name: com.mysql.cj.jdbc.Driver
            url: ********************************************************************************************************************************
            username: root
            password: root
            initial-size: 10
            max-active: 100
            min-idle: 10
            max-wait: 60000
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            stat-view-servlet:
                enabled: true
                url-pattern: /druid/*
                #login-username: admin
                #login-password: admin
            filter:
                stat:
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: false
                wall:
                    config:
                        multi-statement-allow: true
    jackson:
      date-format: yyyy-MM-dd HH:mm:ss
      time-zone: GMT+8
    redis:
      open: true  # 是否开启redis缓存  true开启   false关闭
      database: 0
      host: localhost
      port: 6379
      password:    # 密码（默认为空）
      timeout: 6000ms  # 连接超时时长（毫秒）
      jedis:
        pool:
          max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
          max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-idle: 10      # 连接池中的最大空闲连接
          min-idle: 5       # 连接池中的最小空闲连接
    rabbitmq:
      host: 127.0.0.1
      port: 5672
      username: guest
      password: guest
      listener:
        simple:
          retry:
            enabled: true           # 开启消费者出现异常情况下，进行重试消费，默认false
            max-attempts: 5         # 最大重试次数，默认为3
            initial-interval: 3000  # 重试间隔时间，默认1000(单位毫秒)

logging:
  level:
    io.linfeng: DEBUG
    org.springframework.web: DEBUG
    io.swagger.*: ERROR
