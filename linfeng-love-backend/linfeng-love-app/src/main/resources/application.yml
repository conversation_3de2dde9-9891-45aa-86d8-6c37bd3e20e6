# Tomcat
server:
  tomcat:
    uri-encoding: UTF-8
    max-threads: 1000
    min-spare-threads: 30
  port: 8080
  servlet:
    context-path:
netty:
  port: 8081

spring:
  # 环境 dev|test|prod
  profiles:
    active: dev
  # jackson时间格式化
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  mvc:
    throw-exception-if-no-handler-found: true
  application:
    name: linfeng-love


#  resources:
#    add-mappings: false


#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: io.linfeng.love.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      logic-delete-value: -1
      logic-not-delete-value: 0
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'

task:
  pool:
    # 核心线程池大小
    core-pool-size: 10
    # 最大线程数
    max-pool-size: 30
    # 活跃时间
    keep-alive-seconds: 60
    # 队列容量
    queue-capacity: 50

linfeng:
  redis:
    open: false
  shiro:
    redis: false
  # APP模块，是通过jwt认证的，如果要使用APP模块，则需要修改【加密秘钥】
  jwt:
    # 加密秘钥
    secret: f4e2e52034348f86b67cde581c0f9eb5
    # token有效时长，7天，单位秒
    expire: 604800
  email:
    send: <EMAIL>  #发送方的邮箱地址
#文件大小限制
qiniu:
  # 文件大小 单位M
  max-size: 30

