package io.linfeng.push.consumer;

import com.google.gson.Gson;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import io.linfeng.push.core.UserChannel;
import io.linfeng.push.message.PushMessage;
import io.netty.channel.Channel;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.stereotype.Component;

import java.net.Inet4Address;
import java.net.InetAddress;

@Component
@Slf4j
public class PushMessageConsumer {

    private static Gson gson = new Gson();

    private final UserChannel userChannel;

    public PushMessageConsumer(UserChannel userChannel) {
        this.userChannel = userChannel;
    }

    @RabbitListener(queues = "#{queue.name}")
    public void consumePushMessage(String message) {
        PushMessage pushMessage = gson.fromJson(message, PushMessage.class);
        Channel channel = userChannel.findChannel(pushMessage.getReceiverUid());
        if(channel == null){
            log.info("用户[{}]不在此服务器登录或未登录,推送消息丢弃", pushMessage.getReceiverUid());
            return;
        }
        channel.writeAndFlush(new TextWebSocketFrame(message));
    }
}
