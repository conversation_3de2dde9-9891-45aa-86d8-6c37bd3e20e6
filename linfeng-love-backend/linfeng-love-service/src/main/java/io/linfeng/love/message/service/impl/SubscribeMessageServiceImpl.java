package io.linfeng.love.message.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.love.message.enums.SubscribeStatus;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.message.dao.SubscribeMessageDao;
import io.linfeng.love.message.entity.SubscribeMessageEntity;
import io.linfeng.love.message.service.SubscribeMessageService;


@Service("subscribeMessageService")
public class SubscribeMessageServiceImpl extends ServiceImpl<SubscribeMessageDao, SubscribeMessageEntity> implements SubscribeMessageService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<SubscribeMessageEntity> page = this.page(
                new Page<>(pageNum, pageSize)
        );

        return new PageObject(page);
    }

    @Override
    public void cancelSubscribe(String linkId, Integer uid, String messageType) {
        LambdaQueryWrapper<SubscribeMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SubscribeMessageEntity::getMessageType, messageType);
        wrapper.eq(SubscribeMessageEntity::getLinkId, linkId);
        wrapper.eq(SubscribeMessageEntity::getUid, uid);
        SubscribeMessageEntity subscribeMessageEntity = this.getOne(wrapper);
        if(subscribeMessageEntity != null){
            subscribeMessageEntity.setSubscribeStatus(SubscribeStatus.NO.getValue());
            subscribeMessageEntity.setUpdateTime(DateUtil.nowDateTime());
            this.updateById(subscribeMessageEntity);
        }
    }

    @Override
    public void updateSendTime(String linkId, Integer uid, String messageType, Date sendTime) {
        LambdaQueryWrapper<SubscribeMessageEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SubscribeMessageEntity::getMessageType, messageType);
        wrapper.eq(SubscribeMessageEntity::getLinkId, linkId);
        wrapper.eq(SubscribeMessageEntity::getUid, uid);
        SubscribeMessageEntity subscribeMessageEntity = this.getOne(wrapper);
        if(subscribeMessageEntity != null){
            subscribeMessageEntity.setSendTime(sendTime);
            subscribeMessageEntity.setUpdateTime(DateUtil.nowDateTime());
            this.updateById(subscribeMessageEntity);
        }
    }

}