/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.love.executor;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.love.common.enums.PrizeOrigin;
import io.linfeng.love.mission.entity.MissionDetailEntity;
import io.linfeng.love.mission.entity.MissionEntity;
import io.linfeng.love.mission.entity.UserMissionEntity;
import io.linfeng.love.mission.entity.UserMissionReceiveEntity;
import io.linfeng.love.mission.enums.MissionActivity;
import io.linfeng.love.mission.enums.MissionTarget;
import io.linfeng.love.mission.enums.MissionType;
import io.linfeng.love.mission.enums.UserMissionStatus;
import io.linfeng.love.mission.service.MissionDetailService;
import io.linfeng.love.mission.service.MissionService;
import io.linfeng.love.mission.service.UserMissionReceiveService;
import io.linfeng.love.mission.service.UserMissionService;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.service.UserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 任务事件执行器
 * 执行器模块是app和admin两个系统都能用到的模块，因为业务复杂度不是很高，没有考虑做拆分，暂时放在service模块下
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
@Service("missionEventExecutor")
public class MissionEventExecutor {

    //提高响应速度，部分方法异步执行
    private static ExecutorService threadPool = Executors.newFixedThreadPool(9);

    private final MissionService missionService;

    private final MissionDetailService missionDetailService;

    private final UserMissionService userMissionService;

    private final PrizeEventExecutor prizeEventExecutor;

    private final UserMissionReceiveService userMissionReceiveService;

    private final UserService userService;

    public MissionEventExecutor(MissionService missionService, MissionDetailService missionDetailService, UserMissionService userMissionService, PrizeEventExecutor prizeEventExecutor, UserMissionReceiveService userMissionReceiveService, UserService userService) {
        this.missionService = missionService;
        this.missionDetailService = missionDetailService;
        this.userMissionService = userMissionService;
        this.prizeEventExecutor = prizeEventExecutor;
        this.userMissionReceiveService = userMissionReceiveService;
        this.userService = userService;
    }

    @Transactional
    public void receiveNewUserMission(UserEntity user){
        threadPool.execute(() -> {
            LambdaQueryWrapper<UserMissionReceiveEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserMissionReceiveEntity::getUid, user.getUid());
            UserMissionReceiveEntity userMissionReceive = userMissionReceiveService.getOne(wrapper);
            //根据有无记录判断是否领取过新手任务
            if(userMissionReceive == null){
                userMissionReceive = new UserMissionReceiveEntity();
                userMissionReceive.setUid(user.getUid());
                userMissionReceive.setCreateTime(DateUtil.nowDateTime());
                userMissionReceiveService.save(userMissionReceive);
                userMissionService.receiveActivityMissionList(user.getUid(), MissionActivity.NEW_USER.getValue());
            }

        });
    }

    @Transactional
    public void receiveToDayMissionList(UserEntity user) {
        threadPool.execute(() -> {
            LambdaQueryWrapper<UserMissionReceiveEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserMissionReceiveEntity::getUid, user.getUid());
            UserMissionReceiveEntity userMissionReceive = userMissionReceiveService.getOne(wrapper);
            //领取新手任务的时候会创建一条任务领取记录，此处无需进行非空判断
            //日任务领取
            checkAndReceiveDayMission(userMissionReceive, user);
            //周任务领取
            checkAndReceiveWeekMission(userMissionReceive, user);
            //月任务领取
            checkAndReceiveMonthMission(userMissionReceive, user);
        });
    }

    @Transactional
    public void updateUserMissionTarget(Integer uid, MissionTarget targetCode, Integer currentValue) {
        threadPool.execute(() -> {
            UserEntity user = userService.getUserByUid(uid);
            //所有添加了该指标的任务都更新进度
            LambdaQueryWrapper<MissionEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MissionEntity::getTargetCode, targetCode.getValue());
            List<MissionEntity> targetMissionList = missionService.list(wrapper);
            for(MissionEntity mission: targetMissionList){
                LambdaQueryWrapper<UserMissionEntity> userWrapper = new LambdaQueryWrapper<>();
                userWrapper.eq(UserMissionEntity::getUid, uid);
                userWrapper.eq(UserMissionEntity::getMissionId, mission.getId());
                UserMissionEntity userMission = userMissionService.getOne(userWrapper);
                if(userMission !=null && userMission.getStatus() == UserMissionStatus.COMPLETE.getValue()){
                    continue;
                }
                //基础任务进度更新直接为已完成
                if(mission.getType() == MissionType.BASE.getValue()){
                    userMission.setCurrentValue(currentValue);
                    userMission.setStatus(UserMissionStatus.COMPLETE.getValue());
                    userMission.setUpdateTime(DateUtil.nowDateTime());
                    userMissionService.updateAndRefreshCache(userMission, mission.getActivityCode());
                    //发放奖励
                    prizeEventExecutor.awardPrize(user, mission.getPrizeType(), mission.getPrizeValue(), PrizeOrigin.MISSION.getValue());
                    continue;
                }
                //成长任务更新需判断完成进度,传入进度值比当前进度小的就不直接跳过了
                //传入进度小的场景举例：任务需要上传3张照片，用户先上传3张，再删除，再上传2张，进度为3，传入为2
                if(mission.getType() == MissionType.DEVELOP.getValue() && currentValue > userMission.getCurrentValue()){
                    LambdaQueryWrapper<MissionDetailEntity> detailWrapper = new LambdaQueryWrapper<>();
                    detailWrapper.eq(MissionDetailEntity::getMissionId, mission.getId());
                    List<MissionDetailEntity> missionDetailList = missionDetailService.list(detailWrapper);
                    int completeValue = 0;
                    for(int i=0; i<missionDetailList.size(); i++){
                        //过滤重复达标的进度
                        if(userMission.getCurrentValue() >= missionDetailList.get(i).getTargetValue()){
                            completeValue ++;
                            continue;
                        }
                        if(currentValue >= missionDetailList.get(i).getTargetValue()){
                            completeValue ++;
                            //发放奖励
                            prizeEventExecutor.awardPrize(user, missionDetailList.get(i).getPrizeType(), missionDetailList.get(i).getPrizeValue(), PrizeOrigin.MISSION.getValue());
                        }
                    }
                    userMission.setCurrentValue(currentValue);
                    if(completeValue == missionDetailList.size()){
                        userMission.setStatus(UserMissionStatus.COMPLETE.getValue());
                    }else{
                        userMission.setStatus(UserMissionStatus.PART_COMPLETE.getValue());
                    }

                    userMission.setUpdateTime(DateUtil.nowDateTime());
                    userMissionService.updateAndRefreshCache(userMission, mission.getActivityCode());
                }
            }

        });
    }

    private void checkAndReceiveDayMission(UserMissionReceiveEntity userMissionReceive, UserEntity user){
        if(userMissionReceive.getDayMissionReceiveTime() != null
                && userMissionReceive.getDayMissionReceiveTime().getTime() > DateUtil.getToDay().getTime()){
            return;
        }

        userMissionService.receiveActivityMissionList(user.getUid(), MissionActivity.BASE_DAY.getValue());

        userMissionReceive.setDayMissionReceiveTime(DateUtil.nowDateTime());
        userMissionReceive.setUpdateTime(DateUtil.nowDateTime());
        userMissionReceiveService.updateById(userMissionReceive);
    }

    private void checkAndReceiveWeekMission(UserMissionReceiveEntity userMissionReceive, UserEntity user){
        //系统暂未设计周任务，方法预留
    }

    private void checkAndReceiveMonthMission(UserMissionReceiveEntity userMissionReceive, UserEntity user){
        //系统暂未设计月任务，方法预留
    }

}

