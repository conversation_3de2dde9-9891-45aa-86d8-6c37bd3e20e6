package io.linfeng.love.guest.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.love.guest.dto.response.GuestSimpleResponseDTO;
import io.linfeng.love.guest.entity.GuestQualityEntity;

import java.util.List;
import java.util.Map;

/**
 * 推荐精选信息表
 *
 * <AUTHOR>
 * @date 2023-09-01 15:20:59
 */
public interface GuestQualityService extends IService<GuestQualityEntity> {
    /**
     * 获取精选嘉宾列表
     * @param params 条件
     * @return 精选嘉宾列表
     */
    List<GuestSimpleResponseDTO> selectQualityGuestList(Map<String, Object> params);
}

