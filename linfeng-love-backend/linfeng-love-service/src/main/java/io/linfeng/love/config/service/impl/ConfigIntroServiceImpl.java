package io.linfeng.love.config.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.dao.ConfigIntroDao;
import io.linfeng.love.config.dto.response.ConfigIntroResponseDTO;
import io.linfeng.love.config.entity.ConfigIntroEntity;
import io.linfeng.love.config.service.ConfigIntroService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service("configIntroService")
public class ConfigIntroServiceImpl extends ServiceImpl<ConfigIntroDao, ConfigIntroEntity> implements ConfigIntroService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<ConfigIntroEntity> page = this.page(
                new Page<>(pageNum, pageSize)
        );
        return new PageObject(page);
    }

    @Override
    public List<ConfigIntroResponseDTO> getConfigList() {
        List<ConfigIntroResponseDTO> configList = new ArrayList<>();
        LambdaQueryWrapper<ConfigIntroEntity> wrapper =new LambdaQueryWrapper<>();
        wrapper.orderByAsc(ConfigIntroEntity::getSort);
        List<ConfigIntroEntity> entityList = this.list(wrapper);
        entityList.forEach(entity -> {
            ConfigIntroResponseDTO response = new ConfigIntroResponseDTO();
            response.setId(entity.getId());
            response.setTitle(entity.getTitle());
            response.setIcon(entity.getIcon());
            configList.add(response);
        });
        return configList;
    }

}