package io.linfeng.love.mission.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.common.utils.StringUtil;
import io.linfeng.love.moment.entity.MomentEntity;
import org.springframework.stereotype.Service;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.mission.dao.MissionActivityDao;
import io.linfeng.love.mission.entity.MissionActivityEntity;
import io.linfeng.love.mission.service.MissionActivityService;


@Service("missionActivityService")
public class MissionActivityServiceImpl extends ServiceImpl<MissionActivityDao, MissionActivityEntity> implements MissionActivityService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        LambdaQueryWrapper<MissionActivityEntity> wrapper = new LambdaQueryWrapper<>();
        if(!StringUtil.isEmpty(params.get("name"))){
            wrapper.like(MissionActivityEntity::getName, params.get("name"));
        }
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<MissionActivityEntity> page = this.page(
                new Page<>(pageNum, pageSize),
                wrapper
        );

        return new PageObject(page);
    }

}