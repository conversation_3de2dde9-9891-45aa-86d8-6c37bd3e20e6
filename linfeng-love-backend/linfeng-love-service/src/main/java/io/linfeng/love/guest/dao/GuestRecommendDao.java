package io.linfeng.love.guest.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.love.guest.dto.response.GuestSimpleResponseDTO;
import io.linfeng.love.guest.entity.GuestRecommendEntity;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.entity.UserPreferencesEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 推荐用户信息表
 * 
 * <AUTHOR>
 * @date 2023-09-01 15:20:59
 */
@Mapper
public interface GuestRecommendDao extends BaseMapper<GuestRecommendEntity> {

    List<UserEntity> getRecommendGuestList(@Param("p") UserPreferencesEntity userPreferences, @Param("m") Map<String, Object> map);

    List<GuestSimpleResponseDTO> getRecommendGuestListByUid(@Param("guestUidList")List<Integer> guestUidList, @Param("uid")Integer uid);

    /**
     * 获取历史推荐记录
     * @param params 查询条件
     * @return 历史推荐记录
     */
    List<GuestSimpleResponseDTO> getHistoryRecommend(@Param("param") Map<String, Object> params);

    /**
     * 获取已经推荐过的嘉宾uid列表
     * @param uid 客户uid
     * @return 已经推荐过的嘉宾uid列表
     */
    List<Integer> getExistGuestUidList(@Param("uid") Integer uid);

    /**
     * 获取最新的推荐嘉宾
     * @param uid 客户uid
     * @return 最新的推荐嘉宾
     */
    GuestRecommendEntity getLatestRecommend(@Param("uid")Integer uid);

    /**
     * 删除未操作过的推荐记录
     * @param uid 用户id
     */
    void deleteUnOperatorRecommend(@Param("uid")Integer uid);

    /**
     * 获取附近的嘉宾列表
     * @param dtoPage 分页条件
     * @param longitude 经度
     * @param latitude 维度
     * @return 附近的嘉宾列表
     */
    IPage<GuestSimpleResponseDTO> selectVicinityGuestPage(IPage<GuestSimpleResponseDTO> dtoPage, @Param("uid")Integer uid, @Param("longitude") String longitude, @Param("latitude") String latitude, @Param("gender")Integer gender);

}
