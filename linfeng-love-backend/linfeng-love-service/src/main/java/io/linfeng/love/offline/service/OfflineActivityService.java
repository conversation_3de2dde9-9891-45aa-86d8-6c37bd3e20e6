package io.linfeng.love.offline.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.offline.dto.response.ActivityDetailResponseDTO;
import io.linfeng.love.offline.dto.response.ActivitySimpleResponseDTO;
import io.linfeng.love.offline.entity.OfflineActivityEntity;

import java.util.Map;

/**
 * 线下活动表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 09:00:53
 */
public interface OfflineActivityService extends IService<OfflineActivityEntity> {

    PageObject queryPage(Map<String, Object> params);

    IPage<ActivitySimpleResponseDTO> getActivityList(IPage<ActivitySimpleResponseDTO> page, Integer uid, String city, Integer queryType);

    IPage<ActivitySimpleResponseDTO> getMyActivityList(IPage<ActivitySimpleResponseDTO> page, Integer uid);

    ActivityDetailResponseDTO getActivityDetail(Integer id, Integer uid);
}

