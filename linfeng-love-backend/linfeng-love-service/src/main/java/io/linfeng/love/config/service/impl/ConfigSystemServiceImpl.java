package io.linfeng.love.config.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.utils.RedisUtil;
import io.linfeng.common.utils.StringUtil;
import io.linfeng.love.config.dao.ConfigSystemDao;
import io.linfeng.love.config.entity.ConfigSystemEntity;
import io.linfeng.love.config.service.ConfigSystemService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("configSystemService")
public class ConfigSystemServiceImpl extends ServiceImpl<ConfigSystemDao, ConfigSystemEntity> implements ConfigSystemService {

	private final RedisUtil redisUtil;

	public ConfigSystemServiceImpl(RedisUtil redisUtil) {
		this.redisUtil = redisUtil;
	}

	@Override
	public String getValue(String key) {
		String paramValue = redisUtil.getString(ConfigSystemEntity.CACHE_CONFIG_SYSTEM_PREFIX + key);
		if (!StringUtil.isEmpty(paramValue)) {
			return paramValue;
		}
		//重新获取用户信息
		LambdaQueryWrapper<ConfigSystemEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
		lambdaQueryWrapper.eq(ConfigSystemEntity::getParamKey, key);
		ConfigSystemEntity configSystemEntity = getOne(lambdaQueryWrapper);
		redisUtil.setString(ConfigSystemEntity.CACHE_CONFIG_SYSTEM_PREFIX + key, configSystemEntity.getParamValue(), 7200);
		return configSystemEntity.getParamValue();
	}

	@Override
	public void updateAndRefreshCache(List<ConfigSystemEntity> configSystemEntityList) {
		configSystemEntityList.forEach(configSystemEntity -> {
			LambdaQueryWrapper<ConfigSystemEntity> wrapper = Wrappers.lambdaQuery();
			wrapper.eq(ConfigSystemEntity::getParamKey, configSystemEntity.getParamKey());
			update(configSystemEntity, wrapper);
			redisUtil.setString(ConfigSystemEntity.CACHE_CONFIG_SYSTEM_PREFIX + configSystemEntity.getParamKey(), configSystemEntity.getParamValue(), 7200);
		});
	}

}
