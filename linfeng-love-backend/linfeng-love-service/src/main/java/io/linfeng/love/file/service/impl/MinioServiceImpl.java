package io.linfeng.love.file.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import io.linfeng.love.config.service.ConfigSystemService;
import io.linfeng.love.file.service.MinioService;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * minio存储
 *
 */
@Slf4j
@Component
@Data
public class MinioServiceImpl implements MinioService {

    private String bucketName;

    private String domain;

    private final MinioClient minioClient;

    private final ConfigSystemService configSystemService;

    public MinioServiceImpl(ConfigSystemService configSystemService, MinioClient minioClient){
        this.configSystemService = configSystemService;
        this.minioClient = minioClient;
        this.bucketName = configSystemService.getValue(Constant.MINIO_BUCKET_NAME);
        this.domain = configSystemService.getValue(Constant.MINIO_DOMAIN);
    }

    @Override
    public String upload(MultipartFile file, byte[] data, String suffix) {
        // 文件流
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(data);
        try{
            // 文件路径
            String filePath = generatePath(suffix);
            // 存储到Minio
            minioClient.putObject(PutObjectArgs.builder()
                    .object(filePath)
                    .bucket(bucketName)
                    .stream(byteArrayInputStream, byteArrayInputStream.available(), -1)
                    .contentType(file.getContentType())
                    .build());

            return  domain + "/" + bucketName + filePath;
        }catch (Exception e){
            log.error("文件上传失败", e);
            throw  new LinfengException("文件上传失败");
        }finally {
            IOUtils.closeQuietly(byteArrayInputStream);
        }
    }

    private String generatePath(String suffix){
        //生成uuid
        String uuid = IdUtil.fastSimpleUUID();

        //文件路径
        String path = "/"
                + DateUtil.format(new Date(), "yyyyMMdd")
                + "/" + uuid
                + suffix;
        return path;
    }
}
