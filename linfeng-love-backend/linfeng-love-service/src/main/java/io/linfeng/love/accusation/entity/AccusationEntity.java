package io.linfeng.love.accusation.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户举报表
 * 
 * <AUTHOR>
 * @date 2023-10-19 10:59:41
 */
@Data
@TableName("lf_accusation")
public class AccusationEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId
	private Integer id;
	/**
	 * 用户id
	 */
	private Integer uid;
	/**
	 * 描述
	 */
	private String content;
	/**
	 * 类型1用户 2动态 3评论
	 */
	private Integer type;
	/**
	 * 标签
	 */
	private Integer tag;
	/**
	 * 状态
	 */
	private Integer status;
	/**
	 * 平台反馈
	 */
	private String feedback;
	/**
	 * 关联id
	 */
	private String linkId;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
