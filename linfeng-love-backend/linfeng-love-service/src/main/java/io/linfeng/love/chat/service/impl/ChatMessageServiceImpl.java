package io.linfeng.love.chat.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.ObjectMapperUtil;
import io.linfeng.common.utils.StringUtil;
import io.linfeng.love.chat.dao.ChatMessageDao;
import io.linfeng.love.chat.dto.response.ChatMessageResponseDTO;
import io.linfeng.love.chat.entity.ChatMessageEntity;
import io.linfeng.love.chat.entity.ChatSessionEntity;
import io.linfeng.love.chat.service.ChatMessageService;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.service.UserService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service("chatMessageService")
public class ChatMessageServiceImpl extends ServiceImpl<ChatMessageDao, ChatMessageEntity> implements ChatMessageService {

    private final UserService userService;

    public ChatMessageServiceImpl(UserService userService) {
        this.userService = userService;
    }

    @Override
    public IPage<ChatMessageResponseDTO> selectMessagePage(Integer pageNum, Integer pageSize, ChatSessionEntity chatSession) {

        IPage<ChatMessageResponseDTO> page = new Page<>(pageNum, pageSize);

        Map<String, Object> map = new HashMap<>();
        map.put("sessionId", chatSession.getSessionId());
        map.put("createTime", chatSession.getCreateTime());
        IPage<ChatMessageResponseDTO> chatMessagePage = this.baseMapper.selectMessagePage(page, map);

        return chatMessagePage;
    }

    @Override
    public ChatMessageResponseDTO saveAndRefreshCache(ChatMessageEntity chatMessage) {
        this.save(chatMessage);

        ChatMessageResponseDTO chatMessageResponse = ObjectMapperUtil.convert(chatMessage, ChatMessageResponseDTO.class);
        UserEntity senderUser = userService.getUserByUid(chatMessage.getSenderUid());
        UserEntity receiverUser = userService.getUserByUid(chatMessage.getReceiverUid());
        chatMessageResponse.setSenderOid(senderUser.getOid());
        chatMessageResponse.setReceiverOid(receiverUser.getOid());
        chatMessageResponse.setSendTime(DateUtil.dateToStr(chatMessage.getSendTime(), DateUtil.DATE_FORMAT));

        return chatMessageResponse;
    }

    @Override
    public void updateAndRefreshCache(ChatMessageEntity chatMessage) {
        LambdaQueryWrapper<ChatMessageEntity> wrapper = Wrappers.lambdaQuery();
        if(!StringUtil.isEmpty(chatMessage.getMessageId())){
            wrapper.eq(ChatMessageEntity::getMessageId, chatMessage.getMessageId());
        }
        if(!StringUtil.isEmpty(chatMessage.getSessionId())){
            wrapper.eq(ChatMessageEntity::getSessionId, chatMessage.getSessionId());
        }
        chatMessage.setUpdateTime(DateUtil.nowDateTime());
        this.update(chatMessage, wrapper);

    }
}