package io.linfeng.love.guest.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.love.guest.dao.GuestVisitDao;
import io.linfeng.love.guest.dto.response.GuestSimpleResponseDTO;
import io.linfeng.love.guest.entity.GuestVisitEntity;
import io.linfeng.love.guest.service.GuestVisitService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("guestVisitService")
public class GuestVisitServiceImpl extends ServiceImpl<GuestVisitDao, GuestVisitEntity> implements GuestVisitService {

    @Override
    public Integer getLookMeUserCount(Integer uid) {
        return this.baseMapper.getLookMeUserCount(uid);
    }

    @Override
    public List<GuestSimpleResponseDTO> getLookMeList(Map<String, Object> params) {
        return this.baseMapper.getLookMeList(params);
    }
}