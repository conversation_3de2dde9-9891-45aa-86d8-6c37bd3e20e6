package io.linfeng.love.mission.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class MissionResponseDTO implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 任务id
	 */
	private String missionId;
	/**
	 * 任务名称
	 */
	private String missionName;
	/**
	 * 任务描述
	 */
	private String description;
	/**
	 * 任务类型
	 */
	private Integer type;
	/**
	 * 图标
	 */
	private String icon;
	/**
	 * 引导跳转路径
	 */
	private String guidePath;
	/**
	 * 是否导航页
	 */
	private Integer guideTabBar;
	/**
	 * 引导文字
	 */
	private String guideText;
	/**
	 * 状态
	 */
	private Integer status;
	/**
	 * 当前阶段值
	 */
	private Integer currentValue;
	/**
	 * 最大目标值
	 */
	private Integer maxTargetValue;
	/**
	 * 最大奖品
	 */
	private String maxPrizeName;
	/**
	 * 最大奖品奖励值
	 */
	private Integer maxPrizeValue;
	/**
	 * 详情列表（成长任务才有值）
	 */
	private List<MissionDetailResponseDTO> detailList;

}
