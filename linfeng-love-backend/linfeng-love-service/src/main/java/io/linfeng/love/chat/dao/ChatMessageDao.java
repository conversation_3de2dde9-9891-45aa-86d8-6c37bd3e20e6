package io.linfeng.love.chat.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.love.chat.dto.response.ChatMessageResponseDTO;
import io.linfeng.love.chat.entity.ChatMessageEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 查询消息列表
 * 
 * <AUTHOR>
 * @date 2023-09-19 09:57:37
 */
@Mapper
public interface ChatMessageDao extends BaseMapper<ChatMessageEntity> {

    IPage<ChatMessageResponseDTO>  selectMessagePage(IPage page, @Param("map") Map<String, Object> map);

}
