package io.linfeng.love.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 订阅消息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-04 08:35:26
 */
@Data
@TableName("lf_subscribe_message")
public class SubscribeMessageEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id",type = IdType.AUTO)
	private Integer id;
	/**
	 * 用户id
	 */
	private Integer uid;
	/**
	 * 小程序opend/微信公众号openid
	 */
	private String openid;
	/**
	 * ma/mp 小程序/公众号
	 */
	private String channel;
	/**
	 * 模板id
	 */
	private String tmplId;
	/**
	 * 消息内容json格式
	 */
	private String content;
	/**
	 * 消息类型
	 */
	private String messageType;
	/**
	 * 跳转地址
	 */
	private String url;
	/**
	 * 发送时间
	 */
	private Date sendTime;
	/**
	 * 发送状态
	 */
	private Integer sendStatus;
	/**
	 * 关联id
	 */
	private String linkId;
	/**
	 * 订阅类型 一次性/长期
	 */
	private Integer subscribeType;
	/**
	 * 订阅状态
	 */
	private Integer subscribeStatus;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
