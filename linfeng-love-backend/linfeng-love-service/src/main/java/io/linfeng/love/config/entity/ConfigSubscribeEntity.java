package io.linfeng.love.config.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 消息订阅配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 08:53:05
 */
@Data
@TableName("lf_config_subscribe")
public class ConfigSubscribeEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 类型
	 */
	private String type;
	/**
	 * 模板id
	 */
	private String tmplId;
	/**
	 * 名称
	 */
	private String name;
	/**
	 * 渠道ma/mp 小程序/公众号
	 */
	private String channel;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
