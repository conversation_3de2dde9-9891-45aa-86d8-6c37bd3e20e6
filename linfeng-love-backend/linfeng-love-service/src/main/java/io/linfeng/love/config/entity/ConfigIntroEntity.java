package io.linfeng.love.config.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户介绍信息项配置表
 * 
 * <AUTHOR>
 * @date 2023-08-29 16:06:38
 */
@Data
@TableName("lf_config_intro")
public class ConfigIntroEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 配置标题
	 */
	private String title;
	/**
	 * 配置图标
	 */
	private String icon;
	/**
	 * 提示信息
	 */
	private String placeholder;
	/**
	 * 序号
	 */
	private Integer sort;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
