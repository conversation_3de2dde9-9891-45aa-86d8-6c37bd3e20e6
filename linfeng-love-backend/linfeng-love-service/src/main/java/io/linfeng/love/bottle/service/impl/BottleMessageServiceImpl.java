package io.linfeng.love.bottle.service.impl;

import io.linfeng.love.bottle.dto.response.BottleMessageResponseDTO;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import io.linfeng.love.bottle.dao.BottleMessageDao;
import io.linfeng.love.bottle.entity.BottleMessageEntity;
import io.linfeng.love.bottle.service.BottleMessageService;


@Service("bottleMessageService")
public class BottleMessageServiceImpl extends ServiceImpl<BottleMessageDao, BottleMessageEntity> implements BottleMessageService {

    @Override
    public IPage<BottleMessageResponseDTO> selectMessagePage(Page<BottleMessageResponseDTO> page, Integer bottleId) {
        return this.baseMapper.selectMessagePage(page, bottleId);
    }
}