package io.linfeng.love.mission.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户任务领取记录表
 * 
 * <AUTHOR>
 * @date 2023-11-27 15:20:14
 */
@Data
@TableName("lf_user_mission_receive")
public class UserMissionReceiveEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 用户id
	 */
	private Integer uid;
	/**
	 * 每日任务领取时间
	 */
	private Date dayMissionReceiveTime;
	/**
	 * 周任务领取时间
	 */
	private Date weekMissionReceiveTime;
	/**
	 * 月任务领取时间
	 */
	private Date monthMissionReceiveTime;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
