package io.linfeng.love.config.service.impl;

import org.springframework.stereotype.Service;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.config.dao.ConfigExchangeCashDao;
import io.linfeng.love.config.entity.ConfigExchangeCashEntity;
import io.linfeng.love.config.service.ConfigExchangeCashService;


@Service("configExchangeCashService")
public class ConfigExchangeCashServiceImpl extends ServiceImpl<ConfigExchangeCashDao, ConfigExchangeCashEntity> implements ConfigExchangeCashService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<ConfigExchangeCashEntity> page = this.page(
                new Page<>(pageNum, pageSize)
        );

        return new PageObject(page);
    }

}