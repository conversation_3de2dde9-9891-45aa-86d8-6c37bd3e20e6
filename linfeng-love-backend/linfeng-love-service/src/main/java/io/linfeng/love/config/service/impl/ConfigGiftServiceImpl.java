package io.linfeng.love.config.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.common.utils.RedisUtil;
import io.linfeng.love.config.dto.response.ConfigGiftGroupResponseDTO;
import io.linfeng.love.config.dto.response.ConfigTagGroupResponseDTO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.config.dao.ConfigGiftDao;
import io.linfeng.love.config.entity.ConfigGiftEntity;
import io.linfeng.love.config.service.ConfigGiftService;


@Service("configGiftService")
public class ConfigGiftServiceImpl extends ServiceImpl<ConfigGiftDao, ConfigGiftEntity> implements ConfigGiftService {

    private static final String CACHE_GIFT_PREFIX = "config:gift:";

    private final RedisUtil redisUtils;

    public ConfigGiftServiceImpl(RedisUtil redisUtils) {
        this.redisUtils = redisUtils;
    }

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        LambdaQueryWrapper<ConfigGiftEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(ConfigGiftEntity::getSort);
        Integer groupId = Integer.parseInt((String)params.get("groupId"));
        wrapper.eq(ConfigGiftEntity::getGroupId, groupId);
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<ConfigGiftEntity> page = this.page(
                new Page<>(pageNum, pageSize),
                wrapper
        );

        return new PageObject(page);
    }

    @Override
    public List<ConfigGiftGroupResponseDTO> getGiftGroupList() {
        List<ConfigGiftGroupResponseDTO> tagGroupList = redisUtils.getList(CACHE_GIFT_PREFIX, ConfigGiftGroupResponseDTO.class);
        if (tagGroupList != null) {
            return tagGroupList;
        }
        tagGroupList = this.baseMapper.getGiftGroupList();
        redisUtils.set(CACHE_GIFT_PREFIX, tagGroupList, 7200);
        return tagGroupList;
    }

}