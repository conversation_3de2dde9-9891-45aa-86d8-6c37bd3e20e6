package io.linfeng.love.mission.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.mission.entity.UserMissionPrizeEntity;

import java.util.Map;

/**
 * 用户任务奖励表
 *
 * <AUTHOR>
 * @date 2023-11-27 14:05:19
 */
public interface UserMissionPrizeService extends IService<UserMissionPrizeEntity> {

    PageObject queryPage(Map<String, Object> params);
}

