package io.linfeng.love.offline.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.offline.dto.response.PartnerDetailResponseDTO;
import io.linfeng.love.offline.dto.response.PartnerSimpleResponseDTO;
import io.linfeng.love.offline.entity.OfflinePartnerEntity;

import java.util.List;
import java.util.Map;

/**
 * 线下找搭子表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-13 09:48:07
 */
public interface OfflinePartnerService extends IService<OfflinePartnerEntity> {

    PageObject queryPage(Map<String, Object> params);

    IPage<PartnerSimpleResponseDTO> getPartnerList(IPage<PartnerSimpleResponseDTO> dtoPage, Integer typeId, String city, Integer queryType);

    IPage<PartnerSimpleResponseDTO> getMyPartnerList(IPage<PartnerSimpleResponseDTO> dtoPage, Integer uid, Integer queryType);

    PartnerDetailResponseDTO getPartnerDetail(Integer id, Integer uid);
}

