package io.linfeng.love.accusation.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 举报附件表
 * 
 * <AUTHOR>
 * @date 2023-10-19 10:59:40
 */
@Data
@TableName("lf_accusation_media")
public class AccusationMediaEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 举报id
	 */
	private Integer accusationId;
	/**
	 * 文件名称
	 */
	private String mediaName;
	/**
	 * 文件类型
	 */
	private Integer mediaType;
	/**
	 * 文件路径
	 */
	private String url;
	/**
	 * 创建时间
	 */
	private Date createTime;

}
