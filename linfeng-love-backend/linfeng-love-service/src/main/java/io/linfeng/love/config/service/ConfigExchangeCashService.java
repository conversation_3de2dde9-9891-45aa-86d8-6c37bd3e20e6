package io.linfeng.love.config.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.entity.ConfigExchangeCashEntity;

import java.util.Map;

/**
 * 余额兑换现金表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-12 09:11:46
 */
public interface ConfigExchangeCashService extends IService<ConfigExchangeCashEntity> {

    PageObject queryPage(Map<String, Object> params);
}

