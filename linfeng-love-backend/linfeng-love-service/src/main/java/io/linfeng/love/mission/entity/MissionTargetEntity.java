package io.linfeng.love.mission.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 任务指标表
 * 
 * <AUTHOR>
 * @date 2023-11-30 16:27:37
 */
@Data
@TableName("lf_mission_target")
public class MissionTargetEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 编码
	 */
	private String code;
	/**
	 * 名称
	 */
	private String name;
	/**
	 * 图标
	 */
	private String icon;
	/**
	 * 引导跳转路径
	 */
	private String guidePath;
	/**
	 * 是否导航页
	 */
	private Integer guideTabBar;
	/**
	 * 引导文字
	 */
	private String guideText;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
