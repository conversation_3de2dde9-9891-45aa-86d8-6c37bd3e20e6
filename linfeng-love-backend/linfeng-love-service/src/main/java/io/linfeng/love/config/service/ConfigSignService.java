package io.linfeng.love.config.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.entity.ConfigSignEntity;

import java.util.List;
import java.util.Map;

/**
 * 签到配置表
 *
 * <AUTHOR>
 * @date 2023-11-23 15:52:16
 */
public interface ConfigSignService extends IService<ConfigSignEntity> {

    PageObject queryPage(Map<String, Object> params);

    List<ConfigSignEntity> getConfigSignList();
}

