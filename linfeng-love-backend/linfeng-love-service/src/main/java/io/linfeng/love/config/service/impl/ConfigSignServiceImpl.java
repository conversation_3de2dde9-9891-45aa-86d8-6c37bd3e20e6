package io.linfeng.love.config.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.common.utils.RedisUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.config.dao.ConfigSignDao;
import io.linfeng.love.config.entity.ConfigSignEntity;
import io.linfeng.love.config.service.ConfigSignService;


@Service("configSignService")
public class ConfigSignServiceImpl extends ServiceImpl<ConfigSignDao, ConfigSignEntity> implements ConfigSignService {

    private static final String CACHE_SIGN_PREFIX = "config:sign:";

    private final RedisUtil redisUtils;

    public ConfigSignServiceImpl(RedisUtil redisUtils) {
        this.redisUtils = redisUtils;
    }

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<ConfigSignEntity> page = this.page(
                new Page<>(pageNum, pageSize)
        );

        return new PageObject(page);
    }

    @Override
    public List<ConfigSignEntity> getConfigSignList() {
        List<ConfigSignEntity> configSignList = redisUtils.getList(CACHE_SIGN_PREFIX, ConfigSignEntity.class);
        if (configSignList != null) {
            return configSignList;
        }
        LambdaQueryWrapper<ConfigSignEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(ConfigSignEntity::getDay);
        configSignList = this.list(wrapper);
        redisUtils.set(CACHE_SIGN_PREFIX, configSignList, 7200);
        return configSignList;
    }

}