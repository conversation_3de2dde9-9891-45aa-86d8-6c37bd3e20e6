package io.linfeng.love.pay.service.impl;


import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;
import io.linfeng.common.utils.StringUtil;
import io.linfeng.love.pay.dao.PayOrderDao;
import io.linfeng.love.pay.entity.PayOrderEntity;
import io.linfeng.love.pay.service.PayOrderService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service("payOrderService")
public class PayOrderServiceImpl extends ServiceImpl<PayOrderDao, PayOrderEntity> implements PayOrderService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        QueryWrapper<PayOrderEntity> queryWrapper = new QueryWrapper<>();

        String uid = (String) params.get("uid");
        String businessType = (String) params.get("businessType");
        String status = (String) params.get("status");

        if (!StringUtil.isEmpty(uid)) {
            if (NumberUtil.isInteger(uid)) {
                queryWrapper.lambda().eq(PayOrderEntity::getUid, uid);
            }
        }

        if (!StringUtil.isEmpty(businessType)) {
            queryWrapper.lambda().eq(PayOrderEntity::getBusinessType, Integer.parseInt(businessType));
        }
        if (!StringUtil.isEmpty(status)) {
            queryWrapper.lambda().eq(PayOrderEntity::getStatus, Integer.parseInt(status));
        }
        queryWrapper.lambda().orderByDesc(PayOrderEntity::getCreateTime);
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));
        IPage<PayOrderEntity> page = this.page(new Page<>(pageNum, pageSize),
                queryWrapper
        );

        return new PageObject(page);
    }

    @Override
    public BigDecimal getPayAmount(Map<String, Object> param) {
        return this.baseMapper.getPayAmount(param);
    }

    @Override
    public List<Map<String, Integer>> statisticsPayOrderList() {
        return this.baseMapper.statisticsPayOrderList();
    }

}