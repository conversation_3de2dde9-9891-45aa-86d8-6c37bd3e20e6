package io.linfeng.love.offline.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.offline.entity.OfflineNoticeEntity;

import java.util.Map;

/**
 * 线下活动公告表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-15 17:18:50
 */
public interface OfflineNoticeService extends IService<OfflineNoticeEntity> {

    PageObject queryPage(Map<String, Object> params);
}

