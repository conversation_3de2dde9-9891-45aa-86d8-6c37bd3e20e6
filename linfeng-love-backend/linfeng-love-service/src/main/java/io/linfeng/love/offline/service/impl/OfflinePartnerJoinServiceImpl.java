package io.linfeng.love.offline.service.impl;

import io.linfeng.love.offline.dto.response.MemberResponseDTO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.offline.dao.OfflinePartnerJoinDao;
import io.linfeng.love.offline.entity.OfflinePartnerJoinEntity;
import io.linfeng.love.offline.service.OfflinePartnerJoinService;


@Service("offlinePartnerJoinService")
public class OfflinePartnerJoinServiceImpl extends ServiceImpl<OfflinePartnerJoinDao, OfflinePartnerJoinEntity> implements OfflinePartnerJoinService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));
        IPage<OfflinePartnerJoinEntity> page = this.page(
                new Page<>(pageNum, pageSize)
        );

        return new PageObject(page);
    }

    @Override
    public List<String> getJoinAvatarList(Integer partnerId) {
        return this.baseMapper.getJoinAvatarList(partnerId);
    }

    @Override
    public List<MemberResponseDTO> getMemberList(Integer uid, Integer partnerId) {
        return this.baseMapper.getMemberList(uid, partnerId);
    }

}