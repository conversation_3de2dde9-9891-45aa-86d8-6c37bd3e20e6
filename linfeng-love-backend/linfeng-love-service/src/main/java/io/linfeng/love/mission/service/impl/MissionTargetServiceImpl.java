package io.linfeng.love.mission.service.impl;

import org.springframework.stereotype.Service;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.mission.dao.MissionTargetDao;
import io.linfeng.love.mission.entity.MissionTargetEntity;
import io.linfeng.love.mission.service.MissionTargetService;


@Service("missionTargetService")
public class MissionTargetServiceImpl extends ServiceImpl<MissionTargetDao, MissionTargetEntity> implements MissionTargetService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<MissionTargetEntity> page = this.page(
                new Page<>(pageNum, pageSize)
        );

        return new PageObject(page);
    }

}