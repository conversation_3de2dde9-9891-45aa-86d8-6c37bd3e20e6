package io.linfeng.love.moment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.love.moment.dto.response.MomentLikeResponseDTO;
import io.linfeng.love.moment.entity.MomentLikeEntity;

import java.util.List;

/**
 * 帖子收藏表(废弃)
 *
 * <AUTHOR>
 * @date 2023-10-11 08:39:17
 */
public interface MomentLikeService extends IService<MomentLikeEntity> {

    List<MomentLikeResponseDTO> getMomentLikeList(Integer momentId);

}

