package io.linfeng.love.moment.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.love.moment.dao.MomentMediaDao;
import io.linfeng.love.moment.dto.response.MomentMediaResponseDTO;
import io.linfeng.love.moment.entity.MomentMediaEntity;
import io.linfeng.love.moment.service.MomentMediaService;
import io.linfeng.love.user.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("momentMediaService")
public class MomentMediaServiceImpl extends ServiceImpl<MomentMediaDao, MomentMediaEntity> implements MomentMediaService {

    @Autowired
    UserService userService;

    @Override
    public List<MomentMediaResponseDTO> getMomentImageList(Integer uid) {
        return baseMapper.selectMomentImageList(uid);
    }

}