package io.linfeng.love.petal.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 花瓣推荐嘉宾表
 * 
 * <AUTHOR>
 * @date 2023-10-31 13:49:22
 */
@Data
@TableName("lf_petal_recommend")
public class PetalRecommendEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 用户id
	 */
	private Integer uid;
	/**
	 * 嘉宾用户id
	 */
	private Integer guestUid;
	/**
	 * 花瓣数量
	 */
	private Integer petalAmount;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
