package io.linfeng.love.bottle.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户漂流瓶详情表
 * 
 * <AUTHOR>
 * @date 2023-12-12 16:35:49
 */
@Data
@TableName("lf_user_bottle_detail")
public class UserBottleDetailEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 用户id
	 */
	private Integer uid;
	/**
	 * 内容
	 */
	private String content;
	/**
	 * 回应者id
	 */
	private Integer replyUid;
	/**
	 * 回应时间
	 */
	private Date replyTime;
	/**
	 * 回应内容
	 */
	private String replyContent;
	/**
	 * 捞起次数
	 */
	private Integer pickNum;
	/**
	 * 抛出时间
	 */
	private Date throwTime;
	/**
	 * 最后一次打捞时间
	 */
	private Date lastPickTime;
	/**
	 * 状态
	 */
	private Integer status;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
