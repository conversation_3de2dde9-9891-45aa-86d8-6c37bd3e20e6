package io.linfeng.love.bottle.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户漂流瓶表
 * 
 * <AUTHOR>
 * @date 2023-12-12 16:35:49
 */
@Data
@TableName("lf_user_bottle")
public class UserBottleEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 用户id
	 */
	private Integer uid;
	/**
	 * 剩余次数
	 */
	private Integer residuePickTimes;
	/**
	 * 扔次数
	 */
	private Integer throwTimes;
	/**
	 * 刷新时间
	 */
	private Date refreshTime;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
