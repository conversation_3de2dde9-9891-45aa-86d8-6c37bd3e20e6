package io.linfeng.love.config.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.entity.ConfigExchangePetalEntity;

import java.util.Map;

/**
 * 余额兑换花瓣配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-12 09:11:46
 */
public interface ConfigExchangePetalService extends IService<ConfigExchangePetalEntity> {

    PageObject queryPage(Map<String, Object> params);
}

