package io.linfeng.love.message.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.message.entity.SubscribeMessageEntity;

import java.util.Date;
import java.util.Map;

/**
 * 订阅消息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-02 08:36:49
 */
public interface SubscribeMessageService extends IService<SubscribeMessageEntity> {

    PageObject queryPage(Map<String, Object> params);

    void cancelSubscribe(String linkId, Integer uid, String messageType);

    void updateSendTime(String linkId, Integer uid, String messageType, Date sendTime);

}

