package io.linfeng.love.mission.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.mission.entity.MissionTargetEntity;

import java.util.Map;

/**
 * 任务指标表
 *
 * <AUTHOR>
 * @date 2023-11-30 16:27:37
 */
public interface MissionTargetService extends IService<MissionTargetEntity> {

    PageObject queryPage(Map<String, Object> params);
}

