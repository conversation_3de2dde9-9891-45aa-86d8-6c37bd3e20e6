package io.linfeng.love.config.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.entity.ConfigUserBackEntity;

import java.util.Map;

/**
 * 用户背景配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-02-08 15:40:14
 */
public interface ConfigUserBackService extends IService<ConfigUserBackEntity> {

    PageObject queryPage(Map<String, Object> params);
}

