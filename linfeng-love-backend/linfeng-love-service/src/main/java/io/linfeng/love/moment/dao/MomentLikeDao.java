package io.linfeng.love.moment.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.linfeng.love.moment.dto.response.MomentLikeResponseDTO;
import io.linfeng.love.moment.entity.MomentLikeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 帖子收藏表(废弃)
 * 
 * <AUTHOR>
 * @date 2023-10-11 08:39:17
 */
@Mapper
public interface MomentLikeDao extends BaseMapper<MomentLikeEntity> {

    List<MomentLikeResponseDTO> selectMomentLikeList(@Param("momentId")Integer momentId);

}
