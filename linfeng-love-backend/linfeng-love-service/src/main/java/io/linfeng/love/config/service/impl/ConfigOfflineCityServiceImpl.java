package io.linfeng.love.config.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.common.utils.StringUtil;
import io.linfeng.love.moment.entity.MomentEntity;
import io.linfeng.love.offline.entity.OfflineActivityEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.config.dao.ConfigOfflineCityDao;
import io.linfeng.love.config.entity.ConfigOfflineCityEntity;
import io.linfeng.love.config.service.ConfigOfflineCityService;


@Service("configOfflineCityService")
public class ConfigOfflineCityServiceImpl extends ServiceImpl<ConfigOfflineCityDao, ConfigOfflineCityEntity> implements ConfigOfflineCityService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        LambdaQueryWrapper<ConfigOfflineCityEntity> wrapper = new LambdaQueryWrapper<>();
        if(!StringUtil.isEmpty(params.get("city"))){
            wrapper.like(ConfigOfflineCityEntity::getCity, params.get("city"));
        }

        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<ConfigOfflineCityEntity> page = this.page(
                new Page<>(pageNum, pageSize),
                wrapper
        );

        return new PageObject(page);
    }

}