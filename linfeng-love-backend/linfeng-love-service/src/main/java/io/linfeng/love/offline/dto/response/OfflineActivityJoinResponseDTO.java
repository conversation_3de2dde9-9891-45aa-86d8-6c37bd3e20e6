package io.linfeng.love.offline.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class OfflineActivityJoinResponseDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 真实姓名
	 */
	private String realName;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 性别
	 */
	private Integer gender;
	/**
	 * 加入状态
	 */
	private Integer status;
	/**
	 * 加入时间
	 */
	private Date joinTime;
	/**
	 * 退出时间
	 */
	private Date exitTime;
}
