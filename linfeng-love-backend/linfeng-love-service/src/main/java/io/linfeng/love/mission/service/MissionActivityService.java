package io.linfeng.love.mission.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.mission.entity.MissionActivityEntity;

import java.util.Map;

/**
 * 活动表
 *
 * <AUTHOR>
 * @date 2023-11-27 17:20:04
 */
public interface MissionActivityService extends IService<MissionActivityEntity> {

    PageObject queryPage(Map<String, Object> params);
}

