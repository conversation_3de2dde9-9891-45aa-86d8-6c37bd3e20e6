package io.linfeng.love.mission.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.love.mission.dto.response.MissionResponseDTO;
import io.linfeng.love.mission.entity.UserMissionEntity;

import java.util.List;

/**
 * 用户任务表
 *
 * <AUTHOR>
 * @date 2023-11-27 14:05:19
 */
public interface UserMissionService extends IService<UserMissionEntity> {

    List<MissionResponseDTO> getMissionList(Integer uid, String activityCode);

    void receiveActivityMissionList(Integer uid, String activityCode);

    void updateAndRefreshCache(UserMissionEntity userMission, String activityCode);
}

