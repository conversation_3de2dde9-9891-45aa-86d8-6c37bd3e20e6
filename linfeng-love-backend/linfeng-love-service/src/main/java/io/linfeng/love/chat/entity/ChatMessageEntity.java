package io.linfeng.love.chat.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 私聊表
 * 
 * <AUTHOR>
 * @date 2023-09-19 09:57:37
 */
@Data
@TableName("lf_chat_message")
public class ChatMessageEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId
	private Integer id;
	/**
	 * 消息id
	 */
	private String messageId;
	/**
	 * 会话id
	 */
	private String sessionId;
	/**
	 * 发送者id
	 */
	private Integer senderUid;
	/**
	 * 接收者id
	 */
	private Integer receiverUid;
	/**
	 * 类型
	 */
	private Integer messageType;
	/**
	 * 内容
	 */
	private String content;
	/**
	 * 持续时间
	 */
	private Integer duration;
	/**
	 * 发送时间
	 */
	private Date sendTime;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
