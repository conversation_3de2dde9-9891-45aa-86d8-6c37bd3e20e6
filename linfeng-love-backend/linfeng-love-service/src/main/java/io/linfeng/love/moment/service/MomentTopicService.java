package io.linfeng.love.moment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.love.config.dto.response.TopicResponseDTO;
import io.linfeng.love.moment.entity.MomentTopicEntity;

import java.util.List;

/**
 * 帖子收藏表(废弃)
 *
 * <AUTHOR>
 * @date 2023-10-09 16:48:23
 */
public interface MomentTopicService extends IService<MomentTopicEntity> {

    List<TopicResponseDTO> getTopicList();

    List<TopicResponseDTO> getHotTopicList();

}

