package io.linfeng.love.accusation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;
import io.linfeng.common.utils.StringUtil;
import io.linfeng.love.accusation.dao.AccusationDao;
import io.linfeng.love.accusation.dto.response.AccusationResponseDTO;
import io.linfeng.love.accusation.entity.AccusationEntity;
import io.linfeng.love.accusation.service.AccusationService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service("accusationService")
public class AccusationServiceImpl extends ServiceImpl<AccusationDao, AccusationEntity> implements AccusationService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {



        LambdaQueryWrapper<AccusationEntity> queryWrapper = new LambdaQueryWrapper<>();
        if(!StringUtil.isEmpty(params.get("uid"))){
            queryWrapper.eq(AccusationEntity::getUid, params.get("uid"));
        }
        if(!StringUtil.isEmpty(params.get("content"))){
            queryWrapper.like(AccusationEntity::getContent, params.get("content"));
        }
        if(!StringUtil.isEmpty(params.get("type"))){
            queryWrapper.eq(AccusationEntity::getType, params.get("type"));
        }
        if(!StringUtil.isEmpty(params.get("status"))){
            queryWrapper.eq(AccusationEntity::getStatus, params.get("status"));
        }
        queryWrapper.orderByAsc(AccusationEntity::getUpdateTime);
        queryWrapper.orderByDesc(AccusationEntity::getId);
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<AccusationEntity> page = this.page(
                new Page<>(pageNum, pageSize),
                queryWrapper
        );
        return new PageObject(page);
    }

    @Override
    public List<AccusationResponseDTO> getAccusationList(Integer uid, Integer status) {
        return this.baseMapper.getAccusationList(uid, status);
    }

}