package io.linfeng.love.bottle.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 漂流瓶会话表
 * 
 * <AUTHOR>
 * @date 2023-12-12 16:35:50
 */
@Data
@TableName("lf_bottle_session")
public class BottleSessionEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId
	private Integer id;
	/**
	 * 会话id
	 */
	private Integer bottleId;
	/**
	 * 本人uid
	 */
	private Integer uid;
	/**
	 * 好友uid
	 */
	private Integer friendUid;
	/**
	 * 发最后一条消息的uid
	 */
	private Integer lastMessageUid;
	/**
	 * 最后一条消息类型
	 */
	private Integer lastMessageType;
	/**
	 * 最后一条消息内容
	 */
	private String lastMessageContent;
	/**
	 * 最后一条消息时间
	 */
	private Date lastMessageTime;
	/**
	 * 未读消息数量
	 */
	private Integer unRead;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
