package io.linfeng.love.bottle.service.impl;

import io.linfeng.love.bottle.dto.response.BottleSessionResponseDTO;
import org.springframework.stereotype.Service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import io.linfeng.love.bottle.dao.BottleSessionDao;
import io.linfeng.love.bottle.entity.BottleSessionEntity;
import io.linfeng.love.bottle.service.BottleSessionService;


@Service("bottleSessionService")
public class BottleSessionServiceImpl extends ServiceImpl<BottleSessionDao, BottleSessionEntity> implements BottleSessionService {

    @Override
    public List<BottleSessionResponseDTO> getAllSessionList(Integer uid) {
        return this.baseMapper.getAllSessionList(uid);
    }

    @Override
    public BottleSessionResponseDTO getBottleSession(Integer uid, Integer bottleId) {
        return this.baseMapper.getBottleSession(uid, bottleId);
    }
}