package io.linfeng.love.file.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.FileUtil;
import io.linfeng.love.config.service.ConfigSystemService;
import io.linfeng.love.file.dao.FileDao;
import io.linfeng.love.file.entity.FileEntity;
import io.linfeng.love.file.enums.OssSupplier;
import io.linfeng.love.file.service.FileService;
import io.linfeng.love.file.service.MinioService;
import io.linfeng.love.file.service.QiNiuOssService;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;


@Service("fileService")
public class FileServiceImpl extends ServiceImpl<FileDao, FileEntity> implements FileService {

    @Value("${qiniu.max-size}")
    private Long maxSize;

    private final QiNiuOssService qiNiuOssService;

    private final MinioService minioService;

    private final ConfigSystemService configSystemService;

    public FileServiceImpl(QiNiuOssService qiNiuOssService, MinioService minioService, ConfigSystemService configSystemService) {
        this.qiNiuOssService = qiNiuOssService;
        this.minioService = minioService;
        this.configSystemService = configSystemService;
    }

    @Override
    public String upload(MultipartFile file, boolean compressed) {
        if (file.isEmpty()) {
            throw new LinfengException("上传文件不能为空");
        }

        if(!FileUtil.checkSize(maxSize, file.getSize())){
            throw new LinfengException("上传文件超出规定大小");
        }
        String url = null;
        try{

            //h5的录音文件是blob格式上传，没有文件后缀，这边做下特殊处理，没有文件后缀的就是.mp3格式
            String suffix = null;
            if(file.getOriginalFilename().lastIndexOf(".") > 0){
                suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
            }else{
                if(file.getContentType().contains("image")){
                    suffix = ".png";
                }else{
                    suffix = ".mp3";
                }
            }
            byte[] uploadByte = file.getBytes();
            //图片文件先压缩后上传
            if(FileUtil.isImageSuffix(suffix) && compressed){
                uploadByte = FileUtil.compressedImage(file);
            }

            int ossSupplier = Integer.parseInt(configSystemService.getValue(Constant.OSS_SUPPLIER));
            if(ossSupplier == OssSupplier.MINIO.getValue()){
                url = minioService.upload(file, uploadByte, suffix);
            }
            if(ossSupplier == OssSupplier.QINIU.getValue()){
                url = qiNiuOssService.upload(uploadByte, suffix);
            }
            FileEntity fileEntity = new FileEntity();
            fileEntity.setUrl(url);
            fileEntity.setCreateTime(DateUtil.nowDateTime());
            this.save(fileEntity);
        }catch (Exception e){
            log.error("文件上传失败", e);
            throw new LinfengException("文件上传失败", e);
        }
        return url;
    }

    @Override
    public String upload(String url){
        String suffix = url.substring(url.lastIndexOf("."));
        return upload(urlToMultipartFile(url, IdUtil.fastSimpleUUID()+suffix), true);
    }

    private MultipartFile urlToMultipartFile(String url, String fileName) {
        File file = null;
        MultipartFile multipartFile = null;
        try {
            HttpURLConnection httpUrl = (HttpURLConnection) new URL(url).openConnection();
            httpUrl.connect();
            file = inputStreamToFile(httpUrl.getInputStream(),fileName);
            multipartFile = fileToMultipartFile(file);
            httpUrl.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return multipartFile;
    }

    private File inputStreamToFile(InputStream ins, String fileName) throws Exception{
        File file = new File(System.getProperty("java.io.tmpdir") + File.separator + fileName);
        OutputStream os = new FileOutputStream(file);
        int bytesRead;
        int len = 8192;
        byte[] buffer = new byte[len];
        while ((bytesRead = ins.read(buffer, 0, len)) != -1) {
            os.write(buffer, 0, bytesRead);
        }
        os.close();
        ins.close();
        return file;
    }

    private CommonsMultipartFile fileToMultipartFile(File file) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        FileItem item=factory.createItem(file.getName(),"text/plain",true,file.getName());
        int bytesRead = 0;
        byte[] buffer = new byte[8192];
        try {
            FileInputStream fis = new FileInputStream(file);
            OutputStream os = item.getOutputStream();
            while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            fis.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new CommonsMultipartFile(item);
    }


}