package io.linfeng.love.chat.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.love.chat.dao.ChatHelloDao;
import io.linfeng.love.chat.dto.response.ChatHelloResponseDTO;
import io.linfeng.love.guest.entity.ChatHelloEntity;
import io.linfeng.love.chat.service.ChatHelloService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("guestHelloService")
public class ChatHelloServiceImpl extends ServiceImpl<ChatHelloDao, ChatHelloEntity> implements ChatHelloService {

    @Override
    public List<ChatHelloResponseDTO> getChatHelloList(Integer uid) {
        return this.baseMapper.getChatHelloList(uid);
    }
}