package io.linfeng.love.identity.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.love.identity.dao.IdentityJobDao;
import io.linfeng.love.identity.entity.IdentityJobEntity;
import io.linfeng.love.identity.service.IdentityJobService;
import org.springframework.stereotype.Service;

@Service("identityJobService")
public class IdentityJobServiceImpl extends ServiceImpl<IdentityJobDao, IdentityJobEntity> implements IdentityJobService {


}