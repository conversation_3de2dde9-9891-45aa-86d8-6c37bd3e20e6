package io.linfeng.love.moment.dto.response;

import lombok.Data;

import java.util.List;


@Data
public class MomentResponseDTO {

	/**
	 * momentId
	 */
	private Integer momentId;

	/**
	 * oid
	 */
	private String oid;
	/**
	 * 用户名
	 */
	private String userName;
	/**
	 * 性别
	 */
	private Integer gender;
	/**
	 * 头像
	 */
	private String avatar;
	/**
	 * 生日
	 */
	private String birthday;
	/**
	 * 生日年份
	 */
	private String birthdayYear;
	/**
	 * 居住城市
	 */
	private String livingCity;
	/**
	 * 工作
	 */
	private Integer job;
	/**
	 * 账号类型 1普通用户 2官方账号
	 */
	private Integer type;
	/**
	 * 动态内容
	 */
	private String content;
	/**
	 * 文件类型
	 */
	private Integer mediaType;
	/**
	 * 文件列表
	 */
	private List<MomentMediaResponseDTO> mediaList;
	/**
	 * 点赞数量
	 */
	private Integer lv;
	/**
	 * 评论数量
	 */
	private Integer cv;
	/**
	 * 是否匿名
	 */
	private Boolean anonymous;
	/**
	 * 是否私密
	 */
	private Boolean privacy;
	/**
	 * 是否点赞
	 */
	private Boolean likeFlag;

	/**
	 * 所在城市
	 */
	private String city;

	/**
	 * 所在地址
	 */
	private String address;

	/**
	 * 纬度
	 */
	private String latitude;

	/**
	 * 经度
	 */
	private String longitude;
	/**
	 * 创建时间
	 */
	private String createTime;
	/**
	 * 话题列表
	 */
	private List<MomentTopicResponseDTO> topicList;

}
