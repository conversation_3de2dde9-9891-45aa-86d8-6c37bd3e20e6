package io.linfeng.love.accusation.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.love.accusation.dao.AccusationMediaDao;
import io.linfeng.love.accusation.entity.AccusationMediaEntity;
import io.linfeng.love.accusation.service.AccusationMediaService;
import org.springframework.stereotype.Service;


@Service("accusationMediaService")
public class AccusationMediaServiceImpl extends ServiceImpl<AccusationMediaDao, AccusationMediaEntity> implements AccusationMediaService {


}