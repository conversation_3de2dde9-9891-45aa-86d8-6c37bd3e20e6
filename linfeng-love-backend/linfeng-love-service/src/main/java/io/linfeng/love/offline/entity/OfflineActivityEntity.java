package io.linfeng.love.offline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 线下活动表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 09:00:53
 */
@Data
@TableName("lf_offline_activity")
public class OfflineActivityEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 活动名称
	 */
	private String activityName;
	/**
	 * 开始时间
	 */
	private Date startTime;
	/**
	 * 结束时间
	 */
	private Date endTime;
	/**
	 * 报名开始时间
	 */
	private Date joinStartTime;
	/**
	 * 报名结束时间
	 */
	private Date joinEndTime;
	/**
	 * 早鸟结束时间
	 */
	private Date earlyEndTime;
	/**
	 * 纬度
	 */
	private String latitude;
	/**
	 * 精度
	 */
	private String longitude;
	/**
	 * 活动城市
	 */
	private String city;
	/**
	 * 活动地点
	 */
	private String addressTitle;
	/**
	 * 详细地址
	 */
	private String addressDetail;
	/**
	 * 男生参加人数
	 */
	private Integer manJoinNumber;
	/**
	 * 女生参加人数
	 */
	private Integer womanJoinNumber;
	/**
	 * 男生人数上限
	 */
	private Integer manNumber;
	/**
	 * 女生人数上限
	 */
	private Integer womanNumber;
	/**
	 * 总人数
	 */
	private Integer totalNumber;
	/**
	 * 男生费用
	 */
	private BigDecimal manAmount;
	/**
	 * 女生费用
	 */
	private BigDecimal womanAmount;
	/**
	 * 男生早鸟费用
	 */
	private BigDecimal manEarlyAmount;
	/**
	 * 女生早鸟费用
	 */
	private BigDecimal womanEarlyAmount;
	/**
	 * 官方用户uid
	 */
	private Integer officialUid;
	/**
	 * 官方照片（联系方式）
	 */
	private String officialImage;
	/**
	 * 活动主照片
	 */
	private String urlMain;
	/**
	 * 活动封面照片
	 */
	private String urlCover;
	/**
	 * 活动照片列表
	 */
	private String urlList;
	/**
	 * 活动状态
	 */
	private Integer status;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
