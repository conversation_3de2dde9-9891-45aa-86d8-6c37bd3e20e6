package io.linfeng.love.mission.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.mission.entity.UserMissionReceiveEntity;

import java.util.Map;

/**
 * 用户任务领取记录表
 *
 * <AUTHOR>
 * @date 2023-11-27 15:20:14
 */
public interface UserMissionReceiveService extends IService<UserMissionReceiveEntity> {

    PageObject queryPage(Map<String, Object> params);
}

