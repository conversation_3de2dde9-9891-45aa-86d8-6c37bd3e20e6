package io.linfeng.love.message.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.message.entity.MessageSystemEntity;

import java.util.Map;

/**
 * 系统消息表
 *
 * <AUTHOR>
 * @date 2023-12-08 10:44:56
 */
public interface MessageSystemService extends IService<MessageSystemEntity> {

    PageObject queryPage(Map<String, Object> params);

    MessageSystemEntity getLastMessage();

    void saveAndRefreshCache(MessageSystemEntity messageSystem);

    void updateAndRefreshCache(MessageSystemEntity messageSystem);
}

