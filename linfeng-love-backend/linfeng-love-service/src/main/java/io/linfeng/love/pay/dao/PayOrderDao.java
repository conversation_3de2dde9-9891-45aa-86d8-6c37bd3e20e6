package io.linfeng.love.pay.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.linfeng.love.pay.entity.PayOrderEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 支付订单主表
 * 
 * <AUTHOR>
 * @date 2023-09-06 16:22:14
 */
@Mapper
public interface PayOrderDao extends BaseMapper<PayOrderEntity> {

    BigDecimal getPayAmount(@Param("param") Map<String, Object> param);

    List<Map<String, Integer>> statisticsPayOrderList();

}
