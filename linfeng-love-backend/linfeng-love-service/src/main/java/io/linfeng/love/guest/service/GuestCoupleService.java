package io.linfeng.love.guest.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.love.guest.dto.response.GuestSimpleResponseDTO;
import io.linfeng.love.guest.entity.GuestCoupleEntity;

import java.util.List;
import java.util.Map;

/**
 * 嘉宾配对信息表
 *
 * <AUTHOR>
 * @date 2023-10-17 09:02:11
 */
public interface GuestCoupleService extends IService<GuestCoupleEntity> {

    List<GuestSimpleResponseDTO> getLikeList(Map<String, Object> params);

    List<GuestSimpleResponseDTO> getLikeMeList(Map<String, Object> params);

    List<Integer> getExistGuestUidList(Integer uid);

}

