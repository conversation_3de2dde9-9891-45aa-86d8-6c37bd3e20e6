package io.linfeng.love.config.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 余额兑换现金表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-12 09:11:46
 */
@Data
@TableName("lf_config_exchange_cash")
public class ConfigExchangeCashEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId
	private Integer id;
	/**
	 * 金额
	 */
	private BigDecimal amount;
	/**
	 * 排序号
	 */
	private Integer sort;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
