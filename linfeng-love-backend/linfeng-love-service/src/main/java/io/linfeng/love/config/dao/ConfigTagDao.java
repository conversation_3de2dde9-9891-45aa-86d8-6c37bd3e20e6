package io.linfeng.love.config.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.linfeng.love.config.dto.response.ConfigTagGroupResponseDTO;
import io.linfeng.love.config.entity.ConfigTagEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 标签配置表
 * 
 * <AUTHOR>
 * @date 2023-10-18 14:54:01
 */
@Mapper
public interface ConfigTagDao extends BaseMapper<ConfigTagEntity> {

    List<ConfigTagGroupResponseDTO> getTagGroupList();
}
