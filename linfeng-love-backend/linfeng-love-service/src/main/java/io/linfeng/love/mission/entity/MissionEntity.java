package io.linfeng.love.mission.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 任务表
 * 
 * <AUTHOR>
 * @date 2023-11-27 11:05:38
 */
@Data
@TableName("lf_mission")
public class MissionEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 活动编码
	 */
	private String activityCode;
	/**
	 * 任务类型
	 */
	private Integer type;
	/**
	 * 任务周期（日、周、月、单次）
	 */
	private Integer period;
	/**
	 * 任务名称
	 */
	private String name;
	/**
	 * 任务描述
	 */
	private String description;
	/**
	 * 任务指标编码
	 */
	private String targetCode;
	/**
	 * 任务指标值
	 */
	private Integer targetValue;
	/**
	 * 奖励类型（字典）
	 */
	private Integer prizeType;
	/**
	 * 奖励值
	 */
	private Integer prizeValue;
	/**
	 * 自动领取
	 */
	private Integer autoReceive;
	/**
	 * 自动审核
	 */
	private Integer autoProcess;
	/**
	 * 自动发放
	 */
	private Integer autoPrize;
	/**
	 * 任务状态
	 */
	private Integer status;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
