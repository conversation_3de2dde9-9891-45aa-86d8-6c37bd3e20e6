package io.linfeng.love.pay.entity;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.love.pay.enums.PayStatus;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付订单主表
 * 
 * <AUTHOR>
 * @date 2023-09-06 16:22:14
 */
@Data
@TableName("lf_pay_order")
public class PayOrderEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 订单号
	 */
	private String orderNo;
	/**
	 * 客户id
	 */
	private Integer uid;
	/**
	 * openid
	 */
	private String openid;
	/**
	 * 支付金额
	 */
	private BigDecimal amount;
	/**
	 * 业务类型
	 */
	private Integer businessType;
	/**
	 * 终端类型
	 */
	private String terminalType;
	/**
	 * ip地址
	 */
	private String ip;
	/**
	 * 附加信息
	 */
	private String attach;
	/**
	 * 支付提交时间
	 */
	private Date submitTime;
	/**
	 * 支付结果时间
	 */
	private Date resultTime;
	/**
	 * 支付结果信息
	 */
	private String resultMessage;
	/**
	 * 外部订单号
	 */
	private String outOrderNo;
	/**
	 * 支付状态
	 */
	private Integer status;
	/**
	 * 退费状态
	 */
	private Integer refundStatus;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

	public PayOrderEntity(){
		setOrderNo(IdUtil.createSnowflake(0L, 0).nextIdStr());
		setCreateTime(DateUtil.nowDateTime());
		setStatus(PayStatus.PAYING.getValue());
		setSubmitTime(DateUtil.nowDateTime());
		setCreateTime(DateUtil.nowDateTime());
	}

}
