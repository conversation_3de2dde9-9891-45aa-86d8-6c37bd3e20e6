package io.linfeng.love.config.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 会员充值选项
 * 
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
@Data
@TableName("lf_vip_option")
public class VipOptionEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId
	private Integer id;
	/**
	 * 名称
	 */
	private String name;
	/**
	 * 有效天数
	 */
	private Integer validDays;
	/**
	 * 价格
	 */
	private BigDecimal price;
	/**
	 * 描述
	 */
	private String remark;
	/**
	 * 排序
	 */
	private Integer sort;

}
