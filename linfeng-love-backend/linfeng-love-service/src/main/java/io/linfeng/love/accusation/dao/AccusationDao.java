package io.linfeng.love.accusation.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.linfeng.love.accusation.dto.response.AccusationResponseDTO;
import io.linfeng.love.accusation.entity.AccusationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户举报表
 * 
 * <AUTHOR>
 * @date 2023-10-19 10:59:41
 */
@Mapper
public interface AccusationDao extends BaseMapper<AccusationEntity> {

    List<AccusationResponseDTO> getAccusationList(@Param("uid")Integer uid, @Param("status")Integer status);
	
}
