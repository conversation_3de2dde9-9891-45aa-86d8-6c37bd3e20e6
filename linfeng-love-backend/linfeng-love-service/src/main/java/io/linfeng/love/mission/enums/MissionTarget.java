package io.linfeng.love.mission.enums;

/**
 * 此类和字典表中DT_MISSION_TARGET类型下的code对应
 */
public enum MissionTarget {
    /**
     * 头像审核通过
     */
    AVATAR_CHECKED("AVATAR_CHECKED"),
    /**
     * 上传个人照片
     */
    UPLOAD_IMAGE("UPLOAD_IMAGE"),
    /**
     * 补充个人信息
     */
    FIRST_EDIT_INFO("FIRST_EDIT_INFO"),
    /**
     * 实名认证
     */
    IDENTITY_REAL_NAME("IDENTITY_REAL_NAME"),
    /**
     * 学历认证
     */
    IDENTITY_EDUCATION("IDENTITY_REAL_NAME"),
    /**
     * 工作认证
     */
    IDENTITY_JOB("IDENTITY_JOB");

    private String value;

    MissionTarget(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
