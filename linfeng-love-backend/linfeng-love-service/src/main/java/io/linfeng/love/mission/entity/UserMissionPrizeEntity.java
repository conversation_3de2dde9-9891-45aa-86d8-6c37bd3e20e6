package io.linfeng.love.mission.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户任务奖励表
 * 
 * <AUTHOR>
 * @date 2023-11-27 14:05:19
 */
@Data
@TableName("lf_user_mission_prize")
public class UserMissionPrizeEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 用户任务id
	 */
	private Integer userMissionId;
	/**
	 * 奖励类型
	 */
	private String prizeType;
	/**
	 * 奖励值
	 */
	private String prizeValue;
	/**
	 * 状态
	 */
	private String status;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
