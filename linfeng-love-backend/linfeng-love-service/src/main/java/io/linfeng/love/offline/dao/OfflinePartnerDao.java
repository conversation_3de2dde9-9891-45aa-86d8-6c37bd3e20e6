package io.linfeng.love.offline.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.love.offline.dto.response.PartnerDetailResponseDTO;
import io.linfeng.love.offline.dto.response.PartnerSimpleResponseDTO;
import io.linfeng.love.offline.entity.OfflineActivityEntity;
import io.linfeng.love.offline.entity.OfflinePartnerEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 线下找搭子表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-13 09:48:07
 */
@Mapper
public interface OfflinePartnerDao extends BaseMapper<OfflinePartnerEntity> {

    IPage<PartnerSimpleResponseDTO> getPartnerList(IPage<PartnerSimpleResponseDTO> dtoPage, @Param("typeId")Integer typeId, @Param("city")String city, @Param("queryType")Integer queryType);

    IPage<PartnerSimpleResponseDTO> getMyPartnerList(IPage<PartnerSimpleResponseDTO> dtoPage, @Param("uid")Integer uid, @Param("queryType")Integer queryType);

    PartnerDetailResponseDTO getPartnerDetail(@Param("id")Integer id, @Param("uid")Integer uid);

    IPage<PartnerDetailResponseDTO> getPartnerDetailList(IPage<PartnerDetailResponseDTO> dtoPage, @Param("status")Integer status);

}
