package io.linfeng.love.guest.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.love.guest.dao.GuestRecommendDao;
import io.linfeng.love.guest.dto.response.GuestSimpleResponseDTO;
import io.linfeng.love.guest.entity.GuestRecommendEntity;
import io.linfeng.love.guest.service.GuestRecommendService;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.entity.UserPreferencesEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("guestRecommendService")
public class GuestRecommendServiceImpl extends ServiceImpl<GuestRecommendDao, GuestRecommendEntity> implements GuestRecommendService {

    @Override
    public List<UserEntity> getRecommendGuestList(UserPreferencesEntity userPreferences, Map<String, Object> params) {
        return this.baseMapper.getRecommendGuestList(userPreferences, params);
    }

    @Override
    public List<GuestSimpleResponseDTO> getRecommendGuestList(List<Integer> guestUidList, Integer uid) {
        return this.baseMapper.getRecommendGuestListByUid(guestUidList, uid);
    }

    @Override
    public List<GuestSimpleResponseDTO> getHistoryRecommend(Map<String, Object> params) {
        return this.baseMapper.getHistoryRecommend(params);
    }

    @Override
    public List<Integer> getExistGuestUidList(Integer uid) {
        return this.baseMapper.getExistGuestUidList(uid);
    }

    @Override
    public GuestRecommendEntity getLatestRecommend(Integer uid) {
        return this.baseMapper.getLatestRecommend(uid);
    }

    @Override
    public void deleteUnOperatorRecommend(Integer uid) {
        this.baseMapper.deleteUnOperatorRecommend(uid);
    }

    @Override
    public IPage<GuestSimpleResponseDTO> selectVicinityGuestPage(IPage<GuestSimpleResponseDTO> dtoPage, Integer uid, String longitude,String latitude, Integer gender) {
        return this.baseMapper.selectVicinityGuestPage(dtoPage, uid, longitude, latitude, gender);
    }

}