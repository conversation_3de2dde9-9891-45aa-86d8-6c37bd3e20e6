package io.linfeng.love.chat.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.love.chat.dto.response.ChatMessageResponseDTO;
import io.linfeng.love.chat.entity.ChatMessageEntity;
import io.linfeng.love.chat.entity.ChatSessionEntity;

/**
 * 私聊表
 *
 * <AUTHOR>
 * @date 2023-09-19 09:57:37
 */
public interface ChatMessageService extends IService<ChatMessageEntity> {

    IPage<ChatMessageResponseDTO> selectMessagePage(Integer pageNum, Integer pageSize, ChatSessionEntity chatSession);

    ChatMessageResponseDTO saveAndRefreshCache(ChatMessageEntity chatMessage);

    void updateAndRefreshCache(ChatMessageEntity chatMessage);
}

