package io.linfeng.love.mission.service.impl;

import io.linfeng.love.mission.entity.MissionDetailEntity;
import org.springframework.stereotype.Service;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.mission.dao.MissionDetailDao;
import io.linfeng.love.mission.service.MissionDetailService;


@Service("missionDetailService")
public class MissionDetailServiceImpl extends ServiceImpl<MissionDetailDao, MissionDetailEntity> implements MissionDetailService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<MissionDetailEntity> page = this.page(
                new Page<>(pageNum, pageSize)
        );

        return new PageObject(page);
    }

}