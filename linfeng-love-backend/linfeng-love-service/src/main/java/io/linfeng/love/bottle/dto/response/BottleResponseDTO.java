package io.linfeng.love.bottle.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel(value="BottleResponse", description="漂流瓶")
public class BottleResponseDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 漂流瓶id
	 */
	private Integer bottleId;
	/**
	 * 漂流瓶用户uid
	 */
	private String uid;
	/**
	 * 漂流瓶用户oid
	 */
	private String oid;
	/**
	 * 内容
	 */
	private String content;
	/**
	 * 抛出时间
	 */
	private String throwTime;
	/**
	 * 用户名
	 */
	private String userName;
	/**
	 * 头像
	 */
	private String avatar;
	/**
	 * 性别(0未知，1男，2女)
	 */
	private Integer gender;

	/**
	 * 出生年份
	 */
	private String birthday;
	/**
	 * 行业/职业
	 */
	private Integer job;
	/**
	 * 居住城市
	 */
	private String livingCity;


}
