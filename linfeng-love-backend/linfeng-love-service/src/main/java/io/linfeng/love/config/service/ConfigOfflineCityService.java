package io.linfeng.love.config.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.entity.ConfigOfflineCityEntity;

import java.util.List;
import java.util.Map;

/**
 * 线下城市配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-26 10:57:34
 */
public interface ConfigOfflineCityService extends IService<ConfigOfflineCityEntity> {

    PageObject queryPage(Map<String, Object> params);

}

