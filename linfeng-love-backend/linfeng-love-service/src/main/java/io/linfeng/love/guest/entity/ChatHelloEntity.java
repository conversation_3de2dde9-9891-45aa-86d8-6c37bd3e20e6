package io.linfeng.love.guest.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 花瓣解锁嘉宾表
 * 
 * <AUTHOR>
 * @date 2023-10-19 14:13:12
 */
@Data
@TableName("lf_chat_hello")
public class ChatHelloEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 用户id
	 */
	private Integer uid;
	/**
	 * 嘉宾用户id
	 */
	private Integer guestUid;
	/**
	 * 发送者uid
	 */
	private Integer senderUid;
	/**
	 * 内容
	 */
	private String content;
	/**
	 * 类型
	 */
	private Integer type;
	/**
	 * 状态
	 */
	private Integer status;
	/**
	 * 打招呼时间
	 */
	private Date helloTime;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
