package io.linfeng.love.pay.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.pay.entity.PayOrderEntity;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 支付订单主表
 *
 * <AUTHOR>
 * @date 2023-09-06 16:22:14
 */
public interface PayOrderService extends IService<PayOrderEntity> {

    PageObject queryPage(Map<String, Object> params);

    BigDecimal getPayAmount(Map<String, Object> param);


    List<Map<String, Integer>> statisticsPayOrderList();
}

