package io.linfeng.love.offline.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.offline.dto.response.MemberResponseDTO;
import io.linfeng.love.offline.entity.OfflinePartnerJoinEntity;

import java.util.List;
import java.util.Map;

/**
 * 搭子活动参与表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-13 16:27:17
 */
public interface OfflinePartnerJoinService extends IService<OfflinePartnerJoinEntity> {

    PageObject queryPage(Map<String, Object> params);

    List<String> getJoinAvatarList(Integer partnerId);

    List<MemberResponseDTO> getMemberList(Integer uid, Integer partnerId);
}

