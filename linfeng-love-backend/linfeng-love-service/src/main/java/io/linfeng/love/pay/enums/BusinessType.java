package io.linfeng.love.pay.enums;

public enum BusinessType {
    /**
     * 会员充值
     */
    VIP(1, "会员充值"),
    /**
     * 货币充值
     */
    CCY(2, "货币充值"),
    /**
     * 活动报名
     */
    ACTIVITY_JOIN(3, "活动报名"),
    /**
     * 解锁精选嘉宾
     */
    UNLOCK_QUALITY(4, "解锁精选嘉宾");

    private int value;

    private String text;

    BusinessType(int value, String text) {
        this.value = value;
        this.text = text;
    }

    public int getValue() {
        return value;
    }
    public String getText() {
        return text;
    }

}
