package io.linfeng.love.mission.service.impl;

import org.springframework.stereotype.Service;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.mission.dao.UserMissionPrizeDao;
import io.linfeng.love.mission.entity.UserMissionPrizeEntity;
import io.linfeng.love.mission.service.UserMissionPrizeService;


@Service("userMissionPrizeService")
public class UserMissionPrizeServiceImpl extends ServiceImpl<UserMissionPrizeDao, UserMissionPrizeEntity> implements UserMissionPrizeService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<UserMissionPrizeEntity> page = this.page(
                new Page<>(pageNum, pageSize)
        );

        return new PageObject(page);
    }

}