package io.linfeng.love.chat.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.linfeng.love.chat.dto.response.ChatHelloResponseDTO;
import io.linfeng.love.guest.entity.ChatHelloEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 打招呼申请表
 * 
 * <AUTHOR>
 * @date 2023-10-19 14:13:12
 */
@Mapper
public interface ChatHelloDao extends BaseMapper<ChatHelloEntity> {

    /**
     * 获取打招呼申请列表
     * @param uid 用户id
     * @return 打招呼申请列表
     */
    List<ChatHelloResponseDTO> getChatHelloList(@Param("uid")Integer uid);
	
}
