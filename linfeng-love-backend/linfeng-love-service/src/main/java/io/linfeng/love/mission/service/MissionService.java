package io.linfeng.love.mission.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.mission.entity.MissionEntity;

import java.util.Map;

/**
 * 任务表
 *
 * <AUTHOR>
 * @date 2023-11-27 11:05:38
 */
public interface MissionService extends IService<MissionEntity> {

    PageObject queryPage(Map<String, Object> params);

}

