package io.linfeng.love.chat.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class ChatSessionResponseDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 会话id
	 */
	private String sessionId;

	/**
	 * 朋友oid
	 */
	private String friendOid;

	/**
	 * 好友头像
	 */
	private String friendAvatar;

	/**
	 * 朋友用户名
	 */
	private String friendUserName;
	/**
	 * 最后一条消息Oid
	 */
	private String lastMessageOid;
	/**
	 * 最后一条消息类型
	 */
	private Integer lastMessageType;
	/**
	 * 最后一条消息内容
	 */
	private String lastMessageContent;
	/**
	 * 最后一条消息时间
	 */
	private String lastMessageTime;
	/**
	 * 未读消息数量
	 */
	private Integer unRead;
	/**
	 * 在线状态
	 */
	private Integer onlineStatus;
	/**
	 * 最近离线时间
	 */
	private Date lastOfflineTime;


}
