package io.linfeng.love.chat.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.linfeng.love.chat.dto.response.ChatSessionResponseDTO;
import io.linfeng.love.chat.entity.ChatSessionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 聊天会话表
 * 
 * <AUTHOR>
 * @date 2023-09-19 09:57:37
 */
@Mapper
public interface ChatSessionDao extends BaseMapper<ChatSessionEntity> {

    /**
     * 获取所有的会话列表
     * @param uid 用户id
     * @return 所有会话列表
     */
    List<ChatSessionResponseDTO> getAllSessionList(@Param("uid")Integer uid);

    /**
     * 获取用户会话
     * @param uid 用户id
     * @param friendUid 朋友uid
     * @return 会话
     */
    ChatSessionResponseDTO selectSession(@Param("uid")Integer uid, @Param("friendUid")Integer friendUid);
}
