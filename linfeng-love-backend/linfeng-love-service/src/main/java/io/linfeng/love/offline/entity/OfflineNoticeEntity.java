package io.linfeng.love.offline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 线下活动公告表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-15 17:18:50
 */
@Data
@TableName("lf_offline_notice")
public class OfflineNoticeEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId
	private Integer id;
	/**
	 * 标题
	 */
	private String title;
	/**
	 * 正文
	 */
	private String content;
	/**
	 * 封面图
	 */
	private String coverUrl;
	/**
	 * 状态
	 */
	private Integer status;
	/**
	 * 序号
	 */
	private Integer sort;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
