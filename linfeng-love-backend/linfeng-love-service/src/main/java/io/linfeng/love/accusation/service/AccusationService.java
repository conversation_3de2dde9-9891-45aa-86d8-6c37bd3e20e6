package io.linfeng.love.accusation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.accusation.dto.response.AccusationResponseDTO;
import io.linfeng.love.accusation.entity.AccusationEntity;

import java.util.List;
import java.util.Map;

/**
 * 用户举报表
 *
 * <AUTHOR>
 * @date 2023-10-19 10:59:41
 */
public interface AccusationService extends IService<AccusationEntity> {

    PageObject queryPage(Map<String, Object> params);

    List<AccusationResponseDTO> getAccusationList(Integer uid, Integer status);

}

