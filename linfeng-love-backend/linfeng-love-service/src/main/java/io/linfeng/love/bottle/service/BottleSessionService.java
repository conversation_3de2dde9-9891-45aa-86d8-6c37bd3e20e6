package io.linfeng.love.bottle.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.love.bottle.dto.response.BottleSessionResponseDTO;
import io.linfeng.love.bottle.entity.BottleSessionEntity;

import java.util.List;

/**
 * 漂流瓶会话表
 *
 * <AUTHOR>
 * @date 2023-12-12 16:35:50
 */
public interface BottleSessionService extends IService<BottleSessionEntity> {

    List<BottleSessionResponseDTO> getAllSessionList(Integer uid);

    BottleSessionResponseDTO getBottleSession(Integer uid, Integer bottleId);
}

