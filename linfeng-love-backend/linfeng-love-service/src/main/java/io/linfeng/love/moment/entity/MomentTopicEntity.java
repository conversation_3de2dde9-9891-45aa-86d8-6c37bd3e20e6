package io.linfeng.love.moment.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 帖子收藏表(废弃)
 * 
 * <AUTHOR>
 * @date 2023-10-09 16:48:23
 */
@Data
@TableName("lf_moment_topic")
public class MomentTopicEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 动态id
	 */
	private Integer momentId;
	/**
	 * 话题id
	 */
	private Integer topicId;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
