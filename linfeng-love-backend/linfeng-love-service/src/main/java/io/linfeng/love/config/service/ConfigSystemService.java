package io.linfeng.love.config.service;


import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.love.config.entity.ConfigSystemEntity;

import java.util.List;

/**
 * 系统配置信息
 *
 */
public interface ConfigSystemService extends IService<ConfigSystemEntity> {

	/**
	 * 根据key，获取配置的value值
	 */
	String getValue(String key);

	/**
	 * 批量更新配置文件并刷新缓存
	 * @param configSystemEntityList
	 */
	void updateAndRefreshCache(List<ConfigSystemEntity> configSystemEntityList);

}
