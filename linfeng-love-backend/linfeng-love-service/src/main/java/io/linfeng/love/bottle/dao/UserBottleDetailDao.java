package io.linfeng.love.bottle.dao;

import io.linfeng.love.bottle.dto.response.BottleResponseDTO;
import io.linfeng.love.bottle.entity.UserBottleDetailEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户漂流瓶详情表
 * 
 * <AUTHOR>
 * @date 2023-12-12 16:35:49
 */
@Mapper
public interface UserBottleDetailDao extends BaseMapper<UserBottleDetailEntity> {

    BottleResponseDTO pickBottle(@Param("uid")Integer uid);
	
}
