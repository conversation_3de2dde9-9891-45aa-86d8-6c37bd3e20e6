package io.linfeng.love.bottle.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.linfeng.love.bottle.dto.response.BottleMessageResponseDTO;
import io.linfeng.love.bottle.entity.BottleMessageEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.linfeng.love.chat.dto.response.ChatMessageResponseDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 漂流瓶消息表
 * 
 * <AUTHOR>
 * @date 2023-12-12 16:35:50
 */
@Mapper
public interface BottleMessageDao extends BaseMapper<BottleMessageEntity> {

    IPage<BottleMessageResponseDTO> selectMessagePage(Page<BottleMessageResponseDTO> page, @Param("bottleId")Integer bottleId);
	
}
