package io.linfeng.love.config.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.dto.response.ConfigTagGroupResponseDTO;
import io.linfeng.love.config.entity.ConfigTagEntity;

import java.util.List;
import java.util.Map;

/**
 * 标签配置表
 *
 * <AUTHOR>
 * @date 2023-10-18 14:54:01
 */
public interface ConfigTagService extends IService<ConfigTagEntity> {

    PageObject queryPage(Map<String, Object> params);

    List<ConfigTagGroupResponseDTO> getTagGroupList();

}

