package io.linfeng.love.chat.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class ChatHelloResponseDTO implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 嘉宾oid
	 */
	private String oid;
	/**
	 * 发送者oid
	 */
	private String senderOid;
	/**
	 * 用户名
	 */
	private String userName;
	/**
	 * 头像
	 */
	private String avatar;
	/**
	 * 状态
	 */
	private Integer status;
	/**
	 * 内容
	 */
	private String content;
	/**
	 * 打招呼时间
	 */
	private Date helloTime;

}
