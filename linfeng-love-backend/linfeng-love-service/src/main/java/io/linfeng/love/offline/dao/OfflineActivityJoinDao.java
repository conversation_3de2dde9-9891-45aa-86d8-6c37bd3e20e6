package io.linfeng.love.offline.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.linfeng.love.offline.dto.response.ActivitySimpleResponseDTO;
import io.linfeng.love.offline.dto.response.OfflineActivityJoinResponseDTO;
import io.linfeng.love.offline.entity.OfflineActivityJoinEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 线下活动参与表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 09:00:52
 */
@Mapper
public interface OfflineActivityJoinDao extends BaseMapper<OfflineActivityJoinEntity> {

    IPage<OfflineActivityJoinResponseDTO> getPage(IPage<OfflineActivityJoinResponseDTO> page, @Param("activityId") Integer activityId);

    List<String> getJoinAvatarList(@Param("activityId") Integer activityId);

}
