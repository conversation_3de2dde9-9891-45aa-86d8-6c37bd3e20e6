package io.linfeng.love.chat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.love.chat.dto.response.ChatSessionResponseDTO;
import io.linfeng.love.chat.entity.ChatSessionEntity;

import java.util.List;

/**
 * 聊天会话服务
 *
 * <AUTHOR>
 * @date 2023-09-19 09:57:37
 */
public interface ChatSessionService extends IService<ChatSessionEntity> {

    /**
     * 获取所有会话列表
     * @param uid 用户id
     * @return 会话列表
     */
    List<ChatSessionResponseDTO> getAllSessionList(Integer uid);

    /**
     * 获取会话详情
     * @param friendUid 朋友uid
     * @param uid 用户uid
     * @return
     */
    ChatSessionResponseDTO getSessionResponseDTO(Integer uid, Integer friendUid);

    void saveAndRefreshCache(ChatSessionEntity chatSession);

    void updateAndRefreshCache(ChatSessionEntity chatSession);

    void deleteAndRefreshCache(Integer uid, String sessionId);
}

