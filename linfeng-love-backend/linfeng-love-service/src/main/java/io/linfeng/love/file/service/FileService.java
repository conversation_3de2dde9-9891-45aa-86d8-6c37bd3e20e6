package io.linfeng.love.file.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.love.file.entity.FileEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传
 *
 * <AUTHOR>
 * @date 2023-10-25 13:35:13
 */
public interface FileService extends IService<FileEntity> {

    /**
     * 文件上传
     * @param file 文件
     * @return 图片地址
     */
    String upload(MultipartFile file, boolean compressed);

    /**
     * 文件上传
     * @param url 文件地址
     * @return 图片地址
     */
    String upload(String url);

}

