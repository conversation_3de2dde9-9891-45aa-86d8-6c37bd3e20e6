package io.linfeng.love.chat.dao;

import io.linfeng.love.chat.dto.response.ChatFriendResponseDTO;
import io.linfeng.love.chat.entity.ChatFriendEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 好友表
 * 
 * <AUTHOR>
 * @date 2023-11-03 09:34:37
 */
@Mapper
public interface ChatFriendDao extends BaseMapper<ChatFriendEntity> {

    /**
     * 获取好友列表
     * @param uid 用户id
     * @return 好友列表
     */
    List<ChatFriendResponseDTO> getFriendList(@Param("uid")Integer uid);

    /**
     * 获取好友id列表
     * @param uid 用户id
     * @return好友id列表
     */
    List<Integer> getExistFriendUidList(@Param("uid")Integer uid);
	
}
