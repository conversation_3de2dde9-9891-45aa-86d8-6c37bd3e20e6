package io.linfeng.love.mission.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 任务进度表
 * 
 * <AUTHOR>
 * @date 2023-11-27 11:05:38
 */
@Data
@TableName("lf_mission_detail")
public class MissionDetailEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 任务id
	 */
	private Integer missionId;
	/**
	 * 指标值
	 */
	private Integer targetValue;
	/**
	 * 奖励类型
	 */
	private Integer prizeType;
	/**
	 * 奖励值
	 */
	private Integer prizeValue;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
