package io.linfeng.love.guest.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.love.guest.dao.GuestQualityDao;
import io.linfeng.love.guest.dto.response.GuestSimpleResponseDTO;
import io.linfeng.love.guest.entity.GuestQualityEntity;
import io.linfeng.love.guest.service.GuestQualityService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("guestQualityService")
public class GuestQualityServiceImpl extends ServiceImpl<GuestQualityDao, GuestQualityEntity> implements GuestQualityService {

    @Override
    public List<GuestSimpleResponseDTO> selectQualityGuestList(Map<String, Object> params) {
        return this.baseMapper.selectQualityGuestList(params);
    }

}