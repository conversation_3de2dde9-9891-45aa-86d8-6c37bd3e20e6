/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.love.executor;


import io.linfeng.common.utils.DateUtil;
import io.linfeng.love.common.enums.PrizeOrigin;
import io.linfeng.love.common.enums.PrizeType;
import io.linfeng.love.petal.entity.PetalRecordEntity;
import io.linfeng.love.petal.enums.PetalRecordSubType;
import io.linfeng.love.petal.enums.PetalRecordType;
import io.linfeng.love.petal.service.PetalRecordService;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.service.UserService;
import org.springframework.stereotype.Service;

/**
 * 奖励事件执行器
 * 执行器模块是app和admin两个系统都能用到的模块，因为业务复杂度不是很高，没有考虑做拆分，暂时放在service模块下
 *
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
@Service("prizeEventExecutor")
public class PrizeEventExecutor {

    private final UserService userService;

    private final PetalRecordService petalRecordService;

    public PrizeEventExecutor(UserService userService, PetalRecordService petalRecordService) {
        this.userService = userService;
        this.petalRecordService = petalRecordService;
    }

    public void awardPrize(UserEntity user, Integer prizeType, Integer prizeValue, Integer origin){
        //系统目前至设计了花瓣奖励，后续可以横向扩展其他类型奖励
        if(prizeType == PrizeType.PETAL_LIMIT.getValue()){
            awardPetalLimit(user, prizeValue, origin);
        }

    }

    private void awardPetalLimit(UserEntity user, Integer petalLimit, Integer origin){
        user.setPetalLimit(user.getPetalLimit() + petalLimit);
        user.setUpdateTime(DateUtil.nowDateTime());
        user.setAvatarStatus(null);
        user.setRealNameStatus(null);
        userService.updateAndDeleteCache(user);

        //保存花瓣记录
        PetalRecordEntity petalRecordEntity = new PetalRecordEntity();
        petalRecordEntity.setUid(user.getUid());
        petalRecordEntity.setType(PetalRecordType.RECHARGE.getValue());
        if(origin == PrizeOrigin.MISSION.getValue()){
            petalRecordEntity.setSubType(PetalRecordSubType.MISSION.getValue());
        }
        if(origin == PrizeOrigin.SIGN.getValue()){
            petalRecordEntity.setSubType(PetalRecordSubType.SIGN.getValue());
        }
        petalRecordEntity.setAmountForever(0);
        petalRecordEntity.setAmountLimit(petalLimit);
        petalRecordEntity.setCreateTime(DateUtil.nowDateTime());
        petalRecordService.save(petalRecordEntity);
    }

}

