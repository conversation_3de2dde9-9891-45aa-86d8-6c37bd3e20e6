package io.linfeng.love.config.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.love.config.entity.ConfigTopicEntity;
import org.springframework.stereotype.Service;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.config.dao.ConfigUserBackDao;
import io.linfeng.love.config.entity.ConfigUserBackEntity;
import io.linfeng.love.config.service.ConfigUserBackService;


@Service("configUserBackService")
public class ConfigUserBackServiceImpl extends ServiceImpl<ConfigUserBackDao, ConfigUserBackEntity> implements ConfigUserBackService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        LambdaQueryWrapper<ConfigUserBackEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(ConfigUserBackEntity::getSort);
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<ConfigUserBackEntity> page = this.page(
                new Page<>(pageNum, pageSize),
                wrapper
        );

        return new PageObject(page);
    }

}