package io.linfeng.love.bottle.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.bottle.entity.UserBottleEntity;

import java.util.Map;

/**
 * 用户漂流瓶表
 *
 * <AUTHOR>
 * @date 2023-12-12 16:35:49
 */
public interface UserBottleService extends IService<UserBottleEntity> {

    PageObject queryPage(Map<String, Object> params);
}

