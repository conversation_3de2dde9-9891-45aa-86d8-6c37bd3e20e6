package io.linfeng.love.config.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 余额兑换花瓣配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-12 09:11:46
 */
@Data
@TableName("lf_config_exchange_petal")
public class ConfigExchangePetalEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId
	private Integer id;
	/**
	 * 金额
	 */
	private BigDecimal amount;
	/**
	 * 花瓣数量
	 */
	private Integer petal;
	/**
	 * 排序号
	 */
	private Integer sort;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
