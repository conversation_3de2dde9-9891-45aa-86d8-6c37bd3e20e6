package io.linfeng.love.offline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 线下找搭子表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-13 09:48:07
 */
@Data
@TableName("lf_offline_partner")
public class OfflinePartnerEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 发起则id
	 */
	private Integer uid;
	/**
	 * 类型ID
	 */
	private Integer typeId;
	/**
	 * 活动名称
	 */
	private String activityName;
	/**
	 * 开始时间
	 */
	private Date startTime;
	/**
	 * 报名结束时间
	 */
	private Date joinEndTime;
	/**
	 * 维度
	 */
	private String latitude;
	/**
	 * 精度
	 */
	private String longitude;
	/**
	 * 省份
	 */
	private String province;
	/**
	 * 城市
	 */
	private String city;
	/**
	 * 地址标题
	 */
	private String addressTitle;
	/**
	 * 地址详情
	 */
	private String addressDetail;
	/**
	 * 男生参加人数
	 */
	private Integer manJoinNumber;
	/**
	 * 女生参加人数
	 */
	private Integer womanJoinNumber;
	/**
	 * 总人数
	 */
	private Integer totalNumber;
	/**
	 * 男士人数
	 */
	private Integer manNumber;
	/**
	 * 女士人数
	 */
	private Integer womanNumber;
	/**
	 * 男士费用
	 */
	private BigDecimal manAmount;
	/**
	 * 女士费用
	 */
	private BigDecimal womanAmount;
	/**
	 * 活动照片列表
	 */
	private String mediaList;
	/**
	 * 活动状态
	 */
	private Integer status;
	/**
	 * 活动详情
	 */
	private String remark;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
