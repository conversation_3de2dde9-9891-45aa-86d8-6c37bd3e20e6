package io.linfeng.love.moment.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class MomentCommentResponseDTO {


	/**
	 * 评论id
	 */
	private Integer commentId;
	/**
	 * 评论pid
	 */
	private Integer pid;
	/**
	 * 评论内容
	 */
	private String content;
	/**
	 * 评论者oid
	 */
	private String replyOid;
	/**
	 * 评论者用户名
	 */
	private String replyUserName;
	/**
	 * 被评论者oid
	 */
	private String beReplyOid;
	/**
	 * 被评论者用户名
	 */
	private String beReplyUserName;
	/**
	 * 评论者头像
	 */
	private String avatar;
	/**
	 * 性别
	 */
	private Integer gender;
	/**
	 * 生日
	 */
	private String birthday;
	/**
	 * 居住城市
	 */
	private String livingCity;
	/**
	 * ip省份
	 */
	private String ipProvince;
	/**
	 * 工作
	 */
	private Integer job;
	/**
	 * 账号类型 1普通用户 2官方账号
	 */
	private Integer type;
	/**
	 * 创建时间
	 */
	private String createTime;

	/**
	 * 子评论列表
	 */
	private List<MomentCommentResponseDTO> childCommentList;

}
