package io.linfeng.love.mission.dao;

import io.linfeng.love.mission.dto.response.MissionResponseDTO;
import io.linfeng.love.mission.entity.UserMissionEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户任务表
 * 
 * <AUTHOR>
 * @date 2023-11-27 14:05:19
 */
@Mapper
public interface UserMissionDao extends BaseMapper<UserMissionEntity> {

    List<MissionResponseDTO> getMissionList(@Param("uid")Integer uid, @Param("activityCode")String activityCode);

    void receiveActivityMissionList(@Param("uid")Integer uid, @Param("activityCode")String activityCode);
	
}
