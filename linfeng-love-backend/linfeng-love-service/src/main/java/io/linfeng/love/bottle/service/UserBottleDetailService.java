package io.linfeng.love.bottle.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.love.bottle.dto.response.BottleResponseDTO;
import io.linfeng.love.bottle.entity.UserBottleDetailEntity;

/**
 * 用户漂流瓶详情表
 *
 * <AUTHOR>
 * @date 2023-12-12 16:35:49
 */
public interface UserBottleDetailService extends IService<UserBottleDetailEntity> {

    BottleResponseDTO pickBottle(Integer uid);
}

