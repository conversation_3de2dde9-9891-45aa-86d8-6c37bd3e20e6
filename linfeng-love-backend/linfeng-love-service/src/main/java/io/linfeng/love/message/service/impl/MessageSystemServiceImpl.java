package io.linfeng.love.message.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.RedisUtil;
import org.springframework.stereotype.Service;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.message.dao.MessageSystemDao;
import io.linfeng.love.message.entity.MessageSystemEntity;
import io.linfeng.love.message.service.MessageSystemService;


@Service("messageSystemService")
public class MessageSystemServiceImpl extends ServiceImpl<MessageSystemDao, MessageSystemEntity> implements MessageSystemService {

    private static final String CACHE_MESSAGE_SYSTEM_PREFIX = "system:message";

    private final RedisUtil redisUtil;

    public MessageSystemServiceImpl(RedisUtil redisUtil) {
        this.redisUtil = redisUtil;
    }

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        LambdaQueryWrapper<MessageSystemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(MessageSystemEntity::getId);
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<MessageSystemEntity> page = this.page(
                new Page<>(pageNum, pageSize),
                wrapper
        );

        return new PageObject(page);
    }

    @Override
    public MessageSystemEntity getLastMessage() {
        MessageSystemEntity messageSystemEntity = redisUtil.get(CACHE_MESSAGE_SYSTEM_PREFIX, MessageSystemEntity.class);
        if(messageSystemEntity != null){
            return  messageSystemEntity;
        }
        messageSystemEntity = this.baseMapper.getLastMessage();
        redisUtil.set(CACHE_MESSAGE_SYSTEM_PREFIX, messageSystemEntity, RedisUtil.NOT_EXPIRE);
        return messageSystemEntity;
    }

    @Override
    public void saveAndRefreshCache(MessageSystemEntity messageSystem) {
        //删除上一次通知的系统消息,redis里存的主要用于首页弹窗
        redisUtil.delete(CACHE_MESSAGE_SYSTEM_PREFIX);

        messageSystem.setCreateTime(DateUtil.nowDateTime());
        this.save(messageSystem);

        redisUtil.set(CACHE_MESSAGE_SYSTEM_PREFIX, messageSystem, RedisUtil.NOT_EXPIRE);
    }

    @Override
    public void updateAndRefreshCache(MessageSystemEntity messageSystem) {
        redisUtil.delete(CACHE_MESSAGE_SYSTEM_PREFIX);

        messageSystem.setUpdateTime(DateUtil.nowDateTime());
        this.updateById(messageSystem);

        redisUtil.set(CACHE_MESSAGE_SYSTEM_PREFIX, messageSystem, RedisUtil.NOT_EXPIRE);
    }

}