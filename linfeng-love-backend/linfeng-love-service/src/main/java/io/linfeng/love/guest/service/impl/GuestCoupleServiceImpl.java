package io.linfeng.love.guest.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.love.guest.dao.GuestCoupleDao;
import io.linfeng.love.guest.dto.response.GuestSimpleResponseDTO;
import io.linfeng.love.guest.entity.GuestCoupleEntity;
import io.linfeng.love.guest.service.GuestCoupleService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("guestCoupleService")
public class GuestCoupleServiceImpl extends ServiceImpl<GuestCoupleDao, GuestCoupleEntity> implements GuestCoupleService {

    @Override
    public List<GuestSimpleResponseDTO> getLikeList(Map<String, Object> params) {
        return this.baseMapper.getLikeList(params);
    }

    @Override
    public List<GuestSimpleResponseDTO> getLikeMeList(Map<String, Object> params) {
        return this.baseMapper.getLikeMeList(params);
    }

    @Override
    public List<Integer> getExistGuestUidList(Integer uid) {
        return this.baseMapper.getExistGuestUidList(uid);
    }

}