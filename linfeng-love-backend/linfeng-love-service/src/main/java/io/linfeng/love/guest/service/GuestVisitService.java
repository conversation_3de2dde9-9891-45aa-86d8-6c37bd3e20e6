package io.linfeng.love.guest.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.love.guest.dto.response.GuestSimpleResponseDTO;
import io.linfeng.love.guest.entity.GuestVisitEntity;

import java.util.List;
import java.util.Map;

/**
 * 嘉宾访问信息表
 *
 * <AUTHOR>
 * @date 2023-10-16 17:05:07
 */
public interface GuestVisitService extends IService<GuestVisitEntity> {

    Integer getLookMeUserCount(Integer uid);

    List<GuestSimpleResponseDTO> getLookMeList(Map<String, Object> params);
}

