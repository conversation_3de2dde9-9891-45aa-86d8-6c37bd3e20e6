package io.linfeng.love.guest.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.linfeng.love.guest.dto.response.GuestSimpleResponseDTO;
import io.linfeng.love.guest.entity.GuestVisitEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 嘉宾访问信息表
 * 
 * <AUTHOR>
 * @date 2023-10-16 17:05:07
 */
@Mapper
public interface GuestVisitDao extends BaseMapper<GuestVisitEntity> {

    Integer getLookMeUserCount(@Param("uid")Integer uid);

    List<GuestSimpleResponseDTO> getLookMeList(@Param("params")Map<String, Object> params);
	
}
