package io.linfeng.love.bottle.service.impl;

import org.springframework.stereotype.Service;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.bottle.dao.UserBottleDao;
import io.linfeng.love.bottle.entity.UserBottleEntity;
import io.linfeng.love.bottle.service.UserBottleService;


@Service("userBottleService")
public class UserBottleServiceImpl extends ServiceImpl<UserBottleDao, UserBottleEntity> implements UserBottleService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<UserBottleEntity> page = this.page(
                new Page<>(pageNum, pageSize)
        );

        return new PageObject(page);
    }

}