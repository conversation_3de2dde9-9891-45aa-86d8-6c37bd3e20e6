package io.linfeng.love.mission.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户任务表
 * 
 * <AUTHOR>
 * @date 2023-11-27 14:05:19
 */
@Data
@TableName("lf_user_mission")
public class UserMissionEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 用户id
	 */
	private Integer uid;
	/**
	 * 活动编码
	 */
	private String activityCode;
	/**
	 * 任务id
	 */
	private Integer missionId;
	/**
	 * 当前值
	 */
	private Integer currentValue;
	/**
	 * 状态
	 */
	private Integer status;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
