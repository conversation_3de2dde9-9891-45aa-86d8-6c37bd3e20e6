package io.linfeng.love.guest.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.love.guest.dto.response.GuestSimpleResponseDTO;
import io.linfeng.love.guest.entity.GuestRecommendEntity;
import io.linfeng.love.user.entity.UserEntity;
import io.linfeng.love.user.entity.UserPreferencesEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 推荐用户信息表
 *
 * <AUTHOR>
 * @date 2023-09-01 15:20:59
 */
public interface GuestRecommendService extends IService<GuestRecommendEntity> {

    /**
     * 获取推荐嘉宾列表
     * @param userPreferences 用户筛选条件
     * @param params 其他筛选条件
     * @return 荐嘉宾列表
     */
    List<UserEntity> getRecommendGuestList(UserPreferencesEntity userPreferences, Map<String, Object> params);
    /**
     * 获取嘉宾列表
     * @param guestUidList 嘉宾用户列表
     * @param uid 登录用户uid
     * @return 荐嘉宾列表
     */
    List<GuestSimpleResponseDTO> getRecommendGuestList(List<Integer> guestUidList, Integer uid);

    /**
     * 获取历史推荐记录
     * @param params 查询条件
     * @return 历史推荐记录
     */
    List<GuestSimpleResponseDTO> getHistoryRecommend(Map<String, Object> params);

    /**
     * 获取已经推荐过的嘉宾uid列表
     * @param uid 客户uid
     * @return 已经推荐过的嘉宾uid列表
     */
    List<Integer> getExistGuestUidList(Integer uid);

    /**
     * 获取最新的推荐嘉宾
     * @param uid 客户uid
     * @return 最新的推荐嘉宾
     */
    GuestRecommendEntity getLatestRecommend(Integer uid);

    /**
     * 删除未操作过的推荐记录
     * @param uid 用户id
     */
    void deleteUnOperatorRecommend(Integer uid);

    /**
     * 获取附近的嘉宾列表
     * @param dtoPage 分页条件
     * @param longitude 经度
     * @param latitude 维度
     * @param gender 性别
     * @return 附近的嘉宾列表
     */
    IPage<GuestSimpleResponseDTO> selectVicinityGuestPage(IPage<GuestSimpleResponseDTO> dtoPage, Integer uid, String longitude,String latitude, Integer gender);

}

