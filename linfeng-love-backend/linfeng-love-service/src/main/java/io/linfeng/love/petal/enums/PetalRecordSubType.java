package io.linfeng.love.petal.enums;

/**
 * 花瓣记录子类型
 */
public enum PetalRecordSubType {
    /** 充值的子类型 */
    /** 在线充值 */
    CASH(101),
    /** 签到奖励 */
    SIGN(102),
    /** 任务奖励 */
    MISSION(103),
    /** 打招呼未成功返还 */
    HELLO_RETURN(104),
    /** 余额兑换 */
    ACCOUNT_EXCHANGE(105),
    /** 消费的子类型 */
    /** 解锁嘉宾 */
    UNLOCK(201),
    /** 打招呼 */
    HELLO(202),
    /** 推荐嘉宾 */
    RECOMMEND(203),
    /** 礼物赠送 */
    GIFT_GIVE(204),
    /** 精选嘉宾 */
    QUALITY(205);

    private int value;

    PetalRecordSubType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

}
