package io.linfeng.love.offline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 搭子活动参与表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-13 16:27:17
 */
@Data
@TableName("lf_offline_partner_join")
public class OfflinePartnerJoinEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 客户id
	 */
	private Integer uid;
	/**
	 * 搭子id
	 */
	private Integer partnerId;
	/**
	 * 状态
	 */
	private Integer status;
	/**
	 * 加入时间
	 */
	private Date joinTime;
	/**
	 * 退出时间
	 */
	private Date exitTime;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
