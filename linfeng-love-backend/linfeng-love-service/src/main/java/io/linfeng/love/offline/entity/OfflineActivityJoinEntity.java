package io.linfeng.love.offline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 线下活动参与表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 09:00:52
 */
@Data
@TableName("lf_offline_activity_join")
public class OfflineActivityJoinEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 客户id
	 */
	private Integer uid;
	/**
	 * 活动id
	 */
	private Integer activityId;
	/**
	 * 状态
	 */
	private Integer status;
	/**
	 * 支付订单号
	 */
	private String orderNo;
	/**
	 * 退款订单号
	 */
	private String refundNo;
	/**
	 * 费用
	 */
	private BigDecimal amount;
	/**
	 * 加入时间
	 */
	private Date joinTime;
	/**
	 * 退出提交时间
	 */
	private Date exitSubmitTime;
	/**
	 * 退出时间
	 */
	private Date exitTime;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
