package io.linfeng.love.config.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.entity.ConfigPartnerEntity;

import java.util.Map;

/**
 * 搭子类型配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-10 08:53:55
 */
public interface ConfigPartnerService extends IService<ConfigPartnerEntity> {

    PageObject queryPage(Map<String, Object> params);
}

