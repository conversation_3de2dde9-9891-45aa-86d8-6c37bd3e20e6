package io.linfeng.love.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.love.chat.dao.ChatFriendDao;
import io.linfeng.love.chat.dto.response.ChatFriendResponseDTO;
import io.linfeng.love.chat.entity.ChatFriendEntity;
import io.linfeng.love.chat.enums.FriendStatus;
import io.linfeng.love.chat.service.ChatFriendService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("chatFriendService")
public class ChatFriendServiceImpl extends ServiceImpl<ChatFriendDao, ChatFriendEntity> implements ChatFriendService {

    @Override
    public List<ChatFriendResponseDTO> getFriendList(Integer uid) {
        return this.baseMapper.getFriendList(uid);
    }

    @Override
    public List<Integer> getExistFriendUidList(Integer uid) {
        return this.baseMapper.getExistFriendUidList(uid);
    }

    @Override
    public Integer checkFriendStatus(Integer uid, Integer friendUid) {
        LambdaQueryWrapper<ChatFriendEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatFriendEntity::getUid, uid);
        wrapper.eq(ChatFriendEntity::getFriendUid, friendUid);
        ChatFriendEntity chatFriend = this.getOne(wrapper);
        if(chatFriend == null){
            return FriendStatus.NO.getValue();
        }

        return chatFriend.getStatus();
    }
}