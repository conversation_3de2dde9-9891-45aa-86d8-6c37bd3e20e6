package io.linfeng.love.offline.service.impl;

import org.springframework.stereotype.Service;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.offline.dao.OfflineNoticeDao;
import io.linfeng.love.offline.entity.OfflineNoticeEntity;
import io.linfeng.love.offline.service.OfflineNoticeService;


@Service("offlineNoticeService")
public class OfflineNoticeServiceImpl extends ServiceImpl<OfflineNoticeDao, OfflineNoticeEntity> implements OfflineNoticeService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<OfflineNoticeEntity> page = this.page(
                new Page<>(pageNum, pageSize)
        );

        return new PageObject(page);
    }

}