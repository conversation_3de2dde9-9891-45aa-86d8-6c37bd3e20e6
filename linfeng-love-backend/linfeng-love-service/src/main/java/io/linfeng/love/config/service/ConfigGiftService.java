package io.linfeng.love.config.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.dto.response.ConfigGiftGroupResponseDTO;
import io.linfeng.love.config.entity.ConfigGiftEntity;

import java.util.List;
import java.util.Map;

/**
 * 礼物配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-28 09:54:32
 */
public interface ConfigGiftService extends IService<ConfigGiftEntity> {

    PageObject queryPage(Map<String, Object> params);

    List<ConfigGiftGroupResponseDTO> getGiftGroupList();
}

