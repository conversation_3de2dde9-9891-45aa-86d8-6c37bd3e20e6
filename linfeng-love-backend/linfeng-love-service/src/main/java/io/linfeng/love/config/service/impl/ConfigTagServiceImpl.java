package io.linfeng.love.config.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;
import io.linfeng.common.utils.RedisUtil;
import io.linfeng.love.config.dao.ConfigTagDao;
import io.linfeng.love.config.dto.response.ConfigTagGroupResponseDTO;
import io.linfeng.love.config.entity.ConfigTagEntity;
import io.linfeng.love.config.service.ConfigTagService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service("configTagService")
public class ConfigTagServiceImpl extends ServiceImpl<ConfigTagDao, ConfigTagEntity> implements ConfigTagService {

    private static final String CACHE_TAG_PREFIX = "config:tag:";

    private final RedisUtil redisUtils;

    public ConfigTagServiceImpl(RedisUtil redisUtils) {
        this.redisUtils = redisUtils;
    }

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        LambdaQueryWrapper<ConfigTagEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(ConfigTagEntity::getSort);
        Integer groupId = Integer.parseInt((String)params.get("groupId"));
        wrapper.eq(ConfigTagEntity::getGroupId, groupId);
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<ConfigTagEntity> page = this.page(
                new Page<>(pageNum, pageSize),
                wrapper
        );

        return new PageObject(page);
    }

    @Override
    public List<ConfigTagGroupResponseDTO> getTagGroupList() {
        List<ConfigTagGroupResponseDTO> tagGroupList = redisUtils.getList(CACHE_TAG_PREFIX, ConfigTagGroupResponseDTO.class);
        if (tagGroupList != null) {
            return tagGroupList;
        }
        tagGroupList = this.baseMapper.getTagGroupList();
        redisUtils.set(CACHE_TAG_PREFIX, tagGroupList, 7200);
        return tagGroupList;
    }

}