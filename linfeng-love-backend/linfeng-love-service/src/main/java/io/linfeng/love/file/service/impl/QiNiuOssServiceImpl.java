package io.linfeng.love.file.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.qiniu.common.Zone;
import com.qiniu.http.Response;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.Auth;
import io.linfeng.common.exception.LinfengException;
import io.linfeng.common.utils.Constant;
import io.linfeng.common.utils.StringUtil;
import io.linfeng.love.config.service.ConfigSystemService;
import io.linfeng.love.file.service.QiNiuOssService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 七牛云存储
 *
 */
@Slf4j
@Component
public class QiNiuOssServiceImpl implements QiNiuOssService {

    private String directory;

    private String domain;

    private String token;

    private Long tokenExpiredTime;

    private final ConfigSystemService configSystemService;

    private UploadManager uploadManager;

    public QiNiuOssServiceImpl(ConfigSystemService configSystemService){
        this.configSystemService = configSystemService;
        uploadManager = new UploadManager(new Configuration(Zone.autoZone()));
    }

    @Override
    public String upload(byte[] data, String suffix) {
        checkToken();
        String path = generatePath(suffix);
        try {
            Response res = uploadManager.put(data, path, token);
            if (!res.isOK()) {
                log.error("上传七牛云失败：", res.error);
                throw new LinfengException("图片上传失败");
            }
        } catch (Exception e) {
            throw new LinfengException("上传七牛云失败：", e);
        }
        return domain + "/" + path;
    }

    private String generatePath(String suffix){
        //生成uuid
        String uuid = IdUtil.fastSimpleUUID();

        //文件路径
        String path = directory + "/"
                + DateUtil.format(new Date(), "yyyyMMdd")
                + "/" + uuid
                + suffix;
        return path;
    }

    private void checkToken(){
        //token过期时间默认3600秒，在过期5分钟前刷新token，如果是在集群环境下需要存在数据库或者redis当中
        if(!StringUtil.isEmpty(token) && (tokenExpiredTime - 5 * 60 * 100) > System.currentTimeMillis()){
            return;
        }
        String accessKey = configSystemService.getValue(Constant.QI_NIU_ACCESS_KEY);
        String secretKey = configSystemService.getValue(Constant.QI_NIU_SECRET_KEY);
        String bucketName = configSystemService.getValue(Constant.QI_NIU_BUCKET_NAME);
        domain = configSystemService.getValue(Constant.QI_NIU_DOMAIN);
        directory = configSystemService.getValue(Constant.QI_NIU_DIRECTORY);
        token = Auth.create(accessKey, secretKey).uploadToken(bucketName);
        tokenExpiredTime = System.currentTimeMillis() + 3600 * 100;
    }
}
