package io.linfeng.love.chat.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class ChatFriendResponseDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 朋友oid
	 */
	private String oid;

	/**
	 * 好友头像
	 */
	private String avatar;

	/**
	 * 朋友用户名
	 */
	private String userName;
	/**
	 * 生日
	 */
	private String birthday;

	/**
	 * 居住城市
	 */
	private String livingCity;

	/**
	 * 单位名称
	 */
	private Integer job;

	/**
	 * 状态
	 */
	private Integer status;
	/**
	 * 在线状态
	 */
	private Integer onlineStatus;
	/**
	 * 最近离线时间
	 */
	private Date lastOfflineTime;

}
