package io.linfeng.love.petal.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 花瓣解锁嘉宾表
 * 
 * <AUTHOR>
 * @date 2023-09-14 16:02:23
 */
@Data
@TableName("lf_petal_unlock")
public class PetalUnlockEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 用户id
	 */
	private Integer uid;
	/**
	 * 嘉宾用户id
	 */
	private Integer guestUid;
	/**
	 * 花瓣数量
	 */
	private Integer petalAmount;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
