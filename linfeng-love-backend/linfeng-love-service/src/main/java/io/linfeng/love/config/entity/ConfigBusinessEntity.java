
package io.linfeng.love.config.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 业务配置信息
 *
 */
@Data
@TableName("lf_config_business")
public class ConfigBusinessEntity {

	public static final String CACHE_CONFIG_BUSINESS_PREFIX = "config:business:";

	@TableId
	private Long id;

	@NotBlank(message="参数名不能为空")
	private String paramKey;

	@NotBlank(message="参数值不能为空")
	private String paramValue;

	private String remark;

}
