package io.linfeng.love.bottle.dao;

import io.linfeng.love.bottle.dto.response.BottleSessionResponseDTO;
import io.linfeng.love.bottle.entity.BottleSessionEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 漂流瓶会话表
 * 
 * <AUTHOR>
 * @date 2023-12-12 16:35:50
 */
@Mapper
public interface BottleSessionDao extends BaseMapper<BottleSessionEntity> {

    List<BottleSessionResponseDTO> getAllSessionList(@Param("uid")Integer uid);

    BottleSessionResponseDTO getBottleSession(@Param("uid")Integer uid, @Param("bottleId")Integer bottleId);
	
}
