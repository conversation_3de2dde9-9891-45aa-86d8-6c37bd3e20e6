package io.linfeng.love.chat.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 好友表
 * 
 * <AUTHOR>
 * @date 2023-11-03 09:34:37
 */
@Data
@TableName("lf_chat_friend")
public class ChatFriendEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId
	private Integer id;
	/**
	 * 本人uid
	 */
	private Integer uid;
	/**
	 * 好友uid
	 */
	private Integer friendUid;
	/**
	 * 状态
	 */
	private Integer status;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
