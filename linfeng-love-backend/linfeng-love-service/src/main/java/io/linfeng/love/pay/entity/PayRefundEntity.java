package io.linfeng.love.pay.entity;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.linfeng.common.utils.DateUtil;
import io.linfeng.love.pay.enums.RefundStatus;
import lombok.Data;

/**
 * 退款信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 09:05:00
 */
@Data
@TableName("lf_pay_refund")
public class PayRefundEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 支付订单号
	 */
	private String orderNo;
	/**
	 * 用户id
	 */
	private Integer uid;
	/**
	 * 退款订单号
	 */
	private String refundNo;
	/**
	 * 退款金额
	 */
	private BigDecimal amount;
	/**
	 * 订单金额
	 */
	private BigDecimal total;
	/**
	 * 退款币种，默认CNY
	 */
	private String currency;
	/**
	 * 退款原因（商户填写）
	 */
	private String reason;
	/**
	 * 退款备注（客户填写）
	 */
	private String remark;
	/**
	 * 状态
	 */
	private Integer status;
	/**
	 * 外部退款订单号
	 */
	private String outRefundNo;
	/**
	 * 提交时间
	 */
	private Date submitTime;
	/**
	 * 结果时间
	 */
	private Date resultTime;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

	public PayRefundEntity(){
		setRefundNo("T" + IdUtil.createSnowflake(0L, 0).nextIdStr());
		setCreateTime(DateUtil.nowDateTime());
		setStatus(RefundStatus.APPLYING.getValue());
		setSubmitTime(DateUtil.nowDateTime());
		setCreateTime(DateUtil.nowDateTime());
	}

}
