package io.linfeng.love.config.dao;

import io.linfeng.love.config.dto.response.ConfigGiftGroupResponseDTO;
import io.linfeng.love.config.entity.ConfigGiftEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 礼物配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-28 09:54:32
 */
@Mapper
public interface ConfigGiftDao extends BaseMapper<ConfigGiftEntity> {

    List<ConfigGiftGroupResponseDTO> getGiftGroupList();
}
