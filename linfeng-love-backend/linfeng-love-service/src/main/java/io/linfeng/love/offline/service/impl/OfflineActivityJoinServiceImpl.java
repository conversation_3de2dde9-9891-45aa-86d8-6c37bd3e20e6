package io.linfeng.love.offline.service.impl;

import io.linfeng.love.offline.dto.response.OfflineActivityJoinResponseDTO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.offline.dao.OfflineActivityJoinDao;
import io.linfeng.love.offline.entity.OfflineActivityJoinEntity;
import io.linfeng.love.offline.service.OfflineActivityJoinService;


@Service("offlineActivityJoinService")
public class OfflineActivityJoinServiceImpl extends ServiceImpl<OfflineActivityJoinDao, OfflineActivityJoinEntity> implements OfflineActivityJoinService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));
        Integer activityId = Integer.parseInt((String)params.get("activityId"));;
        IPage<OfflineActivityJoinResponseDTO> page = this.baseMapper.getPage(
                new Page<>(pageNum, pageSize), activityId
        );
        return new PageObject(page);
    }

    @Override
    public List<String> getJoinAvatarList(Integer activityId) {
        return baseMapper.getJoinAvatarList(activityId);
    }

}