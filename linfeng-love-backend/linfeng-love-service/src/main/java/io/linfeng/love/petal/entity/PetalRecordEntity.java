package io.linfeng.love.petal.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 花瓣交易记录表
 * 
 * <AUTHOR>
 * @date 2023-09-15 12:15:53
 */
@Data
@TableName("lf_petal_record")
public class PetalRecordEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 客户id
	 */
	private Integer uid;
	/**
	 * 类型
	 */
	private Integer type;
	/**
	 * 子类型
	 */
	private Integer subType;
	/**
	 * 数量（永久）
	 */
	private Integer amountForever;
	/**
	 * 数量（限时）
	 */
	private Integer amountLimit;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
