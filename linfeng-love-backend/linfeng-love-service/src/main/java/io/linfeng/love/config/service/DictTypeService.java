package io.linfeng.love.config.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.entity.DictTypeEntity;

import java.util.Map;

/**
 * 数据字典类型表
 *
 * <AUTHOR>
 * @date 2023-11-13 10:09:12
 */
public interface DictTypeService extends IService<DictTypeEntity> {

    PageObject queryPage(Map<String, Object> params);

}

