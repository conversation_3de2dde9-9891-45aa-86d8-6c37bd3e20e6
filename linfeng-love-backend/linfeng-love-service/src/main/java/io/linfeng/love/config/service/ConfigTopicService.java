package io.linfeng.love.config.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.entity.ConfigTopicEntity;

import java.util.Map;

/**
 * 话题表
 *
 * <AUTHOR>
 * @date 2023-10-26 15:21:39
 */
public interface ConfigTopicService extends IService<ConfigTopicEntity> {

    PageObject queryPage(Map<String, Object> params);

}

