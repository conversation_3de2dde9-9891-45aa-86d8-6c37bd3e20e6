package io.linfeng.love.config.service.impl;

import org.springframework.stereotype.Service;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.config.dao.ConfigExchangePetalDao;
import io.linfeng.love.config.entity.ConfigExchangePetalEntity;
import io.linfeng.love.config.service.ConfigExchangePetalService;


@Service("configExchangePetalService")
public class ConfigExchangePetalServiceImpl extends ServiceImpl<ConfigExchangePetalDao, ConfigExchangePetalEntity> implements ConfigExchangePetalService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<ConfigExchangePetalEntity> page = this.page(
                new Page<>(pageNum, pageSize)
        );

        return new PageObject(page);
    }

}