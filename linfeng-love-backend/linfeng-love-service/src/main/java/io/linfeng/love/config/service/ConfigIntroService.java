package io.linfeng.love.config.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.dto.response.ConfigIntroResponseDTO;
import io.linfeng.love.config.entity.ConfigIntroEntity;

import java.util.List;
import java.util.Map;

/**
 * 用户介绍信息项配置表
 *
 * <AUTHOR>
 * @date 2023-08-29 16:06:38
 */
public interface ConfigIntroService extends IService<ConfigIntroEntity> {

    PageObject queryPage(Map<String, Object> params);

    List<ConfigIntroResponseDTO> getConfigList();
}

