package io.linfeng.love.offline.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class MemberResponseDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 成员oid
	 */
	private String oid;
	/**
	 * 手机号
	 */
	private String mobile;
	/**
	 * 头像
	 */
	private String avatar;
	/**
	 * 昵称
	 */
	private String userName;
}
