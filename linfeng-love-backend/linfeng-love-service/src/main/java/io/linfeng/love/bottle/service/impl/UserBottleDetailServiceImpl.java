package io.linfeng.love.bottle.service.impl;

import io.linfeng.love.bottle.dto.response.BottleResponseDTO;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import io.linfeng.love.bottle.dao.UserBottleDetailDao;
import io.linfeng.love.bottle.entity.UserBottleDetailEntity;
import io.linfeng.love.bottle.service.UserBottleDetailService;


@Service("userBottleDetailService")
public class UserBottleDetailServiceImpl extends ServiceImpl<UserBottleDetailDao, UserBottleDetailEntity> implements UserBottleDetailService {

    @Override
    public BottleResponseDTO pickBottle(Integer uid) {
        return this.baseMapper.pickBottle(uid);
    }
}