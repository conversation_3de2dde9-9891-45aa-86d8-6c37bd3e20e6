package io.linfeng.love.moment.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.linfeng.love.config.dto.response.TopicResponseDTO;
import io.linfeng.love.moment.entity.MomentTopicEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 帖子收藏表(废弃)
 * 
 * <AUTHOR>
 * @date 2023-10-09 16:48:23
 */
@Mapper
public interface MomentTopicDao extends BaseMapper<MomentTopicEntity> {

    List<TopicResponseDTO> getTopicResponseDTOList();

    List<TopicResponseDTO> getHotTopicResponseDTOList();
}
