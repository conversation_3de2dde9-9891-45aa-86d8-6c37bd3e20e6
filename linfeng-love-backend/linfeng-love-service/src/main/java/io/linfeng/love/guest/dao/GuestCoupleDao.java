package io.linfeng.love.guest.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.linfeng.love.guest.dto.response.GuestSimpleResponseDTO;
import io.linfeng.love.guest.entity.GuestCoupleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 嘉宾配对信息表
 * 
 * <AUTHOR>
 * @date 2023-10-17 09:02:11
 */
@Mapper
public interface GuestCoupleDao extends BaseMapper<GuestCoupleEntity> {

    List<GuestSimpleResponseDTO> getLikeList(@Param("param") Map<String, Object> params);

    List<GuestSimpleResponseDTO> getLikeMeList(@Param("param") Map<String, Object> params);

    List<Integer> getExistGuestUidList(@Param("uid")Integer uid);

    List<Map<String, Integer>> statisticsGuestCoupleList();
	
}
