package io.linfeng.love.config.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 签到配置表
 * 
 * <AUTHOR>
 * @date 2023-11-23 15:52:16
 */
@Data
@TableName("lf_config_sign")
public class ConfigSignEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 连续签到天数
	 */
	private Integer day;
	/**
	 * 奖励数量
	 */
	private Integer prize;
	/**
	 * 额外奖励数量
	 */
	private Integer extraPrize;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
