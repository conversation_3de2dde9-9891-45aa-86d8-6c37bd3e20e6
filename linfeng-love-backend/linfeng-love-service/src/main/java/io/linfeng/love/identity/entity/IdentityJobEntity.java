package io.linfeng.love.identity.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 工作认证表
 * 
 * <AUTHOR>
 * @date 2023-09-13 21:41:07
 */
@Data
@TableName("lf_identity_job")
public class IdentityJobEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 用户id
	 */
	private Integer uid;
	/**
	 * 认证方式
	 */
	private String type;
	/**
	 * 认证图片
	 */
	private String image;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
