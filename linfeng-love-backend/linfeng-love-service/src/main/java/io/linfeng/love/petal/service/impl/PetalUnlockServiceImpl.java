package io.linfeng.love.petal.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.love.petal.dao.PetalUnlockDao;
import io.linfeng.love.petal.entity.PetalUnlockEntity;
import io.linfeng.love.petal.service.PetalUnlockService;
import org.springframework.stereotype.Service;

@Service("petalUnlockService")
public class PetalUnlockServiceImpl extends ServiceImpl<PetalUnlockDao, PetalUnlockEntity> implements PetalUnlockService {


}