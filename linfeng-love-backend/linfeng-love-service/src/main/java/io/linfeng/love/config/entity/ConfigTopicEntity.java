package io.linfeng.love.config.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 话题表
 * 
 * <AUTHOR>
 * @date 2023-10-26 15:21:39
 */
@Data
@TableName("lf_config_topic")
public class ConfigTopicEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 话题id
	 */
	@TableId
	private Integer id;
	/**
	 * 标题
	 */
	private String title;
	/**
	 * 描述
	 */
	private String introduce;
	/**
	 * 背景图片
	 */
	private String backgroundImage;
	/**
	 * 排序
	 */
	private Integer sort;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
