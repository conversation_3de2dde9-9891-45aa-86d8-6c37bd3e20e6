package io.linfeng.love.guest.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.linfeng.love.guest.dto.response.GuestSimpleResponseDTO;
import io.linfeng.love.guest.entity.GuestQualityEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 推荐精选信息表
 * 
 * <AUTHOR>
 * @date 2023-09-01 15:20:59
 */
@Mapper
public interface GuestQualityDao extends BaseMapper<GuestQualityEntity> {

    /**
     * 获取精选嘉宾列表
     * @param map 筛选条件
     * @return精选嘉宾列表
     */
    List<GuestSimpleResponseDTO> selectQualityGuestList(@Param("m") Map<String, Object> map);

}
