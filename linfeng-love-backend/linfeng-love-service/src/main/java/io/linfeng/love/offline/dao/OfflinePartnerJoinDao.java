package io.linfeng.love.offline.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.love.offline.dto.response.MemberResponseDTO;
import io.linfeng.love.offline.dto.response.OfflineActivityJoinResponseDTO;
import io.linfeng.love.offline.dto.response.PartnerJoinResponseDTO;
import io.linfeng.love.offline.entity.OfflinePartnerJoinEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 搭子活动参与表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-13 16:27:17
 */
@Mapper
public interface OfflinePartnerJoinDao extends BaseMapper<OfflinePartnerJoinEntity> {

    List<String> getJoinAvatarList(@Param("partnerId") Integer partnerId);

    List<MemberResponseDTO> getMemberList(@Param("uid")Integer uid, @Param("partnerId")Integer partnerId);
}
