package io.linfeng.love.pay.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.pay.entity.PayRefundEntity;

import java.util.Map;

/**
 * 退款信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 09:05:00
 */
public interface PayRefundService extends IService<PayRefundEntity> {

    PageObject queryPage(Map<String, Object> params);
}

