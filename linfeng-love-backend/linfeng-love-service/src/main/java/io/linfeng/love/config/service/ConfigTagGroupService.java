package io.linfeng.love.config.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.entity.ConfigTagGroupEntity;

import java.util.Map;

/**
 * 标签分组配置表
 *
 * <AUTHOR>
 * @date 2023-10-18 14:54:02
 */
public interface ConfigTagGroupService extends IService<ConfigTagGroupEntity> {

    PageObject queryPage(Map<String, Object> params);
}

