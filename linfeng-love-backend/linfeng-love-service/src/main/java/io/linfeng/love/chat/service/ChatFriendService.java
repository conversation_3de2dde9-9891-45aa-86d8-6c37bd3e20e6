package io.linfeng.love.chat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.love.chat.dto.response.ChatFriendResponseDTO;
import io.linfeng.love.chat.entity.ChatFriendEntity;

import java.util.List;

/**
 * 好友表
 *
 * <AUTHOR>
 * @date 2023-11-03 09:34:37
 */
public interface ChatFriendService extends IService<ChatFriendEntity> {

    /**
     * 获取好友列表
     * @param uid 用户id
     * @return 好友列表
     */
    List<ChatFriendResponseDTO> getFriendList(Integer uid);

    /**
     * 获取好友id列表
     * @param uid 用户id
     * @return好友id列表
     */
    List<Integer> getExistFriendUidList(Integer uid);

    /**
     * 校验朋友状态
     * @param uid 用户id
     * @param friendUid 朋友uid
     * @return 是否有效朋友关系（无记录，黑名单返回false）
     */
    Integer checkFriendStatus(Integer uid, Integer friendUid);
}

