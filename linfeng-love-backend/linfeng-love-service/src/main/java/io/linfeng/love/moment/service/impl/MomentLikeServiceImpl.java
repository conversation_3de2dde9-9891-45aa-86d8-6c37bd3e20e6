package io.linfeng.love.moment.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.love.moment.dao.MomentLikeDao;
import io.linfeng.love.moment.dto.response.MomentLikeResponseDTO;
import io.linfeng.love.moment.entity.MomentLikeEntity;
import io.linfeng.love.moment.service.MomentLikeService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("momentLikeService")
public class MomentLikeServiceImpl extends ServiceImpl<MomentLikeDao, MomentLikeEntity> implements MomentLikeService {


    @Override
    public List<MomentLikeResponseDTO> getMomentLikeList(Integer momentId) {
        return this.baseMapper.selectMomentLikeList(momentId);
    }
}