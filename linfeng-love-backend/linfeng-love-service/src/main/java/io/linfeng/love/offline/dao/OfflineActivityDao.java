package io.linfeng.love.offline.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.linfeng.love.offline.dto.response.ActivityDetailResponseDTO;
import io.linfeng.love.offline.dto.response.ActivitySimpleResponseDTO;
import io.linfeng.love.offline.entity.OfflineActivityEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 线下活动表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 09:00:53
 */
@Mapper
public interface OfflineActivityDao extends BaseMapper<OfflineActivityEntity> {

    IPage<ActivitySimpleResponseDTO> getActivityList(IPage<ActivitySimpleResponseDTO> page, @Param("uid")Integer uid, @Param("city")String city, @Param("queryType")Integer queryType);

    IPage<ActivitySimpleResponseDTO> getMyActivityList(IPage<ActivitySimpleResponseDTO> page, @Param("uid")Integer uid);

    ActivityDetailResponseDTO getActivityDetail(@Param("id")Integer id, @Param("uid")Integer uid);
	
}
