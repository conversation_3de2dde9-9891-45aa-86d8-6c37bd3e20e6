package io.linfeng.love.offline.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.offline.entity.OfflineActivityJoinEntity;

import java.util.List;
import java.util.Map;

/**
 * 线下活动参与表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 09:00:52
 */
public interface OfflineActivityJoinService extends IService<OfflineActivityJoinEntity> {

    PageObject queryPage(Map<String, Object> params);

    List<String> getJoinAvatarList(Integer activityId);
}

