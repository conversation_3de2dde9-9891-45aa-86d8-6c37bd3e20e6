package io.linfeng.love.config.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.utils.RedisUtil;
import io.linfeng.common.utils.StringUtil;
import io.linfeng.love.config.dao.ConfigBusinessDao;
import io.linfeng.love.config.entity.ConfigBusinessEntity;
import io.linfeng.love.config.service.ConfigBusinessService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service("configBusinessService")
public class ConfigBusinessServiceImpl extends ServiceImpl<ConfigBusinessDao, ConfigBusinessEntity> implements ConfigBusinessService {

	private static final String VIOLATION_WORD_KEY = "violationWord";

	private final RedisUtil redisUtil;

	public ConfigBusinessServiceImpl(RedisUtil redisUtil) {
		this.redisUtil = redisUtil;
	}

	@Override
	public String getValue(String key) {
		String paramValue = redisUtil.get(ConfigBusinessEntity.CACHE_CONFIG_BUSINESS_PREFIX + key, String.class);
		if (!StringUtil.isEmpty(paramValue)) {
			return paramValue;
		}
		//重新获取用户信息
		LambdaQueryWrapper<ConfigBusinessEntity> lambdaQueryWrapper = Wrappers.lambdaQuery();
		lambdaQueryWrapper.eq(ConfigBusinessEntity::getParamKey, key);
		ConfigBusinessEntity configSystemEntity = getOne(lambdaQueryWrapper);
		redisUtil.set(ConfigBusinessEntity.CACHE_CONFIG_BUSINESS_PREFIX + key, configSystemEntity.getParamValue(), 7200);
		return configSystemEntity.getParamValue();
	}

	@Override
	public void updateAndRefreshCache(List<ConfigBusinessEntity> configSystemEntityList) {
		configSystemEntityList.forEach(configSystemEntity -> {

			//花瓣过期时间做下特殊处理


			LambdaQueryWrapper<ConfigBusinessEntity> wrapper = Wrappers.lambdaQuery();
			wrapper.eq(ConfigBusinessEntity::getParamKey, configSystemEntity.getParamKey());
			update(configSystemEntity, wrapper);
			redisUtil.set(ConfigBusinessEntity.CACHE_CONFIG_BUSINESS_PREFIX + configSystemEntity.getParamKey(), configSystemEntity.getParamValue(), 7200);
		});
	}

	@Override
	public boolean hasViolationWord(String word) {
		String violationWordString = getValue(VIOLATION_WORD_KEY);
		if(StringUtil.isEmpty(violationWordString)){
			return false;
		}
		List<String> violationWordList =  Arrays.asList(violationWordString.split("，"));
		for(String violationWord : violationWordList){
			if(word.contains(violationWord)){
				return true;
			}
		}
		return false;
	}
}
