package io.linfeng.love.chat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.love.chat.dto.response.ChatHelloResponseDTO;
import io.linfeng.love.guest.entity.ChatHelloEntity;

import java.util.List;

/**
 * 花瓣解锁嘉宾表
 *
 * <AUTHOR>
 * @date 2023-10-19 14:13:12
 */
public interface ChatHelloService extends IService<ChatHelloEntity> {

    /**
     * 获取打招呼申请列表
     * @param uid 用户id
     * @return 打招呼申请列表
     */
    List<ChatHelloResponseDTO> getChatHelloList(Integer uid);

}

