package io.linfeng.love.config.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.dao.ConfigTagGroupDao;
import io.linfeng.love.config.entity.ConfigTagGroupEntity;
import io.linfeng.love.config.service.ConfigTagGroupService;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service("configTagGroupService")
public class ConfigTagGroupServiceImpl extends ServiceImpl<ConfigTagGroupDao, ConfigTagGroupEntity> implements ConfigTagGroupService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        LambdaQueryWrapper<ConfigTagGroupEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(ConfigTagGroupEntity::getSort);
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<ConfigTagGroupEntity> page = this.page(
                new Page<>(pageNum, pageSize),
                wrapper
        );
        return new PageObject(page);
    }

}