package io.linfeng.love.petal.service.impl;


import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;
import io.linfeng.common.utils.StringUtil;
import io.linfeng.love.petal.service.PetalRecordService;
import io.linfeng.love.petal.dao.PetalRecordDao;
import io.linfeng.love.petal.entity.PetalRecordEntity;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service("petalRecordService")
public class PetalRecordServiceImpl extends ServiceImpl<PetalRecordDao, PetalRecordEntity> implements PetalRecordService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        QueryWrapper<PetalRecordEntity> queryWrapper = new QueryWrapper<>();

        String uid = (String) params.get("uid");
        String type = (String) params.get("type");
        String subType = (String) params.get("subType");

        if (!StringUtil.isEmpty(uid)) {
            if (NumberUtil.isInteger(uid)) {
                queryWrapper.lambda().eq(PetalRecordEntity::getUid, uid);
            }
        }

        if (!StringUtil.isEmpty(type)) {
            queryWrapper.lambda().eq(PetalRecordEntity::getType, Integer.parseInt(type));
        }
        if (!StringUtil.isEmpty(subType)) {
            queryWrapper.lambda().eq(PetalRecordEntity::getSubType, Integer.parseInt(subType));
        }
        queryWrapper.lambda().orderByDesc(PetalRecordEntity::getCreateTime);
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));

        IPage<PetalRecordEntity> page = this.page(
                new Page<>(pageNum, pageSize),
                queryWrapper
        );

        return new PageObject(page);
    }

}