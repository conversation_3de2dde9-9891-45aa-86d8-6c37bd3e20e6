package io.linfeng.love.moment.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.utils.RedisUtil;
import io.linfeng.love.config.dto.response.TopicResponseDTO;
import io.linfeng.love.moment.dao.MomentTopicDao;
import io.linfeng.love.moment.entity.MomentTopicEntity;
import io.linfeng.love.moment.service.MomentTopicService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("momentTopicService")
public class MomentTopicServiceImpl extends ServiceImpl<MomentTopicDao, MomentTopicEntity> implements MomentTopicService {

    private static final String CACHE_TOPIC_PREFIX = "config:topic:all";

    private static final String CACHE_TOPIC_HOT_PREFIX = "config:topic:hot";

    private final RedisUtil redisUtils;

    public MomentTopicServiceImpl(RedisUtil redisUtils) {
        this.redisUtils = redisUtils;
    }

    @Override
    public List<TopicResponseDTO> getTopicList() {
        List<TopicResponseDTO> topicList = redisUtils.getList(CACHE_TOPIC_PREFIX, TopicResponseDTO.class);
        if (topicList != null) {
            return topicList;
        }
        topicList = this.baseMapper.getTopicResponseDTOList();
        //10分钟从数据库读一次
        redisUtils.set(CACHE_TOPIC_PREFIX, topicList, 600);
        return topicList;
    }

    @Override
    public List<TopicResponseDTO> getHotTopicList() {
        List<TopicResponseDTO> topicList = redisUtils.getList(CACHE_TOPIC_HOT_PREFIX, TopicResponseDTO.class);
        if (topicList != null) {
            return topicList;
        }
        topicList = this.baseMapper.getHotTopicResponseDTOList();
        //10分钟从数据库读一次
        redisUtils.set(CACHE_TOPIC_HOT_PREFIX, topicList, 600);
        return topicList;
    }

}