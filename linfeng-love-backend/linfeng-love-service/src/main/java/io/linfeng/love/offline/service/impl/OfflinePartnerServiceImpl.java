package io.linfeng.love.offline.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.linfeng.common.utils.StringUtil;
import io.linfeng.love.offline.dto.response.PartnerDetailResponseDTO;
import io.linfeng.love.offline.dto.response.PartnerSimpleResponseDTO;
import io.linfeng.love.offline.entity.OfflineActivityEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.offline.dao.OfflinePartnerDao;
import io.linfeng.love.offline.entity.OfflinePartnerEntity;
import io.linfeng.love.offline.service.OfflinePartnerService;


@Service("offlinePartnerService")
public class OfflinePartnerServiceImpl extends ServiceImpl<OfflinePartnerDao, OfflinePartnerEntity> implements OfflinePartnerService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<PartnerDetailResponseDTO> dtoPage = new Page<>(pageNum, pageSize);
        Integer status = null;
        if(!StringUtil.isEmpty(params.get("status"))){
            status = Integer.parseInt((String)params.get("status"));
        }
        IPage<PartnerDetailResponseDTO> page = this.baseMapper.getPartnerDetailList(dtoPage, status);

        return new PageObject(page);
    }

    @Override
    public IPage<PartnerSimpleResponseDTO> getPartnerList(IPage<PartnerSimpleResponseDTO> dtoPage, Integer typeId, String city, Integer queryType) {
        return this.baseMapper.getPartnerList(dtoPage, typeId, city, queryType);
    }

    @Override
    public IPage<PartnerSimpleResponseDTO> getMyPartnerList(IPage<PartnerSimpleResponseDTO> dtoPage, Integer uid, Integer queryType) {
        return this.baseMapper.getMyPartnerList(dtoPage, uid, queryType);
    }

    @Override
    public PartnerDetailResponseDTO getPartnerDetail(Integer id, Integer uid) {
        return this.baseMapper.getPartnerDetail(id, uid);
    }

}