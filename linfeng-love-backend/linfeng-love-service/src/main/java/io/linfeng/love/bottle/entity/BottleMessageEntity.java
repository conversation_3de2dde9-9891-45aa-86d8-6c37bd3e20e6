package io.linfeng.love.bottle.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 漂流瓶消息表
 * 
 * <AUTHOR>
 * @date 2023-12-12 16:35:50
 */
@Data
@TableName("lf_bottle_message")
public class BottleMessageEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId
	private Integer id;
	/**
	 * 漂流瓶id
	 */
	private Integer bottleId;
	/**
	 * 消息id
	 */
	private String messageId;
	/**
	 * 发送者id
	 */
	private Integer senderUid;
	/**
	 * 接收者id
	 */
	private Integer receiverUid;
	/**
	 * 类型
	 */
	private Integer messageType;
	/**
	 * 内容
	 */
	private String content;
	/**
	 * 持续时间（语音、视频）
	 */
	private Integer duration;
	/**
	 * 发送时间
	 */
	private Date sendTime;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
