package io.linfeng.love.petal.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 花瓣充值选项
 * 
 * <AUTHOR>
 * @date 2023-09-28 14:26:17
 */
@Data
@TableName("lf_petal_option")
public class PetalOptionEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId
	private Integer id;
	/**
	 * 有效天数
	 */
	private Integer amount;
	/**
	 * 价格
	 */
	private BigDecimal price;
	/**
	 * 描述
	 */
	private String remark;
	/**
	 * 排序
	 */
	private Integer sort;

}
