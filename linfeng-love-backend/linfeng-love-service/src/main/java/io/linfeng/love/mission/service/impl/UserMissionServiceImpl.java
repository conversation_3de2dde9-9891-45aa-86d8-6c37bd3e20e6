package io.linfeng.love.mission.service.impl;

import io.linfeng.common.utils.DateUtil;
import io.linfeng.common.utils.RedisUtil;
import io.linfeng.love.mission.dto.response.MissionResponseDTO;
import io.linfeng.love.mission.enums.MissionActivity;
import org.springframework.stereotype.Service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import io.linfeng.love.mission.dao.UserMissionDao;
import io.linfeng.love.mission.entity.UserMissionEntity;
import io.linfeng.love.mission.service.UserMissionService;


@Service("userMissionService")
public class UserMissionServiceImpl extends ServiceImpl<UserMissionDao, UserMissionEntity> implements UserMissionService {

    private static final String CACHE_MISSION_PREFIX = "mission:";

    private final RedisUtil redisUtils;

    public UserMissionServiceImpl(RedisUtil redisUtils) {
        this.redisUtils = redisUtils;
    }

    @Override
    public List<MissionResponseDTO> getMissionList(Integer uid, String activityCode) {
        String cache = CACHE_MISSION_PREFIX + activityCode + ":" + uid;
        List<MissionResponseDTO> missionResponseDTOList = redisUtils.getList(cache, MissionResponseDTO.class);
        if (missionResponseDTOList != null && missionResponseDTOList.size() != 0) {
            return missionResponseDTOList;
        }

        missionResponseDTOList = this.baseMapper.getMissionList(uid, activityCode);
        if(activityCode.equals(MissionActivity.NEW_USER.getValue())){
            redisUtils.set(cache, missionResponseDTOList, 7200);
        } else if (activityCode.equals(MissionActivity.BASE_DAY.getValue())) {
            redisUtils.set(cache, missionResponseDTOList, DateUtil.getToNight());
        }
        return missionResponseDTOList;
    }

    @Override
    public void receiveActivityMissionList(Integer uid, String activityCode) {
        this.baseMapper.receiveActivityMissionList(uid, activityCode);
    }

    @Override
    public void updateAndRefreshCache(UserMissionEntity userMission, String activityCode) {
        this.baseMapper.updateById(userMission);
        redisUtils.delete( CACHE_MISSION_PREFIX + activityCode + ":" + userMission.getUid());
    }
}