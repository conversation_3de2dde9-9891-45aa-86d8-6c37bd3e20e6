package io.linfeng.love.guest.dto.response;

import lombok.Data;

import java.io.Serializable;


@Data
public class GuestSimpleResponseDTO implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 嘉宾uid
	 */
	private Integer uid;
	/**
	 * 嘉宾oid
	 */
	private String oid;
	/**
	 * 用户名
	 */
	private String userName;
	/**
	 * 头像
	 */
	private String avatar;
	/**
	 * 性别
	 */
	private Integer gender;
	/**
	 * 年龄
	 */
	private Integer age;
	/**
	 * 工作
	 */
	private Integer job;
	/**
	 * 学历
	 */
	private Integer education;
	/**
	 * 学校
	 */
	private String school;
	/**
	 * 年薪
	 */
	private Integer salary;
	/**
	 * 身高
	 */
	private Integer stature;
	/**
	 * 锁定标识
	 */
	private Integer lockFlag;
	/**
	 * 操作状态
	 */
	private Integer operatorStatus;
	/**
	 * 配对状态
	 */
	private Integer coupleStatus;
	/**
	 * 朋友状态
	 */
	private Integer friendStatus;
	/**
	 * 在线状态
	 */
	private Integer onlineStatus;
	/**
	 * 是否为会员 0普通用户 1会员
	 */
	private Integer vip;
	/**
	 * 实名状态
	 */
	private Integer realNameStatus;
	/**
	 * 距离
	 */
	private Double distance;
	/**
	 * 个性签名
	 */
	private String signature;

}
