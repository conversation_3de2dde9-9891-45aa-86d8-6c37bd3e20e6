package io.linfeng.love.offline.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;
import io.linfeng.common.utils.StringUtil;
import io.linfeng.love.chat.dto.response.ChatMessageResponseDTO;
import io.linfeng.love.moment.entity.MomentEntity;
import io.linfeng.love.offline.dao.OfflineActivityDao;
import io.linfeng.love.offline.dto.response.ActivityDetailResponseDTO;
import io.linfeng.love.offline.dto.response.ActivitySimpleResponseDTO;
import io.linfeng.love.offline.entity.OfflineActivityEntity;
import io.linfeng.love.offline.service.OfflineActivityService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service("offlineActivityService")
public class OfflineActivityServiceImpl extends ServiceImpl<OfflineActivityDao, OfflineActivityEntity> implements OfflineActivityService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        LambdaQueryWrapper<OfflineActivityEntity> wrapper = new LambdaQueryWrapper<>();
        if(!StringUtil.isEmpty(params.get("id"))){
            wrapper.eq(OfflineActivityEntity::getId, params.get("id"));
        }
        if(!StringUtil.isEmpty(params.get("activityName"))){
            wrapper.like(OfflineActivityEntity::getActivityName, params.get("activityName"));
        }
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<OfflineActivityEntity> page = this.page(
                new Page<>(pageNum, pageSize),
                wrapper
        );

        return new PageObject(page);
    }

    @Override
    public IPage<ActivitySimpleResponseDTO> getActivityList(IPage<ActivitySimpleResponseDTO> page, Integer uid, String city, Integer queryType) {
        return this.baseMapper.getActivityList(page, uid, city, queryType);
    }

    @Override
    public IPage<ActivitySimpleResponseDTO> getMyActivityList(IPage<ActivitySimpleResponseDTO> page, Integer uid) {
        return this.baseMapper.getMyActivityList(page, uid);
    }

    @Override
    public ActivityDetailResponseDTO getActivityDetail(Integer id, Integer uid) {
        return this.baseMapper.getActivityDetail(id, uid);
    }

}