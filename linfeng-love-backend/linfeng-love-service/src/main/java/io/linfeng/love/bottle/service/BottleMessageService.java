package io.linfeng.love.bottle.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.love.bottle.dto.response.BottleMessageResponseDTO;
import io.linfeng.love.bottle.entity.BottleMessageEntity;

/**
 * 漂流瓶消息表
 *
 * <AUTHOR>
 * @date 2023-12-12 16:35:50
 */
public interface BottleMessageService extends IService<BottleMessageEntity> {

    IPage<BottleMessageResponseDTO> selectMessagePage(Page<BottleMessageResponseDTO> page, Integer bottleId);
}

