package io.linfeng.love.config.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.entity.ConfigSubscribeEntity;

import java.util.Map;

/**
 * 消息订阅配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 08:53:05
 */
public interface ConfigSubscribeService extends IService<ConfigSubscribeEntity> {

    PageObject queryPage(Map<String, Object> params);
}

