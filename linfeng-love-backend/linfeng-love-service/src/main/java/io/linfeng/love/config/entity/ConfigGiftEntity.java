package io.linfeng.love.config.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 礼物配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-28 10:16:13
 */
@Data
@TableName("lf_config_gift")
public class ConfigGiftEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 分类id
	 */
	private Integer groupId;
	/**
	 * 名称
	 */
	private String name;
	/**
	 * 静态地址
	 */
	private String staticUrl;
	/**
	 * 动态地址
	 */
	private String dynamicUrl;
	/**
	 * 价格（花瓣数量）
	 */
	private Integer petal;
	/**
	 * 是否vip
	 */
	private Integer vip;
	/**
	 * 是否支持免费花瓣
	 */
	private Integer supportLimit;
	/**
	 * 状态
	 */
	private Integer status;
	/**
	 * 排序
	 */
	private Integer sort;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
