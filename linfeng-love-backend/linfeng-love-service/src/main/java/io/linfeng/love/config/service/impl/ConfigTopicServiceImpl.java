package io.linfeng.love.config.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.dao.ConfigTopicDao;
import io.linfeng.love.config.entity.ConfigTopicEntity;
import io.linfeng.love.config.service.ConfigTopicService;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("configTopicService")
public class ConfigTopicServiceImpl extends ServiceImpl<ConfigTopicDao, ConfigTopicEntity> implements ConfigTopicService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        LambdaQueryWrapper<ConfigTopicEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(ConfigTopicEntity::getSort);
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<ConfigTopicEntity> page = this.page(
                new Page<>(pageNum, pageSize),
                wrapper
        );

        return new PageObject(page);
    }

}