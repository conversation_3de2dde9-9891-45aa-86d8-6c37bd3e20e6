package io.linfeng.love.chat.dto.response;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;


@Data
public class ChatMessageResponseDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 会话id
	 */
	private String sessionId;

	/**
	 * 消息id
	 */
	private String messageId;
	/**
	 * 发送者oid
	 */
	private String senderOid;

	/**
	 * 接收者oid
	 */
	private String receiverOid;
	/**
	 * 发送时间
	 */
	private String sendTime;
	/**
	 * 内容
	 */
	private String content;
	/**
	 * 类型
	 */
	private Integer messageType;
	/**
	 * 持续时间
	 */
	private Integer duration;

}
