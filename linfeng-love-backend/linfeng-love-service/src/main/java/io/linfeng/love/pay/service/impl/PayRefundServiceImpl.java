package io.linfeng.love.pay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.linfeng.common.utils.StringUtil;
import io.linfeng.love.moment.entity.MomentEntity;
import io.linfeng.love.pay.entity.PayOrderEntity;
import org.springframework.stereotype.Service;
import java.util.Map;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.api.PageObject;

import io.linfeng.love.pay.dao.PayRefundDao;
import io.linfeng.love.pay.entity.PayRefundEntity;
import io.linfeng.love.pay.service.PayRefundService;


@Service("payRefundService")
public class PayRefundServiceImpl extends ServiceImpl<PayRefundDao, PayRefundEntity> implements PayRefundService {

    @Override
    public PageObject queryPage(Map<String, Object> params) {
        QueryWrapper<PayRefundEntity> queryWrapper = new QueryWrapper<>();
        if(!StringUtil.isEmpty(params.get("uid"))){
            queryWrapper.lambda().eq(PayRefundEntity::getUid, params.get("uid"));
        }
        if (!StringUtil.isEmpty(params.get("status"))) {
            queryWrapper.lambda().eq(PayRefundEntity::getStatus, Integer.parseInt((String) params.get("status")));
        }
        queryWrapper.lambda().orderByDesc(PayRefundEntity::getCreateTime);
        long pageSize = Long.parseLong((String)params.get("limit"));
        long pageNum = Long.parseLong((String)params.get("page"));;
        IPage<PayRefundEntity> page = this.page(
                new Page<>(pageNum, pageSize),
                queryWrapper
        );

        return new PageObject(page);
    }

}