package io.linfeng.love.config.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.linfeng.common.api.PageObject;
import io.linfeng.love.config.entity.ConfigGiftGroupEntity;

import java.util.Map;

/**
 * 礼物分组配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-28 09:54:32
 */
public interface ConfigGiftGroupService extends IService<ConfigGiftGroupEntity> {

    PageObject queryPage(Map<String, Object> params);
}

