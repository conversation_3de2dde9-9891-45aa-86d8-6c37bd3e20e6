package io.linfeng.love.identity.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 学历认证表
 * 
 * <AUTHOR>
 * @date 2023-09-13 17:05:23
 */
@Data
@TableName("lf_identity_education")
public class IdentityEducationEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 客户id
	 */
	private Integer uid;
	/**
	 * 身份证原件照片
	 */
	private String idCardImage;
	/**
	 * 学历证明照片1
	 */
	private String educationImageOne;
	/**
	 * 学历证明照片2
	 */
	private String educationImageTwo;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
