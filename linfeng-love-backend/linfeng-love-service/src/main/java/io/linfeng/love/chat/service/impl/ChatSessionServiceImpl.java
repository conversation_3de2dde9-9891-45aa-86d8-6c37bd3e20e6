package io.linfeng.love.chat.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.linfeng.common.utils.DateUtil;
import io.linfeng.love.chat.dao.ChatSessionDao;
import io.linfeng.love.chat.dto.response.ChatSessionResponseDTO;
import io.linfeng.love.chat.entity.ChatSessionEntity;
import io.linfeng.love.chat.service.ChatSessionService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("chatSessionService")
public class ChatSessionServiceImpl extends ServiceImpl<ChatSessionDao, ChatSessionEntity> implements ChatSessionService {

    @Override
    public List<ChatSessionResponseDTO> getAllSessionList(Integer uid) {

        List<ChatSessionResponseDTO> chatSessionList = this.baseMapper.getAllSessionList(uid);

        return chatSessionList;
    }

    @Override
    public ChatSessionResponseDTO getSessionResponseDTO(Integer uid, Integer friendUid) {
        ChatSessionResponseDTO chatSessionResponseDTO = baseMapper.selectSession(uid, friendUid);
        return chatSessionResponseDTO;
    }

    @Override
    public void saveAndRefreshCache(ChatSessionEntity chatSession) {
        chatSession.setCreateTime(DateUtil.nowDateTime());
        this.save(chatSession);

    }

    @Override
    public void updateAndRefreshCache(ChatSessionEntity chatSession) {
        LambdaQueryWrapper<ChatSessionEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatSessionEntity::getSessionId, chatSession.getSessionId());
        wrapper.eq(ChatSessionEntity::getUid, chatSession.getUid());
        chatSession.setUpdateTime(DateUtil.nowDateTime());
        this.update(chatSession, wrapper);

    }

    @Override
    public void deleteAndRefreshCache(Integer uid, String sessionId) {
        LambdaQueryWrapper<ChatSessionEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatSessionEntity::getSessionId, sessionId);
        wrapper.eq(ChatSessionEntity::getUid, uid);
        this.remove(wrapper);

    }

}