package io.linfeng.love.chat.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 好友表
 * 
 * <AUTHOR>
 * @date 2023-09-19 09:57:37
 */
@Data
@TableName("lf_chat_session")
public class ChatSessionEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@TableId
	private Integer id;
	/**
	 * 会话id
	 */
	private String sessionId;
	/**
	 * 本人uid
	 */
	private Integer uid;
	/**
	 * 好友uid
	 */
	private Integer friendUid;
	/**
	 * 最后一条消息uid
	 */
	private Integer lastMessageUid;
	/**
	 * 最后一条消息类型
	 */
	private Integer lastMessageType;
	/**
	 * 最后一条消息内容
	 */
	private String lastMessageContent;
	/**
	 * 未读消息数量
	 */
	private Integer unRead;
	/**
	 * 最后一条消息时间
	 */
	private Date lastMessageTime;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

	public ChatSessionEntity(){
	}

}
