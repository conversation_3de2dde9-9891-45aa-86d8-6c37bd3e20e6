package io.linfeng.love.config.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 标签配置表
 * 
 * <AUTHOR>
 * @date 2023-10-18 14:54:01
 */
@Data
@TableName("lf_config_tag")
public class ConfigTagEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 分组编码
	 */
	private Integer groupId;
	/**
	 * 标签名称
	 */
	private String name;
	/**
	 * 序号
	 */
	private Integer sort;
	/**
	 * 图标
	 */
	private String icon;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

}
