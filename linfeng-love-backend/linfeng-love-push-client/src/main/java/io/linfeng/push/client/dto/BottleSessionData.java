package io.linfeng.push.client.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BottleSessionData {

    /**
     * 会话id
     */
    private Integer bottleId;
    /**
     * 好友oid
     */
    private String friendOid;
    /**
     * 好友头像
     */
    private String friendAvatar;
    /**
     * 好友昵称
     */
    private String friendUserName;

    /**
     * 最后一条消息Oid
     */
    private String lastMessageOid;
    /**
     * 最后一条消息类型
     */
    private Integer lastMessageType;
    /**
     * 最后一条消息内容
     */
    private String lastMessageContent;
    /**
     * 最后一条消息时间
     */
    private String lastMessageTime;
    /**
     * 未读消息数量
     */
    private Integer unRead;
    /**
     * 锁定标识
     */
    private Integer lockFlag;

}
