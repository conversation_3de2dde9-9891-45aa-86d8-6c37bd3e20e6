package io.linfeng.push.client.enums;

public enum InteractMessageDataType {
    /**
     * 关注
     */
    CONCERN("concern"),
    /**
     * 评论
     */
    COMMENT("comment"),
    /**
     * 点赞
     */
    LIKE("like"),
    /**
     * 配对
     */
    COUPLE("couple"),
    /**
     * 心动
     */
    HEART("heart"),
    /**
     * 打招呼
     */
    HELLO("hello"),
    /**
     * 打招呼
     */
    HELLO_REPLY("helloReply");

    private String value;

    InteractMessageDataType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
