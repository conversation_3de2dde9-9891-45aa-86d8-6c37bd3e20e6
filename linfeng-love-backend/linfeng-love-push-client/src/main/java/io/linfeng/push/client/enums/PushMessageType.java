package io.linfeng.push.client.enums;

public enum PushMessageType {
    /**
     * 聊天消息
     */
    CHAT("chat"),
    /**
     * 会话消息
     */
    SESSION("session"),
    /**
     * 打招呼消息
     */
    HELLO("hello"),
    /**
     * 互动消息：点赞/评论/关注
     */
    INTERACT("interact"),
    /**
     * 官方消息：花瓣清理/审核结果/举报结果
     */
    OFFICIAL("official"),
    /**
     * 系统消息：版本更新
     */
    SYSTEM("system"),
    /**
     * 漂流瓶消息
     */
    BOTTLE("bottle"),
    /**
     * 漂流瓶会话消息
     */
    BOTTLE_SESSION("bottleSession"),
    /**
     * 漂流瓶聊天消息
     */
    BOTTLE_CHAT("bottleChat");

    private String value;

    PushMessageType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
