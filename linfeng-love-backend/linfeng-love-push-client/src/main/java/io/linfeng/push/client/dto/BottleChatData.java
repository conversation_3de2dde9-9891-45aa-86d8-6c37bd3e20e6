package io.linfeng.push.client.dto;

import lombok.Data;

@Data
public class BottleChatData {

    /**
     * 消息id
     */
    private String messageId;
    /**
     * 会话id
     */
    private Integer bottleId;
    /**
     * 发送者oid
     */
    private String senderOid;
    /**
     * 接收者oid
     */
    private String receiverOid;
    /**
     * 类型
     */
    private Integer messageType;
    /**
     * 内容
     */
    private String content;
    /**
     * 发送时间
     */
    private String sendTime;

    /**
     * 持续时间
     */
    private Integer duration;

}
