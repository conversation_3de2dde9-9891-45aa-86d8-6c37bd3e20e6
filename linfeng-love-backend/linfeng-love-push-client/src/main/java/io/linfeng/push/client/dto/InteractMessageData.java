package io.linfeng.push.client.dto;

import lombok.Data;

@Data
public class InteractMessageData {
    /**
     * 用户oid
     */
    private String guestOid;
    /**
     * 用户名
     */
    private String guestUserName;
    /**
     * 用户头像
     */
    private String guestAvatar;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String content;
    /**
     * 类型
     */
    private String dataType;
    /**
     * 跳转图片
     */
    private String linkImage;
    /**
     * 跳转链接
     */
    private String linkUrl;
    /**
     * 发送时间
     */
    private String sendTime;

}
