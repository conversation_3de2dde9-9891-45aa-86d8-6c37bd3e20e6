package io.linfeng.push.client.producer;

import com.google.gson.Gson;
import io.linfeng.push.client.dto.PushMessage;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

@Component
public class MessageProducer {

    private final RabbitTemplate rabbitTemplate;

    private static Gson gson = new Gson();

    public MessageProducer(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
    }

    public void sendMessage(String exchange, String routingKey, PushMessage message){

        rabbitTemplate.convertAndSend(exchange,routingKey,gson.toJson(message));

    }
}
