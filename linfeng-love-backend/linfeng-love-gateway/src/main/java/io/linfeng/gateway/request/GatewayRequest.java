package io.linfeng.gateway.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description="网关请求对象")
public class GatewayRequest {

    @ApiModelProperty(value = "密文")
    private String ciphertext;

    @ApiModelProperty(value = "请求地址")
    private String url;

    @ApiModelProperty(value = "请求方式")
    private String method;

}
