/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.gateway.controller;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import io.linfeng.common.api.Result;
import io.linfeng.common.utils.AESUtil;
import io.linfeng.common.utils.StringUtil;
import io.linfeng.gateway.request.GatewayRequest;
import io.linfeng.gateway.response.GatewayResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;


/**
 * 移动端网关
 *
 * <AUTHOR> CX
 * @date 2023-09-28 14:26:17
 */
@RestController
@RequestMapping("app/gateway")
@Api(tags = "移动端网关Api")
@Slf4j
public class GatewayController {

    @Value("${app.url:http://127.0.0.1:8080/app/}")
    private String urlPrefix;

    /**
     * get请求
     * @return 结果
     */
    @ApiOperation(value = "post请求")
    @GetMapping("get")
    public Result<GatewayResponse> get(GatewayRequest request, HttpServletRequest httpServletRequest) throws Exception {

        GatewayResponse response = new GatewayResponse();

        String requestData = AESUtil.desEncrypt(request.getCiphertext());
        Map<String, Object> requestParams = JSONObject.parseObject(requestData, Map.class);
        HttpRequest httpRequest = HttpUtil.createGet(urlPrefix + request.getUrl());
        if(!StringUtil.isEmpty(httpServletRequest.getHeader("token"))){
            httpRequest.header("token", httpServletRequest.getHeader("token"));
        }
        HttpResponse httpResponse = httpRequest.form(requestParams).execute();
        String responseStr = httpResponse.body();
        response.setCiphertext(AESUtil.encrypt(responseStr));

        return new Result<GatewayResponse>().ok(response);
    }

    /**
     * post请求
     * @return 结果
     */
    @ApiOperation(value = "post请求")
    @PostMapping("post")
    public Result<GatewayResponse> post(@RequestBody GatewayRequest request, HttpServletRequest httpServletRequest) throws Exception {

        GatewayResponse response = new GatewayResponse();

        String requestData = AESUtil.desEncrypt(request.getCiphertext());

        HttpRequest httpRequest = HttpUtil.createPost(urlPrefix + request.getUrl());
        if(!StringUtil.isEmpty(httpServletRequest.getHeader("token"))){
            httpRequest.header("token", httpServletRequest.getHeader("token"));
        }
        httpRequest.header("content-type", "application/json");
        HttpResponse httpResponse = httpRequest.body(requestData).execute();
        String responseStr = httpResponse.body();
        response.setCiphertext(AESUtil.encrypt(responseStr));

        return new Result<GatewayResponse>().ok(response);
    }
}
