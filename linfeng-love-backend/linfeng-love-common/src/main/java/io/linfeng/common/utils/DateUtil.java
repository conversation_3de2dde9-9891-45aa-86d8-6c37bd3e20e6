package io.linfeng.common.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * Date工具类
 * <AUTHOR>
 * @date 2023/1/20 13:38
 */
public final class DateUtil {

    public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT_UTC = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    public static final String DATE_FORMAT_DATE = "yyyy-MM-dd";
    public static final String DATE_FORMAT_YEAR = "yyyy";
    public static final String DATE_FORMAT_MONTH_DATE = "MM-dd";
    public static final String DATE_FORMAT_MONTH = "yyyy-MM";
    public static final String DATE_TIME_FORMAT_NUM = "yyyyMMddHHmmss";
    public static final String DATE_FORMAT_NUM = "yyyyMMdd";
    public static final String DATE_FORMAT_START = "yyyy-MM-dd 00:00:00";
    public static final String DATE_FORMAT_END = "yyyy-MM-dd 23:59:59";
    public static final String DATE_FORMAT_MONTH_START = "yyyy-MM-01 00:00:00";
    public static final String DATE_FORMAT_YEAR_START = "yyyy-01-01 00:00:00";
    public static final String DATE_FORMAT_YEAR_END = "yyyy-12-31 23:59:59";
    public static final String DATE_FORMAT_HHMMSS = "HH:mm:ss";
    public static final String DATE_FORMAT_START_PEREND = "00:00:00";
    public static final String DATE_FORMAT_END_PEREND = "23:59:59";
    public static final String DATE_FORMAT_HHMM = "yyyy-MM-dd HH:mm";

    private DateUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 获取当前日期,指定格式
     * 描述:<描述函数实现的功能>.
     *
     * @return
     */
    public static Date nowDateTime() {
        return strToDate(nowDateTimeStr(), DATE_FORMAT);
    }

    /**
     * 获取当前日期,指定格式
     * 描述:<描述函数实现的功能>.
     *
     * @return
     */
    public static String nowDateTimeStr() {
        return nowDate(DATE_FORMAT);
    }

    /**
     * 获取当前日期,指定格式
     * 描述:<描述函数实现的功能>.
     *
     * @return
     */
    public static String nowDate(String DATE_FORMAT) {
        SimpleDateFormat dft = new SimpleDateFormat(DATE_FORMAT);
        return dft.format(new Date());
    }

    /**
     * 获取当前日期,指定格式
     * 描述:<描述函数实现的功能>.
     *
     * @return
     */
    public static String nowDateTime(String DATE_FORMAT) {
        SimpleDateFormat dft = new SimpleDateFormat(DATE_FORMAT);
        return dft.format(new Date());
    }

    /**
     * 获取当前时间戳（秒级）
     * @return
     */
    public static Long getTime() {
        return (System.currentTimeMillis()/1000L);
    }

    /**
     * convert a date to string in a specifies fromat.
     *
     * @param date
     * @param DATE_FORMAT
     * @return
     */
    public static String dateToStr(Date date, String DATE_FORMAT) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat myFormat = new SimpleDateFormat(DATE_FORMAT);
        return myFormat.format(date);
    }

    /**
     * parse a String to Date in a specifies fromat.
     *
     * @param dateStr
     * @param DATE_FORMAT
     * @return
     * @throws ParseException
     */
    public static Date strToDate(String dateStr, String DATE_FORMAT) {
        SimpleDateFormat myFormat = new SimpleDateFormat(DATE_FORMAT);
        try {
            return myFormat.parse(dateStr);
        } catch (ParseException e) {
            return null;
        }
    }


    /**
     * date add Second
     *
     * @param date
     * @param num
     * @return
     */
    public static Date addSecond(Date date, int num) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.SECOND, num);
        return calendar.getTime();
    }

    public static Date addDay(String newDate, int num) {
        SimpleDateFormat format = new SimpleDateFormat(DATE_FORMAT);
        try {
            Date currdate = format.parse(newDate);
            Calendar ca = Calendar.getInstance();
            ca.setTime(currdate);
            ca.add(Calendar.DATE, num);
            return strToDate(format.format(ca.getTime()), DATE_FORMAT);
        } catch (ParseException e) {
            return null;
        }
    }

    /** 获得今天零点*/
    public static Date getToDay() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        return c.getTime();
    }

    public static Date getToNight() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        return c.getTime();
    }

    /** 获得本周周一*/
    public static Date getWeekMonday() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_WEEK, 2);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        return c.getTime();
    }

    /** 获得本月第一天*/
    public static Date getMonthFirstDay() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        return c.getTime();
    }

    /** 获得本月第一天*/
    public static Date getYearFirstDay() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_YEAR, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        return c.getTime();
    }

    /**
     *  两个日期之前的相差秒数
     * @param starDate 开始日期
     * @param endDate 结束日期
     * @return 相差秒数
     */
    public static int secondsBetween(Date starDate,Date endDate){

        Calendar cal = Calendar.getInstance();

        cal.setTime(starDate);

        long time1 = cal.getTimeInMillis();

        cal.setTime(endDate);

        long time2 = cal.getTimeInMillis();

        long between_days=(time2-time1)/(1000);

        return Integer.parseInt(String.valueOf(between_days));

    }

    /**
     *  两个日期之前的相差天数
     * @param starDate 开始日期
     * @param endDate 结束日期
     * @return 相差天数
     */
    public static int daysBetween(Date starDate,Date endDate){

        Calendar cal = Calendar.getInstance();

        cal.setTime(starDate);

        long time1 = cal.getTimeInMillis();

        cal.setTime(endDate);

        long time2 = cal.getTimeInMillis();

        long between_days=(time2-time1)/(1000*3600*24);

        return Integer.parseInt(String.valueOf(between_days));

    }


    public static int getAge(String date) throws ParseException {
        int age = 0;
        Calendar birthday = Calendar.getInstance();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATE_FORMAT_DATE);
        birthday.setTime(simpleDateFormat.parse(date));
        Calendar now = Calendar.getInstance();

        int birthdayYear = birthday.get(Calendar.YEAR);
        int birthdayMonth = birthday.get(Calendar.MONTH);
        int birthdayDay = birthday.get(Calendar.DAY_OF_MONTH);
        int nowYear = now.get(Calendar.YEAR);
        int nowMonth = now.get(Calendar.MONTH);
        int nowDay = now.get(Calendar.DAY_OF_MONTH);

        age = nowYear - birthdayYear;

        if(nowMonth < birthdayMonth){
            age = age-1;
        }else if(nowMonth == birthdayMonth){
            if(nowDay < birthdayDay){
                age = age-1;
            }
        }

        return age;
    }

    public static String getConstellation(String date) throws ParseException {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATE_FORMAT_DATE);
        calendar.setTime(simpleDateFormat.parse(date));
        Integer month = calendar.get(Calendar.MONTH) + 1;
        Integer day = calendar.get(Calendar.DATE);
        Boolean isMatch = (month == 1 && day >= 10) || (month == 2 && day <= 18);
        if (isMatch) {
            return "水瓶座";
        }
        isMatch = month == 2 || (month == 3 && day <= 20);
        if (isMatch) {
            return "双鱼座";
        }
        isMatch = month == 3 || (month == 4 && day <= 19);
        if (isMatch) {
            return "白羊座";
        }
        isMatch = month == 4 || (month == 5 && day <= 20);
        if (isMatch) {
            return "金牛座";
        }
        isMatch = month == 5 || (month == 6 && day <= 21);
        if (isMatch) {
            return "双子座";
        }
        isMatch = month == 6 || (month == 7 && day <= 22);
        if (isMatch) {
            return "巨蟹座";
        }
        isMatch = month == 7 || (month == 8 && day <= 22);
        if (isMatch) {
            return "狮子座";
        }
        isMatch = month == 8 || (month == 9 && day <= 22);
        if (isMatch) {
            return "处女座";
        }
        isMatch = month == 9 || (month == 10 && day <= 23);
        if (isMatch) {
            return "天秤座";
        }
        isMatch = month == 10 || (month == 11 && day <= 22);
        if (isMatch) {
            return "天蝎座";
        }
        isMatch = month == 11 || (month == 12 && day <= 21);
        if (isMatch) {
            return "射手座";
        }
        isMatch = month == 12 || (month == 1 && day <= 20);
        if (isMatch) {
            return "摩羯座";
        }
        return "";
    }

}
