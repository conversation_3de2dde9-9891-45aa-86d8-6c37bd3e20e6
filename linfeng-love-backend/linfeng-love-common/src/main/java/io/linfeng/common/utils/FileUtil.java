package io.linfeng.common.utils;

import com.drew.imaging.ImageMetadataReader;
import com.drew.imaging.ImageProcessingException;
import com.drew.metadata.Metadata;
import com.drew.metadata.MetadataException;
import com.drew.metadata.exif.ExifIFD0Directory;
import org.apache.tomcat.jni.Directory;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2023/2/21 10:38
 */
public class FileUtil {

    public static boolean checkSize(long maxSize, long size) {
        // 单位 M
        int len = 1024 * 1024;
        if(size > (maxSize * len)){
            return false;
        }
        return true;
    }

    public static String urlToBase64(String url) throws IOException {
        URL imageUrl = new URL(url);
        BufferedInputStream bis = null;
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            bis = new BufferedInputStream(imageUrl.openStream());
            byte[] buffer = new byte[1024];

            int bytesRead;
            while ((bytesRead = bis.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }

            byte[] imageBytes = baos.toByteArray();
            Base64.Encoder encoder = Base64.getEncoder();
            String base64Image = encoder.encodeToString(imageBytes);

            return base64Image;
        } finally{
            if (bis != null) {
                bis.close();
            }
            if (baos != null) {
                baos.close();
            }
        }
    }

    public static byte[] compressedImage(MultipartFile file)  throws IOException{
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(file.getBytes()));
            if(image.getWidth() < 3000 && image.getHeight() < 3000){
                return file.getBytes();
            }
            //ios平台下上传文件会翻转90度，这边先旋转再压缩
            BufferedImage rotateImage = rotateImage(image, getRotationAngle(file.getInputStream()));
            int targetWidth = rotateImage.getWidth() / 2;
            int targetHeight = rotateImage.getHeight() / 2;

            BufferedImage compressedImage =new BufferedImage(targetWidth, targetHeight, image.getType());
            Graphics2D g2d = compressedImage.createGraphics();
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.drawImage(rotateImage, 0, 0, targetWidth, targetHeight, null);
            g2d.dispose();

            ImageIO.write(compressedImage, "jpg", baos);
            byte[] compressedImageData = baos.toByteArray();
            return compressedImageData;
        } finally{
            if (baos != null) {
                baos.close();
            }
        }
    }

    public static boolean isImageSuffix(String suffix){
        String[] imageExtensions = new String[]{".jpg", ".jpeg", ".png", ".gif", ".bmp"};
        for (String ext : imageExtensions) {
            if (suffix.equals(ext)) {
                return true;
            }
        }
        return false;
    }

    public static int readImageOrientation(InputStream inputStream) throws ImageProcessingException, IOException, MetadataException {
        Metadata metadata = ImageMetadataReader.readMetadata(inputStream);
        ExifIFD0Directory exifIFD0Directory = metadata.getDirectory(ExifIFD0Directory.class);
        return exifIFD0Directory.getInt(ExifIFD0Directory.TAG_ORIENTATION);
    }

    public static int getRotationAngle(InputStream inputStream) {
        int orientation = 0;
        try {
            orientation = readImageOrientation(inputStream);
        } catch (ImageProcessingException | IOException | MetadataException e) {
            e.printStackTrace();
        }
        int rotationAngle = 0;
        if (orientation == 6) {
            rotationAngle = 90;
        } else if (orientation == 3) {
            rotationAngle = 180;
        } else if (orientation == 8) {
            rotationAngle = 270;
        }
        return rotationAngle;
    }

    // 旋转图片
    public static BufferedImage rotateImage(BufferedImage image, int rotationAngle) {
        int width = image.getWidth();
        int height = image.getHeight();
        if(rotationAngle / 90 % 2 == 1){
            width = image.getHeight();
            height = image.getWidth();
        }
        int type = image.getType();
        BufferedImage rotatedImage = new BufferedImage(width, height, type);

        Graphics2D graphics2D = rotatedImage.createGraphics();
        graphics2D.translate((width - image.getWidth()) / 2, (height - image.getHeight()) / 2);
        graphics2D.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        graphics2D.rotate(Math.toRadians(rotationAngle), image.getWidth() / 2, image.getHeight() / 2);
        graphics2D.drawImage(image, null, null);
        graphics2D.dispose();
        return rotatedImage;
    }

}
