/**
 * -----------------------------------
 * Copyright (c) 2021-2024
 *  All rights reserved, Designed By linfeng.tech , linfengtech.cn
 * 林风婚恋交友商业版本请务必保留此注释头信息
 * 商业版授权联系技术客服	 QQ:  973921677/3582996245
 * 严禁分享、盗用、转卖源码或非法牟利！
 * 版权所有 ，侵权必究！
 * -----------------------------------
 */
package io.linfeng.common.utils;

/**
 * 错误信息常量
 *
 */
public class ErrorMessage {

	/** 需要引导客户进行下一步操作的通用返回码 **/
	public static final int COMMON_GUIDE_CODE = 9;
	public static final String BUSINESS_PETAL_NOT_ENOUGH = "花瓣余额不足，请先充值";

	public static final String BUSINESS_PETAL_UNLOCK_REPEAT = "已经解锁该嘉宾，请勿重复解锁";

}
