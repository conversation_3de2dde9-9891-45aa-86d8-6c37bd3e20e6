<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>io.linfeng</groupId>
		<artifactId>linfeng-love-parent</artifactId>
		<version>1.0.0</version>
		<relativePath  />
	</parent>


	<groupId>io.linfeng</groupId>
	<artifactId>linfeng-love-common</artifactId>
	<name>linfeng-love-common</name>
	<version>1.0.0</version>
	<packaging>jar</packaging>
	
	<description></description>
	
	<dependencies>
		<dependency>
			<groupId>net.sf.dozer</groupId>
			<artifactId>dozer</artifactId>
			<version>5.5.1</version>
		</dependency>
		<dependency>
			<groupId>com.drewnoakes</groupId>
			<artifactId>metadata-extractor</artifactId>
			<version>2.7.2</version>
			<scope>compile</scope>
		</dependency>
	</dependencies>
	
</project>
